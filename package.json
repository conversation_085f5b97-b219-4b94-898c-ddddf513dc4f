{"name": "BuddyChipUtimate", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo -F web dev", "build": "turbo -F web build", "check-types": "turbo -F web check-types", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "check": "biome ci .", "dev:web": "turbo -F web dev", "db:push": "turbo -F web db:push", "db:studio": "turbo -F web db:studio", "db:generate": "turbo -F web db:generate", "db:migrate": "turbo -F web db:migrate"}, "devDependencies": {"@biomejs/biome": "^2.0.0", "turbo": "^2.5.4"}, "packageManager": "pnpm@10.12.1", "dependencies": {"import-in-the-middle": "^1.14.2", "require-in-the-middle": "^7.5.2"}}