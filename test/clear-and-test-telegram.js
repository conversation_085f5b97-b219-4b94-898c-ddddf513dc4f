#!/usr/bin/env node

/**
 * Clear Telegram webhook queue and send test message
 */

const https = require('https');

const BOT_TOKEN = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
const TEST_CHAT_ID = '904041730';

async function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function clearWebhookQueue() {
  console.log('🧹 Clearing webhook queue...');
  
  // Get webhook info first
  const webhookOptions = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/getWebhookInfo`,
    method: 'GET'
  };

  try {
    const webhookResult = await makeRequest(webhookOptions);
    console.log('📊 Current webhook info:', JSON.stringify(webhookResult.data, null, 2));

    // Clear the webhook queue by setting webhook again
    const clearOptions = {
      hostname: 'api.telegram.org',
      port: 443,
      path: `/bot${BOT_TOKEN}/setWebhook`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const clearData = {
      url: 'https://www.buddychip.app/api/telegram/webhook',
      drop_pending_updates: true  // This clears the queue
    };

    const clearResult = await makeRequest(clearOptions, clearData);
    console.log('✅ Webhook queue cleared:', clearResult.data);

  } catch (error) {
    console.error('❌ Error clearing webhook queue:', error);
  }
}

async function sendTestMessage() {
  console.log(`💬 Sending test message to chat ${TEST_CHAT_ID}...`);
  
  const messageOptions = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/sendMessage`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  const messageData = {
    chat_id: TEST_CHAT_ID,
    text: `🤖 *Telegram Bot Test Message*\n\nHello! This is a test message from BuddyChip AI.\n\n✅ Webhook is working correctly\n✅ Bot can send messages\n✅ Ready for use!\n\n*Time:* ${new Date().toLocaleString()}`,
    parse_mode: 'Markdown'
  };

  try {
    const result = await makeRequest(messageOptions, messageData);
    
    if (result.status === 200 && result.data.ok) {
      console.log('✅ Test message sent successfully!');
      console.log('📨 Message details:', {
        messageId: result.data.result.message_id,
        chatId: result.data.result.chat.id,
        text: result.data.result.text.substring(0, 50) + '...'
      });
    } else {
      console.log('❌ Failed to send test message:', result.data);
    }
  } catch (error) {
    console.error('❌ Error sending test message:', error);
  }
}

async function getUpdates() {
  console.log('📥 Getting pending updates...');
  
  const updatesOptions = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/getUpdates`,
    method: 'GET'
  };

  try {
    const result = await makeRequest(updatesOptions);
    
    if (result.status === 200 && result.data.ok) {
      console.log(`📊 Found ${result.data.result.length} pending updates`);
      
      if (result.data.result.length > 0) {
        console.log('📋 Recent updates:');
        result.data.result.slice(-3).forEach((update, index) => {
          console.log(`  ${index + 1}. Update ${update.update_id}: ${update.message?.text || 'No text'} from ${update.message?.from?.username || 'Unknown'}`);
        });
      }
    } else {
      console.log('❌ Failed to get updates:', result.data);
    }
  } catch (error) {
    console.error('❌ Error getting updates:', error);
  }
}

async function main() {
  console.log('🚀 Starting Telegram bot cleanup and test...\n');
  
  // Step 1: Get pending updates
  await getUpdates();
  console.log();
  
  // Step 2: Clear webhook queue
  await clearWebhookQueue();
  console.log();
  
  // Step 3: Send test message
  await sendTestMessage();
  console.log();
  
  console.log('🎉 Telegram bot cleanup and test completed!');
  console.log('💡 Try sending a message to your bot now to test the webhook.');
}

if (require.main === module) {
  main().catch(console.error);
}