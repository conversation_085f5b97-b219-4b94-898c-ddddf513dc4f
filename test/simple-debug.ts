/**
 * Simple debug to see what accounts exist
 */

import { PrismaClient } from "./apps/web/prisma/generated/index.js";

const prisma = new PrismaClient();

async function simpleDebug() {
  console.log("🔍 Simple account debug...\n");

  try {
    const userId = "user_2zBfQTmoTeEAmQW9hNiJ7qIHwXQ";
    
    // Check what the UI query returns (same as accounts router)
    const accounts = await prisma.monitoredAccount.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        _count: {
          select: {
            mentions: true,
          },
        },
      },
    });

    console.log(`📊 UI Query Result: Found ${accounts.length} accounts`);
    for (const account of accounts) {
      console.log(`  - ${account.twitterHandle} (ID: ${account.id})`);
      console.log(`    Active: ${account.isActive}`);
      console.log(`    Created: ${account.createdAt}`);
      console.log(`    Mentions: ${account._count.mentions}`);
      console.log('');
    }

    // Check what the count query returns (same as user service)
    const activeCount = await prisma.monitoredAccount.count({
      where: {
        userId: userId,
        isActive: true,
      },
    });

    console.log(`📈 Count Query Result: ${activeCount} active accounts`);

    console.log('\n🎯 The Issue:');
    console.log(`   UI shows: ${accounts.length} accounts`);
    console.log(`   Count check: ${activeCount} active accounts`);
    console.log(`   User limit: 1`);
    console.log(`   Problem: ${activeCount >= 1 ? 'User has reached limit' : 'User should be able to add accounts'}`);

  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    await prisma.$disconnect();
  }
}

simpleDebug();