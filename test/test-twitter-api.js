/**
 * Test script to check Twitter API user tweets functionality
 * Run with: bun run test/test-twitter-api.js
 */

const { twitterClient } = require('../apps/web/src/lib/twitter-client');

async function testTwitterAPI() {
  try {
    console.log('🧪 Testing Twitter API user tweets functionality...');
    
    // Test with a few different accounts
    const testAccounts = [
      'reisnertobias',
      'elonmusk',
      'vitalikbuterin',
      'DefiIgna<PERSON>'
    ];
    
    for (const username of testAccounts) {
      console.log(`\n📋 Testing @${username}...`);
      
      try {
        const result = await twitterClient.getUserLastTweets(username, {
          limit: 5,
          includeReplies: false,
          includeRetweets: true,
        });
        
        console.log(`✅ API Response for @${username}:`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Message: ${result.message}`);
        console.log(`   Tweets count: ${result.tweets.length}`);
        console.log(`   Has next page: ${result.has_next_page}`);
        
        if (result.tweets.length > 0) {
          console.log(`   Sample tweet: ${result.tweets[0].text.substring(0, 100)}...`);
          console.log(`   Tweet author: @${result.tweets[0].author.userName}`);
          console.log(`   Tweet ID: ${result.tweets[0].id}`);
        }
        
        // Test with replies included
        const resultWithReplies = await twitterClient.getUserLastTweets(username, {
          limit: 5,
          includeReplies: true,
          includeRetweets: true,
        });
        
        console.log(`   With replies: ${resultWithReplies.tweets.length} tweets`);
        
      } catch (error) {
        console.error(`❌ Error testing @${username}:`, error.message);
      }
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testTwitterAPI();
