/**
 * Telegram Integration Tests
 * 
 * Tests for Telegram bot functionality and integration
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TelegramBenjiAgent, getTelegramBenjiForUser } from '../apps/web/src/lib/telegram-benji-agent';
import { 
  generateTelegramLinkCode, 
  parseTelegramLinkCode, 
  linkTelegramAccount,
  validateTelegramWebhook
} from '../apps/web/src/lib/telegram-auth';
import { 
  extractTwitterUrl, 
  containsTwitterUrl, 
  parseCommand,
  validateTelegramConfig
} from '../apps/web/src/lib/telegram-utils';

// Mock dependencies
vi.mock('../apps/web/src/lib/db-utils', () => ({
  prisma: {
    user: {
      findUnique: vi.fn()
    },
    telegramUser: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn()
    }
  }
}));

describe('Telegram Integration', () => {
  describe('TelegramBenjiAgent', () => {
    it('should create agent with Telegram-specific config', () => {
      const agent = new TelegramBenjiAgent({
        userId: 'test-user',
        telegramUserId: 'tg-user-123',
        telegramChatId: '*********',
        maxMessageLength: 4000
      });

      expect(agent).toBeInstanceOf(TelegramBenjiAgent);
    });

    it('should format long messages for Telegram', () => {
      const agent = new TelegramBenjiAgent();
      const longText = 'A'.repeat(5000);
      
      const messages = agent.formatForTelegram(longText);
      
      expect(messages.length).toBeGreaterThan(1);
      expect(messages.every(msg => msg.length <= 4000)).toBe(true);
    });

    it('should detect Twitter URLs', () => {
      expect(TelegramBenjiAgent.isTwitterUrl('https://twitter.com/user/status/123')).toBe(true);
      expect(TelegramBenjiAgent.isTwitterUrl('https://x.com/user/status/123')).toBe(true);
      expect(TelegramBenjiAgent.isTwitterUrl('https://example.com')).toBe(false);
    });

    it('should extract Twitter URLs', () => {
      const url = 'https://twitter.com/user/status/123';
      const message = `Check this out: ${url}`;
      
      expect(TelegramBenjiAgent.extractTwitterUrl(message)).toBe(url);
    });

    it('should detect commands', () => {
      expect(TelegramBenjiAgent.isCommand('/start')).toBe(true);
      expect(TelegramBenjiAgent.isCommand('/help with args')).toBe(true);
      expect(TelegramBenjiAgent.isCommand('not a command')).toBe(false);
    });

    it('should parse commands', () => {
      const result = TelegramBenjiAgent.parseCommand('/help arg1 arg2');
      
      expect(result).toEqual({
        command: 'help',
        args: ['arg1', 'arg2']
      });
    });
  });

  describe('Telegram Authentication', () => {
    it('should generate valid link codes', () => {
      const chatId = '*********';
      const linkCode = generateTelegramLinkCode(chatId);
      
      expect(linkCode.code).toMatch(/^TG_*********_\d+_[a-f0-9]+$/);
      expect(linkCode.telegramChatId).toBe(chatId);
      expect(linkCode.expiresAt).toBeInstanceOf(Date);
      expect(linkCode.isUsed).toBe(false);
    });

    it('should parse link codes correctly', () => {
      const chatId = '*********';
      const timestamp = Date.now();
      const code = `TG_${chatId}_${timestamp}_abc123`;
      
      const parsed = parseTelegramLinkCode(code);
      
      expect(parsed).toEqual({
        telegramChatId: chatId,
        timestamp
      });
    });

    it('should reject invalid link codes', () => {
      expect(parseTelegramLinkCode('invalid')).toBeNull();
      expect(parseTelegramLinkCode('TG_invalid_format')).toBeNull();
      expect(parseTelegramLinkCode('WRONG_123_456_789')).toBeNull();
    });

    it('should validate webhook signatures', () => {
      const body = '{"test": "data"}';
      const token = 'test-token';
      
      // This is a simplified test - in reality you'd use actual HMAC
      const isValid = validateTelegramWebhook(body, 'signature', token);
      
      expect(typeof isValid).toBe('boolean');
    });
  });

  describe('Telegram Utils', () => {
    it('should extract Twitter URLs from text', () => {
      const text = 'Check this tweet: https://twitter.com/user/status/123456';
      const url = extractTwitterUrl(text);
      
      expect(url).toBe('https://twitter.com/user/status/123456');
    });

    it('should detect Twitter URLs in text', () => {
      expect(containsTwitterUrl('https://twitter.com/user/status/123')).toBe(true);
      expect(containsTwitterUrl('https://x.com/user/status/123')).toBe(true);
      expect(containsTwitterUrl('No Twitter links here')).toBe(false);
    });

    it('should parse Telegram commands', () => {
      const result = parseCommand('/start arg1 arg2');
      
      expect(result).toEqual({
        command: 'start',
        args: ['arg1', 'arg2']
      });
    });

    it('should return null for non-commands', () => {
      expect(parseCommand('not a command')).toBeNull();
    });

    it('should validate Telegram configuration', () => {
      // Mock environment variables
      const originalEnv = process.env;
      process.env = { ...originalEnv };
      
      // Test missing token
      delete process.env.TELEGRAM_BOT_TOKEN;
      let config = validateTelegramConfig();
      expect(config.isValid).toBe(false);
      expect(config.errors).toContain('TELEGRAM_BOT_TOKEN is required');
      
      // Test valid config
      process.env.TELEGRAM_BOT_TOKEN = 'test-token';
      config = validateTelegramConfig();
      expect(config.isValid).toBe(true);
      
      // Restore environment
      process.env = originalEnv;
    });
  });

  describe('Message Formatting', () => {
    it('should split long messages correctly', () => {
      const agent = new TelegramBenjiAgent();
      
      // Test paragraph splitting
      const longParagraphs = Array(10).fill('This is a paragraph with some content.').join('\n\n');
      const messages = agent.formatForTelegram(longParagraphs);
      
      expect(messages.length).toBeGreaterThan(1);
      expect(messages.every(msg => msg.length <= 4000)).toBe(true);
    });

    it('should handle single long paragraph', () => {
      const agent = new TelegramBenjiAgent();
      const longParagraph = 'A'.repeat(5000);
      
      const messages = agent.formatForTelegram(longParagraph);
      
      expect(messages.length).toBeGreaterThan(1);
      expect(messages.every(msg => msg.length <= 4000)).toBe(true);
    });

    it('should preserve short messages', () => {
      const agent = new TelegramBenjiAgent();
      const shortMessage = 'This is a short message.';
      
      const messages = agent.formatForTelegram(shortMessage);
      
      expect(messages).toEqual([shortMessage]);
    });
  });

  describe('Integration Scenarios', () => {
    it('should handle Twitter URL processing flow', async () => {
      const agent = new TelegramBenjiAgent({
        userId: 'test-user',
        telegramUserId: 'tg-user-123'
      });

      const twitterUrl = 'https://twitter.com/user/status/123456';
      const tweetContent = 'This is a sample tweet content';

      // Mock the generateTelegramQuickReply method
      const mockResult = {
        textStream: (async function* () {
          yield 'This is a generated reply';
        })()
      };

      vi.spyOn(agent, 'generateTelegramQuickReply').mockResolvedValue(mockResult as any);

      const result = await agent.generateTelegramQuickReply(tweetContent, twitterUrl);
      
      expect(agent.generateTelegramQuickReply).toHaveBeenCalledWith(tweetContent, twitterUrl);
      expect(result).toBeDefined();
    });

    it('should handle conversation flow', async () => {
      const agent = new TelegramBenjiAgent({
        userId: 'test-user',
        telegramUserId: 'tg-user-123'
      });

      const message = 'Hello, how are you?';

      // Mock the generateTelegramResponse method
      const mockResult = {
        textStream: (async function* () {
          yield 'Hello! I am doing well, thank you for asking.';
        })()
      };

      vi.spyOn(agent, 'generateTelegramResponse').mockResolvedValue(mockResult as any);

      const result = await agent.generateTelegramResponse(message);
      
      expect(agent.generateTelegramResponse).toHaveBeenCalledWith(message);
      expect(result).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid Twitter URLs gracefully', () => {
      expect(TelegramBenjiAgent.extractTwitterUrl('invalid url')).toBeNull();
      expect(TelegramBenjiAgent.isTwitterUrl('not a twitter url')).toBe(false);
    });

    it('should handle malformed commands', () => {
      expect(TelegramBenjiAgent.parseCommand('')).toBeNull();
      expect(TelegramBenjiAgent.parseCommand('/')).toEqual({ command: '', args: [] });
    });

    it('should handle empty messages for formatting', () => {
      const agent = new TelegramBenjiAgent();
      
      expect(agent.formatForTelegram('')).toEqual(['']);
      expect(agent.formatForTelegram('   ')).toEqual(['   ']);
    });
  });
});

describe('Environment Configuration', () => {
  it('should detect missing required environment variables', () => {
    const originalEnv = process.env;
    
    // Clear Telegram-related env vars
    process.env = { ...originalEnv };
    delete process.env.TELEGRAM_BOT_TOKEN;
    
    const config = validateTelegramConfig();
    
    expect(config.isValid).toBe(false);
    expect(config.errors).toContain('TELEGRAM_BOT_TOKEN is required');
    
    // Restore environment
    process.env = originalEnv;
  });

  it('should detect optional missing variables', () => {
    const originalEnv = process.env;
    
    process.env = { 
      ...originalEnv,
      TELEGRAM_BOT_TOKEN: 'test-token'
    };
    delete process.env.TELEGRAM_WEBHOOK_SECRET;
    
    const config = validateTelegramConfig();
    
    expect(config.isValid).toBe(true);
    expect(config.warnings).toContain('TELEGRAM_WEBHOOK_SECRET is recommended for webhook security');
    
    // Restore environment
    process.env = originalEnv;
  });
});
