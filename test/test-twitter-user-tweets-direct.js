/**
 * Test script to directly test Twitter API user tweets endpoint
 * Run with: bun run test/test-twitter-user-tweets-direct.js
 */

const { twitterClient } = require('../apps/web/src/lib/twitter-client');

async function testTwitterUserTweetsDirect() {
  try {
    console.log('🧪 Testing Twitter API user tweets endpoint directly...');

    // Clear cache first to avoid stale data
    twitterClient.clearCache();

    // Test with different accounts - some very active ones
    const testAccounts = [
      'elonmusk',      // Very active account
      'vitalikbuterin', // Active crypto account
      'DefiIgnas',     // Active DeFi account
      'reisnertobias', // Our monitored account
    ];

    for (const username of testAccounts) {
      console.log(`\n📋 Testing @${username}...`);

      try {
        // Test without replies first
        const result = await twitterClient.getUserLastTweets(username, {
          includeReplies: false,
        });

        console.log(`✅ API Response for @${username} (no replies):`);
        console.log(`   Status: ${result.status}`);
        console.log(`   Message: ${result.message || 'none'}`);
        console.log(`   Tweets count: ${result.tweets.length}`);
        console.log(`   Has next page: ${result.has_next_page}`);
        console.log(`   Next cursor: ${result.next_cursor || 'none'}`);

        if (result.tweets.length > 0) {
          console.log(`   Sample tweet: ${result.tweets[0].text.substring(0, 100)}...`);
          console.log(`   Tweet author: @${result.tweets[0].author.userName}`);
          console.log(`   Tweet ID: ${result.tweets[0].id}`);
          console.log(`   Is reply: ${result.tweets[0].isReply}`);
          console.log(`   Created at: ${result.tweets[0].createdAt}`);
        }

        // Test with replies included
        const resultWithReplies = await twitterClient.getUserLastTweets(username, {
          includeReplies: true,
        });

        console.log(`   With replies: ${resultWithReplies.tweets.length} tweets`);

        // If we got tweets, break early to avoid rate limiting
        if (result.tweets.length > 0) {
          console.log(`✅ Success! Found tweets for @${username}`);
          break;
        }

      } catch (error) {
        console.error(`❌ Error testing @${username}:`, error.message);
      }

      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testTwitterUserTweetsDirect();
