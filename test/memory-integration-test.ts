/**
 * Memory Integration Test for BuddyChip Mem0 System
 * 
 * Tests the complete memory storage and retrieval pipeline
 */

import mem0Service from '../apps/web/src/lib/mem0-service';

async function testMemoryIntegration() {
  console.log('🧪 Testing BuddyChip Memory Integration...\n');

  try {
    // Test 1: Health Check
    console.log('📋 Test 1: Memory Service Health Check...');
    const healthCheck = await mem0Service.healthCheck();
    console.log('Health Status:', healthCheck.status);
    
    if (healthCheck.status === 'unhealthy') {
      console.error('❌ Memory service is unhealthy:', healthCheck.error);
      console.log('\n🔧 Troubleshooting Tips:');
      console.log('1. Check DATABASE_URL is set correctly');
      console.log('2. Verify OPENAI_API_KEY or OPENROUTER_API_KEY is set');
      console.log('3. Ensure Supabase vector extension is enabled');
      console.log('4. Check if memories table exists in database');
      return false;
    }
    console.log('✅ Memory service is healthy');

    // Test 2: Add Memory
    console.log('\n💾 Test 2: Adding test memory...');
    const testUserId = 'test-user-' + Date.now();
    const testMessages = [
      { role: 'user', content: 'I love trading Bitcoin and Ethereum' },
      { role: 'assistant', content: 'Great! I can help you track crypto trends and market sentiment.' }
    ];
    
    const testContext = {
      userId: testUserId,
      sessionId: 'test-session-' + Date.now(),
      mentionId: 'test-mention-' + Date.now(),
    };

    await mem0Service.addMemories(testUserId, testMessages, testContext, {
      memoryType: 'preference',
      metadata: { test: true, crypto_interest: ['BTC', 'ETH'] }
    });
    console.log('✅ Memory added successfully');

    // Test 3: Search Memory
    console.log('\n🔍 Test 3: Searching for memories...');
    const searchResults = await mem0Service.searchMemories(testUserId, {
      query: 'crypto trading preferences',
      limit: 3,
      memoryType: 'preference'
    });

    console.log(`Found ${searchResults.length} relevant memories:`);
    searchResults.forEach((memory, index) => {
      console.log(`  ${index + 1}. ${memory.content} (similarity: ${memory.similarity?.toFixed(3)})`);
    });

    if (searchResults.length === 0) {
      console.warn('⚠️ No memories found - this might indicate an issue with embedding generation');
    } else {
      console.log('✅ Memory search working correctly');
    }

    // Test 4: Get Memory Context
    console.log('\n🧠 Test 4: Getting memory context...');
    const memoryContext = await mem0Service.getUserMemoryContext(
      testUserId,
      'What cryptocurrencies should I focus on?',
      3
    );

    if (memoryContext && memoryContext.length > 0) {
      console.log(`✅ Memory context retrieved: ${memoryContext.length} relevant memories`);
      console.log('Context preview:', memoryContext.substring(0, 100) + '...');
    } else {
      console.log('ℹ️ No memory context found (this is normal for new users)');
    }

    // Test 5: Get All User Memories
    console.log('\n📋 Test 5: Getting all user memories...');
    const allMemories = await mem0Service.getUserMemories(testUserId, 10);
    console.log(`✅ Retrieved ${allMemories.length} total memories for user`);

    // Test 6: Cleanup
    console.log('\n🧹 Test 6: Cleaning up test data...');
    // Note: In a real scenario, you might want to implement a cleanup method
    // For now, we'll just note that test data exists
    console.log('ℹ️ Test memories will be cleaned up by the database cleanup functions');

    console.log('\n🎉 Memory integration test completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`- Health Check: ✅ ${healthCheck.status}`);
    console.log(`- Memory Addition: ✅ Success`);
    console.log(`- Memory Search: ✅ ${searchResults.length} results`);
    console.log(`- Memory Context: ✅ ${memoryContext ? 'Available' : 'Empty (normal)'}`);
    console.log(`- User Memories: ✅ ${allMemories.length} total`);

    return true;

  } catch (error) {
    console.error('❌ Memory integration test failed:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack?.split('\n').slice(0, 3).join('\n')
      });
    }

    console.log('\n🔧 Common Issues and Solutions:');
    console.log('1. Missing API Keys: Ensure OPENAI_API_KEY or OPENROUTER_API_KEY is set');
    console.log('2. Database Connection: Check DATABASE_URL and DIRECT_URL');
    console.log('3. Vector Extension: Ensure pgvector extension is enabled in Supabase');
    console.log('4. Memory Table: Verify memories table exists with correct schema');
    console.log('5. Network Issues: Check if Supabase is accessible');

    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testMemoryIntegration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

export default testMemoryIntegration;
