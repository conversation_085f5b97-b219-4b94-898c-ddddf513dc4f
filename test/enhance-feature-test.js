/**
 * Simple test script to verify the enhance feature is working
 * Run with: node test/enhance-feature-test.js
 */

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.OPENROUTER_API_KEY = 'test-key';
process.env.COOKIE_API_KEY = 'test-key';
process.env.XAI_API_KEY = 'test-key';
process.env.EXA_API_KEY = 'test-key';

// Mock the AI SDK
const mockStreamText = {
  textStream: (async function* () {
    yield 'This is a test enhanced response with market intelligence. ';
    yield 'Based on current crypto trends, Bitcoin is showing strong momentum. ';
    yield 'The DeFi sector is experiencing increased activity with projects like Uniswap gaining mindshare.';
  })(),
  usage: Promise.resolve({ totalTokens: 150 })
};

// Mock the AI module
const aiMock = {
  streamText: () => mockStreamText,
  generateId: () => 'test-id-123'
};

// Mock the cookie client
const cookieClientMock = {
  getTrendingProjects: async (sector, timeframe) => {
    console.log(`🍪 Mock: Getting trending projects for sector: ${sector || 'general'}, timeframe: ${timeframe}`);
    return [
      {
        name: 'Bitcoin',
        symbol: 'BTC',
        sector: 'layer1',
        mindshare: 85,
        mindshareDelta: 5
      },
      {
        name: 'Ethereum',
        symbol: 'ETH', 
        sector: 'layer1',
        mindshare: 78,
        mindshareDelta: -2
      },
      {
        name: 'Uniswap',
        symbol: 'UNI',
        sector: 'defi',
        mindshare: 65,
        mindshareDelta: 8
      }
    ];
  },
  searchProjects: async ({ query, limit }) => {
    console.log(`🍪 Mock: Searching for project: ${query}`);
    return {
      projects: [
        {
          name: query.charAt(0).toUpperCase() + query.slice(1),
          symbol: query.toUpperCase(),
          sector: 'crypto',
          mindshare: 60,
          mindshareDelta: 3
        }
      ]
    };
  }
};

// Mock modules
require.cache[require.resolve('ai')] = { exports: aiMock };

async function testEnhanceFeature() {
  console.log('🧪 Testing Enhance Feature...\n');

  try {
    // Import the BenjiAgent after mocking
    const { BenjiAgent } = require('../apps/web/src/lib/benji-agent');
    
    // Mock the cookie client import
    const cookieClientPath = require.resolve('../apps/web/src/lib/cookie-client');
    require.cache[cookieClientPath] = { 
      exports: { cookieClient: cookieClientMock }
    };

    // Create a test agent
    const agent = new BenjiAgent({
      model: 'gemini25Flash',
      userId: 'test-user-123',
      enableTools: true,
      maxTokens: 1000,
      temperature: 0.7
    });

    console.log('✅ BenjiAgent created successfully');

    // Test crypto content detection and market intelligence
    const cryptoContent = 'What do you think about Bitcoin and the current DeFi trends? $BTC is looking bullish!';
    
    console.log('\n🔍 Testing market intelligence fetching...');
    console.log('Content:', cryptoContent);

    // Test the enhanced response generation
    console.log('\n🚀 Testing enhanced response generation...');
    
    const result = await agent.generateEnhancedMentionResponseWithIntelligence(cryptoContent, {
      mentionId: 'test-mention-123',
      mentionContent: cryptoContent,
      authorInfo: {
        name: 'Test User',
        handle: 'testuser',
        avatarUrl: 'https://example.com/avatar.jpg'
      }
    });

    console.log('\n✅ Enhanced response generation completed');
    
    // Collect the response text
    let responseText = '';
    for await (const chunk of result.textStream) {
      responseText += chunk;
    }

    console.log('\n📝 Generated Response:');
    console.log('---');
    console.log(responseText);
    console.log('---');

    const usage = await result.usage;
    console.log('\n📊 Usage Stats:');
    console.log(`- Total tokens: ${usage?.totalTokens || 'N/A'}`);

    console.log('\n✅ Enhance feature test completed successfully!');
    
    return true;
  } catch (error) {
    console.error('\n❌ Enhance feature test failed:', error);
    console.error('Stack trace:', error.stack);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testEnhanceFeature()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

module.exports = { testEnhanceFeature };
