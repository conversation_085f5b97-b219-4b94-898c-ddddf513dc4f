/**
 * Compare regular vs enhanced prompts to ensure they're similar
 * Run with: bun run test/compare-prompts.js
 */

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.OPENROUTER_API_KEY = 'test-key';

async function comparePrompts() {
  console.log('🔍 Comparing Regular vs Enhanced Prompts...\n');

  try {
    // Import the BenjiAgent
    const { BenjiAgent } = require('../apps/web/src/lib/benji-agent');

    // Create a test agent
    const agent = new BenjiAgent({
      model: 'gemini25Flash',
      userId: 'test-user-123',
      enableTools: true,
      useFirstPerson: false // Test as external user
    });

    // Test context
    const testContext = {
      mentionId: 'test-mention-123',
      mentionContent: '@parcifap_defi @farcaster_xyz aped',
      authorInfo: {
        name: 'Parcifap DeFi',
        handle: 'parcifap_defi',
      },
      monitoredAccountInfo: {
        name: 'Tech Entrepreneur',
        handle: 'techguru',
      }
    };

    // Access the private buildSystemPrompt method for testing
    const buildSystemPrompt = agent.buildSystemPrompt.bind(agent);

    // Generate both prompts
    const regularPrompt = buildSystemPrompt('mention', testContext);
    const enhancedPrompt = buildSystemPrompt('enhanced', testContext);

    console.log('📝 REGULAR MENTION PROMPT:');
    console.log('=' .repeat(80));
    console.log(regularPrompt);
    console.log('=' .repeat(80));

    console.log('\n📝 ENHANCED MENTION PROMPT:');
    console.log('=' .repeat(80));
    console.log(enhancedPrompt);
    console.log('=' .repeat(80));

    // Analyze differences
    const regularLines = regularPrompt.split('\n');
    const enhancedLines = enhancedPrompt.split('\n');

    console.log('\n📊 PROMPT ANALYSIS:');
    console.log(`- Regular prompt lines: ${regularLines.length}`);
    console.log(`- Enhanced prompt lines: ${enhancedLines.length}`);
    console.log(`- Length difference: ${enhancedPrompt.length - regularPrompt.length} characters`);

    // Check for problematic phrases in enhanced prompt
    const problematicPhrases = [
      'analyze',
      'should',
      'need to',
      'my role is',
      'given that',
      'therefore',
      'explanation',
      'reasoning'
    ];

    const foundProblematic = problematicPhrases.filter(phrase => 
      enhancedPrompt.toLowerCase().includes(phrase)
    );

    if (foundProblematic.length > 0) {
      console.log(`\n⚠️ POTENTIAL ISSUES FOUND:`);
      foundProblematic.forEach(phrase => {
        console.log(`- Contains "${phrase}"`);
      });
    } else {
      console.log(`\n✅ No problematic analytical phrases found`);
    }

    // Check if both prompts end with similar instructions
    const regularEnding = regularLines[regularLines.length - 1];
    const enhancedEnding = enhancedLines[enhancedLines.length - 1];

    console.log(`\n📋 ENDING INSTRUCTIONS:`);
    console.log(`- Regular: "${regularEnding}"`);
    console.log(`- Enhanced: "${enhancedEnding}"`);
    console.log(`- Similar endings: ${regularEnding === enhancedEnding ? '✅ YES' : '❌ NO'}`);

    return true;
    
  } catch (error) {
    console.error('\n❌ Prompt comparison failed:', error);
    return false;
  }
}

// Run the comparison
if (require.main === module) {
  comparePrompts()
    .then(success => {
      console.log('\n' + '='.repeat(50));
      console.log('📋 PROMPT COMPARISON COMPLETE');
      console.log('='.repeat(50));
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Comparison error:', error);
      process.exit(1);
    });
}

module.exports = { comparePrompts };
