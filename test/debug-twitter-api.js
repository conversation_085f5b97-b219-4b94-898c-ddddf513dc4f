/**
 * Debug script to see full Twitter API response
 * Run with: bun run test/debug-twitter-api.js
 */

const { twitterClient } = require('../apps/web/src/lib/twitter-client');

async function debugTwitterAPI() {
  try {
    console.log('🔍 Debugging Twitter API response...');
    
    // Test with a single account
    const username = 'elonmusk';
    console.log(`\n📋 Testing @${username}...`);
    
    const result = await twitterClient.getUserLastTweets(username, {
      includeReplies: false,
    });
    
    console.log(`\n📊 Final result:`, result);
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugTwitterAPI();
