/**
 * Test script to trigger sync for accounts with syncUserTweets enabled
 * Run with: bun run test/trigger-sync-test.js
 */

const { PrismaClient } = require('../apps/web/prisma/generated');
const { MentionSyncService } = require('../apps/web/src/lib/mention-sync-service');

async function triggerSyncTest() {
  const prisma = new PrismaClient();
  const syncService = new MentionSyncService(prisma);
  
  try {
    console.log('🧪 Testing sync for accounts with syncUserTweets enabled...');
    
    // Get accounts with syncUserTweets enabled
    const accounts = await prisma.monitoredAccount.findMany({
      where: {
        userId: 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ',
        syncUserTweets: true,
        isActive: true,
      },
    });

    console.log(`📋 Found ${accounts.length} accounts with syncUserTweets enabled:`);
    accounts.forEach(account => {
      console.log(`   - @${account.twitterHandle} (syncUserTweets: ${account.syncUserTweets})`);
    });

    if (accounts.length === 0) {
      console.log('❌ No accounts found with syncUserTweets enabled');
      return;
    }

    // Test with the first account
    const testAccount = accounts[0];
    console.log(`\n🔄 Testing sync for @${testAccount.twitterHandle}...`);

    // Check current user tweets count
    const beforeCount = await prisma.mention.count({
      where: {
        accountId: testAccount.id,
        isUserTweet: true,
      },
    });

    console.log(`📊 Current user tweets in DB: ${beforeCount}`);

    // Trigger sync
    const result = await syncService.syncAccountMentions(testAccount.id, testAccount.userId);
    
    console.log('📊 Sync result:', {
      success: result.success,
      newMentions: result.newMentions,
      totalMentions: result.totalMentions,
      error: result.error,
    });

    // Check after sync
    const afterCount = await prisma.mention.count({
      where: {
        accountId: testAccount.id,
        isUserTweet: true,
      },
    });

    console.log(`📊 User tweets after sync: ${afterCount}`);
    console.log(`📈 New user tweets added: ${afterCount - beforeCount}`);

    // Show some sample user tweets if any exist
    if (afterCount > 0) {
      const userTweets = await prisma.mention.findMany({
        where: {
          accountId: testAccount.id,
          isUserTweet: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 3,
      });

      console.log('\n📝 Sample user tweets:');
      userTweets.forEach((tweet, i) => {
        console.log(`${i + 1}. @${tweet.authorHandle}: ${tweet.content.substring(0, 100)}...`);
      });
    } else {
      console.log('\n⚠️ No user tweets found. This could mean:');
      console.log('   1. The account has no recent tweets');
      console.log('   2. The Twitter API is not returning user tweets');
      console.log('   3. There might be an API configuration issue');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

triggerSyncTest();
