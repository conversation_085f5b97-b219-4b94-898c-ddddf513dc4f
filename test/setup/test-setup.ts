/**
 * Test Setup
 * 
 * Runs before each test file to setup the test environment
 */

import { beforeAll, afterAll, beforeEach, afterEach, expect } from 'vitest'
import { setupServer } from 'msw/node'
import '@testing-library/jest-dom'

// Import MSW handlers
import { handlers } from '../mocks/handlers'

// Import test utilities
import { cleanupDatabase, resetTestData } from '../utils/database'
import { clearAllMocks } from '../utils/mocks'

// Setup MSW server
export const server = setupServer(...handlers)

// Global test setup
beforeAll(async () => {
  console.log('🧪 Test Setup: Starting test file...')
  
  // Start MSW server
  server.listen({
    onUnhandledRequest: 'warn'
  })
  
  console.log('🎭 MSW server started')
})

// Cleanup after all tests
afterAll(async () => {
  console.log('🧹 Test Setup: Cleaning up test file...')
  
  // Stop MSW server
  server.close()
  
  // Final database cleanup
  await cleanupDatabase()
  
  console.log('✅ Test file cleanup complete')
})

// Setup before each test
beforeEach(async () => {
  console.log('🔄 Test Setup: Preparing test...')
  
  // Reset MSW handlers
  server.resetHandlers()
  
  // Clear all mocks
  clearAllMocks()
  
  // Reset test data to known state
  await resetTestData()
  
  console.log('✅ Test preparation complete')
})

// Cleanup after each test
afterEach(async () => {
  console.log('🧹 Test Setup: Cleaning up test...')
  
  // Clear any test-specific data
  await cleanupDatabase()
  
  // Reset all mocks
  clearAllMocks()
  
  console.log('✅ Test cleanup complete')
})

// Global error handler for unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason)
})

// Global error handler for uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error)
})

// Extend expect with custom matchers
declare global {
  namespace Vi {
    interface JestAssertion<T = any> {
      toBeValidUUID(): T
      toBeValidEmail(): T
      toBeValidTwitterHandle(): T
      toHaveValidTimestamp(): T
      toMatchTweetStructure(): T
      toMatchUserStructure(): T
    }
  }
}

// Custom matchers
expect.extend({
  toBeValidUUID(received: string) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    const pass = uuidRegex.test(received)
    
    return {
      pass,
      message: () => pass 
        ? `Expected ${received} not to be a valid UUID`
        : `Expected ${received} to be a valid UUID`
    }
  },
  
  toBeValidEmail(received: string) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const pass = emailRegex.test(received)
    
    return {
      pass,
      message: () => pass
        ? `Expected ${received} not to be a valid email`
        : `Expected ${received} to be a valid email`
    }
  },
  
  toBeValidTwitterHandle(received: string) {
    const handleRegex = /^[a-zA-Z0-9_]{1,15}$/
    const pass = handleRegex.test(received)
    
    return {
      pass,
      message: () => pass
        ? `Expected ${received} not to be a valid Twitter handle`
        : `Expected ${received} to be a valid Twitter handle`
    }
  },
  
  toHaveValidTimestamp(received: any) {
    const hasCreatedAt = received.createdAt && !isNaN(Date.parse(received.createdAt))
    const hasUpdatedAt = received.updatedAt && !isNaN(Date.parse(received.updatedAt))
    const pass = hasCreatedAt && hasUpdatedAt
    
    return {
      pass,
      message: () => pass
        ? `Expected object not to have valid timestamps`
        : `Expected object to have valid createdAt and updatedAt timestamps`
    }
  },
  
  toMatchTweetStructure(received: any) {
    const requiredFields = ['id', 'text', 'author', 'createdAt']
    const hasAllFields = requiredFields.every(field => field in received)
    const hasAuthorStructure = received.author && 
      'id' in received.author && 
      'userName' in received.author && 
      'name' in received.author
    
    const pass = hasAllFields && hasAuthorStructure
    
    return {
      pass,
      message: () => pass
        ? `Expected object not to match tweet structure`
        : `Expected object to match tweet structure with fields: ${requiredFields.join(', ')} and author object`
    }
  },
  
  toMatchUserStructure(received: any) {
    const requiredFields = ['id', 'planId', 'createdAt', 'updatedAt']
    const hasAllFields = requiredFields.every(field => field in received)
    const hasPlan = received.plan && 'name' in received.plan
    
    const pass = hasAllFields && hasPlan
    
    return {
      pass,
      message: () => pass
        ? `Expected object not to match user structure`
        : `Expected object to match user structure with fields: ${requiredFields.join(', ')} and plan object`
    }
  }
})

// Console logging configuration for tests
const originalConsoleError = console.error
const originalConsoleWarn = console.warn

console.error = (...args: any[]) => {
  // Filter out expected test errors
  const message = args[0]?.toString() || ''
  
  if (
    message.includes('Warning: ReactDOM.render is deprecated') ||
    message.includes('Warning: React.createFactory() is deprecated') ||
    message.includes('MSW: Found a redundant usage of query parameters')
  ) {
    return
  }
  
  originalConsoleError(...args)
}

console.warn = (...args: any[]) => {
  // Filter out expected test warnings
  const message = args[0]?.toString() || ''
  
  if (
    message.includes('MSW: intercepted a request without a matching request handler') ||
    message.includes('Warning: Each child in a list should have a unique "key" prop')
  ) {
    return
  }
  
  originalConsoleWarn(...args)
}

// Server is already exported above
