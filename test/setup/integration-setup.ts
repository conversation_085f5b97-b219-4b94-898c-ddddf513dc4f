/**
 * Integration Test Setup
 * 
 * Setup for integration tests that require database and external services
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import '@testing-library/jest-dom'

// Import MSW handlers
import { handlers } from '../mocks/handlers'

// Import test utilities
import { resetTestData, cleanupDatabase, disconnectTestDatabase } from '../utils/database'
import { clearAllMocks } from '../utils/mocks'

// Setup MSW server
export const server = setupServer(...handlers)

// Global integration test setup
beforeAll(async () => {
  console.log('🧪 Integration Test Setup: Starting...')
  
  // Start MSW server
  server.listen({
    onUnhandledRequest: 'warn'
  })
  
  // Verify test database connection
  const testDbUrl = process.env.DATABASE_URL
  if (!testDbUrl || !testDbUrl.includes('_test')) {
    throw new Error('Integration tests must use a test database (URL must contain "_test")')
  }
  
  console.log('🎭 MSW server started for integration tests')
  console.log('📊 Test database verified:', testDbUrl.replace(/password=[^&\s]+/, 'password=***'))
})

// Cleanup after all integration tests
afterAll(async () => {
  console.log('🧹 Integration Test Setup: Final cleanup...')
  
  // Stop MSW server
  server.close()
  
  // Final database cleanup
  await cleanupDatabase()
  
  // Disconnect from test database
  await disconnectTestDatabase()
  
  console.log('✅ Integration test cleanup complete')
})

// Setup before each integration test
beforeEach(async () => {
  console.log('🔄 Integration Test Setup: Preparing test...')
  
  // Reset MSW handlers
  server.resetHandlers()
  
  // Clear all mocks
  clearAllMocks()
  
  // Reset database to known state
  await resetTestData()
  
  console.log('✅ Integration test preparation complete')
})

// Cleanup after each integration test
afterEach(async () => {
  console.log('🧹 Integration Test Setup: Cleaning up test...')
  
  // Clean up test-specific data
  await cleanupDatabase()
  
  // Reset all mocks
  clearAllMocks()
  
  console.log('✅ Integration test cleanup complete')
})

// Enhanced error handling for integration tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Integration Test: Unhandled Rejection at:', promise, 'reason:', reason)
  // Don't exit process in tests, just log
})

process.on('uncaughtException', (error) => {
  console.error('❌ Integration Test: Uncaught Exception:', error)
  // Don't exit process in tests, just log
})

// Database transaction helpers for integration tests
export const withTransaction = async (callback: (tx: any) => Promise<void>) => {
  const { testPrisma } = await import('../utils/database')
  
  return testPrisma.$transaction(async (tx) => {
    try {
      await callback(tx)
    } catch (error) {
      console.error('❌ Integration Test: Transaction failed:', error)
      throw error
    }
  })
}

// Helper to wait for async operations
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Helper to retry operations (useful for eventual consistency)
export const retry = async <T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      console.warn(`❌ Integration Test: Attempt ${attempt}/${maxAttempts} failed:`, error)
      
      if (attempt < maxAttempts) {
        await waitFor(delay)
      }
    }
  }
  
  throw lastError!
}

// Helper to create test context for tRPC calls
export const createTestContext = (overrides: {
  userId?: string | null
  sessionId?: string | null
} = {}) => {
  return {
    userId: overrides.userId ?? 'test-user-integration',
    sessionId: overrides.sessionId ?? 'test-session-integration',
    req: {
      headers: {
        'user-agent': 'integration-test-agent',
        'x-forwarded-for': '127.0.0.1',
        'content-type': 'application/json'
      },
      url: 'http://localhost:3000/api/trpc',
      method: 'POST'
    }
  }
}

// Helper to simulate authenticated requests
export const withAuth = (userId: string = 'test-user-integration') => {
  return createTestContext({ userId })
}

// Helper to simulate unauthenticated requests
export const withoutAuth = () => {
  return createTestContext({ userId: null, sessionId: null })
}

// Console filtering for cleaner integration test output
const originalConsoleLog = console.log
const originalConsoleWarn = console.warn
const originalConsoleError = console.error

// Filter out noisy logs during integration tests
console.log = (...args: any[]) => {
  const message = args[0]?.toString() || ''
  
  // Allow test-related logs
  if (
    message.includes('🧪') ||
    message.includes('✅') ||
    message.includes('❌') ||
    message.includes('🔄') ||
    message.includes('🧹')
  ) {
    originalConsoleLog(...args)
    return
  }
  
  // Filter out verbose database logs
  if (
    message.includes('prisma:query') ||
    message.includes('prisma:info') ||
    message.includes('DB-Utils')
  ) {
    return
  }
  
  originalConsoleLog(...args)
}

console.warn = (...args: any[]) => {
  const message = args[0]?.toString() || ''
  
  // Filter out MSW warnings
  if (
    message.includes('MSW: intercepted a request without a matching request handler') ||
    message.includes('MSW: Found a redundant usage of query parameters')
  ) {
    return
  }
  
  originalConsoleWarn(...args)
}

console.error = (...args: any[]) => {
  const message = args[0]?.toString() || ''
  
  // Filter out expected test errors
  if (
    message.includes('Warning: ReactDOM.render is deprecated') ||
    message.includes('Warning: React.createFactory() is deprecated')
  ) {
    return
  }
  
  originalConsoleError(...args)
}

// Server is already exported above
