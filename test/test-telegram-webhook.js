#!/usr/bin/env node

/**
 * Test Telegram webhook with a realistic message
 */

const https = require('https');

const testMessage = {
  update_id: 123456789,
  message: {
    message_id: 1,
    chat: {
      id: 12345,
      type: "private"
    },
    from: {
      id: 12345,
      username: "testuser",
      first_name: "Test",
      language_code: "en",
      is_bot: false
    },
    text: "hello",
    date: Math.floor(Date.now() / 1000)
  }
};

const data = JSON.stringify(testMessage);

const options = {
  hostname: 'www.buddychip.app',
  port: 443,
  path: '/api/telegram/webhook',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length,
    'x-telegram-bot-api-secret-token': 'fedb2719f4a14793c848016d95e7378dcd7fab158f6ac7cd46ea266d645989d9',
    'User-Agent': 'TelegramBot/1.0'
  }
};

console.log('🧪 Testing Telegram webhook with /start command...');
console.log('📡 URL:', `https://${options.hostname}${options.path}`);
console.log('📨 Payload:', JSON.stringify(testMessage, null, 2));

const req = https.request(options, (res) => {
  console.log(`\n📊 Response Status: ${res.statusCode}`);
  console.log('📋 Response Headers:', res.headers);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log('\n📝 Response Body:');
    try {
      const parsed = JSON.parse(responseData);
      console.log(JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.log(responseData);
    }
    
    if (res.statusCode === 200) {
      console.log('\n✅ Webhook test successful!');
    } else {
      console.log('\n❌ Webhook test failed!');
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request error:', error);
});

req.write(data);
req.end();