#!/usr/bin/env node

/**
 * Test Telegram webhook with /start command to your real chat ID
 */

const https = require('https');

const testMessage = {
  update_id: Math.floor(Math.random() * 1000000),
  message: {
    message_id: Math.floor(Math.random() * 1000),
    chat: {
      id: 904041730, // Your real chat ID
      type: "private"
    },
    from: {
      id: 904041730, // Your real user ID  
      username: "france<PERSON><PERSON><PERSON>",
      first_name: "<PERSON>",
      language_code: "en",
      is_bot: false
    },
    text: "/start",
    date: Math.floor(Date.now() / 1000)
  }
};

const data = JSON.stringify(testMessage);

const options = {
  hostname: 'www.buddychip.app',
  port: 443,
  path: '/api/telegram/webhook',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': data.length,
    'x-telegram-bot-api-secret-token': 'fedb2719f4a14793c848016d95e7378dcd7fab158f6ac7cd46ea266d645989d9',
    'User-Agent': 'TelegramBot/1.0'
  }
};

console.log('🧪 Testing /start command through webhook...');
console.log('📨 Payload:', JSON.stringify(testMessage, null, 2));

const req = https.request(options, (res) => {
  console.log(`\n📊 Response Status: ${res.statusCode}`);
  
  let responseData = '';
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    console.log('\n📝 Response Body:');
    try {
      const parsed = JSON.parse(responseData);
      console.log(JSON.stringify(parsed, null, 2));
    } catch (e) {
      console.log(responseData);
    }
    
    if (res.statusCode === 200) {
      console.log('\n✅ Webhook processed successfully!');
      console.log('💡 Check your Telegram for the bot response.');
    } else {
      console.log('\n❌ Webhook processing failed!');
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request error:', error);
});

req.write(data);
req.end();