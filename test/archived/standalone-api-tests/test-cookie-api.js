#!/usr/bin/env node

// Test Cookie.fun API directly
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const COOKIE_API_BASE_URL = 'https://api.cookie.fun';

console.log('🍪 Testing Cookie.fun API...');
console.log('🔑 API Key configured:', !!COOKIE_API_KEY);
console.log('🔑 API Key (first 10 chars):', COOKIE_API_KEY?.substring(0, 10) + '...');

async function testEndpoint(endpoint) {
  const url = `${COOKIE_API_BASE_URL}${endpoint}`;
  
  try {
    console.log(`\n🔄 Testing: ${endpoint}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${COOKIE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success: ${JSON.stringify(data).substring(0, 200)}...`);
      return { success: true, status: response.status, data };
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
      return { success: false, status: response.status, error: errorText };
    }
    
  } catch (error) {
    console.log(`💥 Exception: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function testSearchProjects() {
  const url = `${COOKIE_API_BASE_URL}/v3/project/search`;
  
  try {
    console.log(`\n🔄 Testing: POST /v3/project/search`);
    
    const body = {
      searchQuery: 'bitcoin',
      limit: 5
    };
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${COOKIE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success: Found ${data?.data?.length || 0} projects`);
      return { success: true, status: response.status, data };
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
      return { success: false, status: response.status, error: errorText };
    }
    
  } catch (error) {
    console.log(`💥 Exception: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const endpoints = [
    '/v3/sectors',
    '/v2/sectors', 
    '/sectors',
    '/v3/projects',
    '/v2/projects',
    '/projects',
    '/v3/project',
    '/v2/project',
    '/project'
  ];

  console.log('\n🧪 Testing GET endpoints...');
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push({ endpoint, ...result });
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  console.log('\n🧪 Testing POST endpoints...');
  const searchResult = await testSearchProjects();
  results.push({ endpoint: 'POST /v3/project/search', ...searchResult });

  console.log('\n📋 Summary:');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful endpoints: ${successful.length}`);
  successful.forEach(r => console.log(`  - ${r.endpoint}`));
  
  console.log(`❌ Failed endpoints: ${failed.length}`);
  failed.forEach(r => console.log(`  - ${r.endpoint} (${r.status || 'Exception'})`));
  
  if (successful.length > 0) {
    console.log('\n🎉 Cookie.fun API is accessible!');
    console.log('💡 Use the successful endpoints in your application.');
  } else {
    console.log('\n💔 No endpoints are accessible.');
    console.log('💡 Check your API key or contact Cookie.fun support.');
  }
}

runTests().catch(console.error);
