#!/usr/bin/env node

// Comprehensive database status check for BuddyChip
const { PrismaClient } = require('./prisma/generated/index.js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('🔍 BuddyChip Database Status Check');
console.log('=====================================');

const prisma = new PrismaClient({
  log: ['error', 'warn'],
  errorFormat: 'pretty',
});

async function checkDatabaseStatus() {
  try {
    console.log('\n📊 Database Connection Info:');
    console.log('✅ DATABASE_URL configured:', !!process.env.DATABASE_URL);
    console.log('✅ DIRECT_URL configured:', !!process.env.DIRECT_URL);
    console.log('🔗 Using pooler connection:', process.env.DATABASE_URL?.includes('pooler.supabase.com'));

    // Test basic connection
    console.log('\n🔄 Testing basic connection...');
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Basic connection successful');

    // Check database info
    console.log('\n🔄 Checking database info...');
    const dbInfo = await prisma.$queryRaw`
      SELECT 
        version() as db_version,
        current_database() as db_name,
        current_user as db_user
    `;
    console.log('✅ Database info:', dbInfo[0]);

    // Check extensions
    console.log('\n🔄 Checking PostgreSQL extensions...');
    const extensions = await prisma.$queryRaw`
      SELECT name, installed_version 
      FROM pg_available_extensions 
      WHERE name IN ('vector', 'pgcrypto', 'pg_trgm') 
      AND installed_version IS NOT NULL
      ORDER BY name
    `;
    console.log('✅ Installed extensions:', extensions);

    // Check table counts
    console.log('\n🔄 Checking table data...');
    const [
      mentionsCount,
      usersCount,
      accountsCount,
      plansCount,
      responsesCount
    ] = await Promise.all([
      prisma.mention.count(),
      prisma.user.count(),
      prisma.monitoredAccount.count(),
      prisma.subscriptionPlan.count(),
      prisma.aIResponse.count()
    ]);

    console.log('📊 Table Statistics:');
    console.log(`  • Mentions: ${mentionsCount}`);
    console.log(`  • Users: ${usersCount}`);
    console.log(`  • Monitored Accounts: ${accountsCount}`);
    console.log(`  • Subscription Plans: ${plansCount}`);
    console.log(`  • AI Responses: ${responsesCount}`);

    // Test a complex query
    console.log('\n🔄 Testing complex query...');
    const recentMentions = await prisma.mention.findMany({
      take: 3,
      orderBy: { mentionedAt: 'desc' },
      include: {
        responses: {
          take: 1,
          orderBy: { createdAt: 'desc' }
        }
      }
    });
    console.log(`✅ Successfully fetched ${recentMentions.length} recent mentions with AI responses`);

    // Check for any processing errors
    console.log('\n🔄 Checking for processing errors...');
    const errorCount = await prisma.mention.count({
      where: {
        processingError: {
          not: null
        }
      }
    });
    
    if (errorCount > 0) {
      console.log(`⚠️  Found ${errorCount} mentions with processing errors`);
    } else {
      console.log('✅ No processing errors found');
    }

    console.log('\n🎉 Database Status: HEALTHY');
    console.log('=====================================');
    console.log('✅ All database checks passed successfully!');
    console.log('✅ Extensions installed correctly');
    console.log('✅ Tables accessible and populated');
    console.log('✅ Complex queries working');
    console.log('✅ No critical errors detected');

  } catch (error) {
    console.error('\n❌ Database Status: ERROR');
    console.error('=====================================');
    console.error('Error details:', error);
    
    if (error.message?.includes('Can\'t reach database server')) {
      console.error('\n💡 Possible solutions:');
      console.error('  • Check if Supabase project is paused');
      console.error('  • Verify DATABASE_URL and DIRECT_URL are correct');
      console.error('  • Try using pooler connection for both URLs');
    }
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkDatabaseStatus();
