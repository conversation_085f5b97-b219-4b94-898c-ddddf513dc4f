#!/usr/bin/env node

// Test database connection
const { PrismaClient } = require('./prisma/generated/index.js');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

console.log('🔍 Testing database connection...');
console.log('🔍 DATABASE_URL exists:', !!process.env.DATABASE_URL);
console.log('🔍 DATABASE_URL value:', process.env.DATABASE_URL?.substring(0, 50) + '...');

const prisma = new PrismaClient({
  log: ['query', 'error', 'warn'],
  errorFormat: 'pretty',
});

async function testConnection() {
  try {
    console.log('🔄 Testing basic connection...');
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Basic connection successful');

    console.log('🔄 Testing mentions count...');
    const mentionCount = await prisma.mention.count();
    console.log(`✅ Found ${mentionCount} mentions in database`);

    console.log('🔄 Testing users count...');
    const userCount = await prisma.user.count();
    console.log(`✅ Found ${userCount} users in database`);

    console.log('🔄 Testing a simple mention query...');
    const mentions = await prisma.mention.findMany({
      take: 1,
      include: {
        account: {
          select: {
            id: true,
            twitterHandle: true,
            displayName: true,
          },
        },
      },
    });
    console.log(`✅ Successfully fetched ${mentions.length} mention(s)`);
    if (mentions.length > 0) {
      console.log('📄 Sample mention:', {
        id: mentions[0].id,
        content: mentions[0].content?.substring(0, 50) + '...',
        account: mentions[0].account?.twitterHandle,
      });
    }

    console.log('🎉 All database tests passed!');
  } catch (error) {
    console.error('❌ Database test failed:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta,
    });
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
