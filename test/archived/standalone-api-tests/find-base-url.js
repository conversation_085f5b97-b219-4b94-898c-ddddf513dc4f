#!/usr/bin/env node

// Find the correct Cookie.fun API base URL
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;

console.log('🔍 Finding correct Cookie.fun API base URL...');
console.log('🔑 API Key:', COOKIE_API_KEY?.substring(0, 10) + '...');

async function findBaseURL() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  // Common base URL variations to try
  const baseURLs = [
    'https://api.cookie.fun',
    'https://cookie.fun/api',
    'https://api.cookiefun.com',
    'https://cookiefun.com/api',
    'https://app.cookie.fun/api',
    'https://backend.cookie.fun',
    'https://api-v3.cookie.fun',
    'https://v3.api.cookie.fun',
    'https://cookie-api.fun',
    'https://api.cookie.app',
    'https://cookie.app/api'
  ];

  // Test endpoint that should work according to user
  const testEndpoint = '/v3/account';
  const testBody = { "username": "SmokeyTheBera" };

  console.log('\n🧪 Testing different base URLs...');
  
  for (const baseURL of baseURLs) {
    try {
      console.log(`\n🔄 Testing: ${baseURL}`);
      
      const response = await fetch(`${baseURL}${testEndpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${COOKIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testBody)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS! Found working base URL: ${baseURL}`);
        console.log(`📋 Response keys: ${Object.keys(data).join(', ')}`);
        
        if (data.data) {
          console.log(`👤 Account: ${data.data.username || data.data.name || 'Unknown'}`);
        }
        
        // Test another endpoint to confirm
        console.log(`\n🔄 Confirming with project endpoint...`);
        const projectResponse = await fetch(`${baseURL}/v3/project`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${COOKIE_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ "slug": "berachain" })
        });
        
        if (projectResponse.ok) {
          const projectData = await projectResponse.json();
          console.log(`✅ Project endpoint also works!`);
          console.log(`🚀 Project: ${projectData.data?.name || projectData.data?.slug || 'Unknown'}`);
        }
        
        console.log(`\n🎉 CORRECT BASE URL: ${baseURL}`);
        return baseURL;
        
      } else if (response.status === 401) {
        console.log(`🔑 Base URL responds but authentication failed - might be correct URL with wrong key`);
      } else if (response.status === 403) {
        console.log(`🚫 Base URL responds but access forbidden - might be correct URL`);
      } else if (response.status !== 404) {
        console.log(`⚠️  Base URL responds with ${response.status} - might be correct URL`);
      } else {
        console.log(`❌ 404 - Not found`);
      }
      
    } catch (error) {
      if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
        console.log(`🌐 DNS/Connection error - URL doesn't exist`);
      } else {
        console.log(`💥 Exception: ${error.message}`);
      }
    }
    
    // Add delay to avoid overwhelming servers
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n❌ No working base URL found from common variations');
  console.log('\n💡 Suggestions:');
  console.log('1. Check Cookie.fun documentation for current API URL');
  console.log('2. Contact Cookie.fun support');
  console.log('3. Check if the service has moved to a different domain');
  console.log('4. Verify the API key is still valid');
}

findBaseURL().catch(console.error);
