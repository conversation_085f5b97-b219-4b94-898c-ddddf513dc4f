#!/usr/bin/env node

// Test Cookie.fun API with updated documentation endpoints
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const COOKIE_API_BASE_URL = 'https://api.cookie.fun';

console.log('🍪 Testing Cookie.fun API with UPDATED documentation...');
console.log('🔑 API Key configured:', !!COOKIE_API_KEY);

async function testUpdatedEndpoints() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const testCases = [
    {
      name: 'GET /v3/sectors',
      method: 'GET',
      url: `${COOKIE_API_BASE_URL}/v3/sectors`,
      body: null
    },
    {
      name: 'POST /v3/project/search',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/project/search`,
      body: {
        "query": "bitcoin",
        "page": 1,
        "limit": 20
      }
    },
    {
      name: 'POST /v3/project/mindshare-leaderboard',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/project/mindshare-leaderboard`,
      body: {
        "mindshareTimeframe": "_30Days",
        "sortBy": "Mindshare",
        "sortOrder": "Descending"
      }
    },
    {
      name: 'POST /v3/project/mindshare-leaderboard (with sector)',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/project/mindshare-leaderboard`,
      body: {
        "mindshareTimeframe": "_30Days",
        "sortBy": "Mindshare",
        "sortOrder": "Descending",
        "sectorSlug": "defi"
      }
    },
    {
      name: 'POST /v3/project (get project details)',
      method: 'POST',
      url: `${COOKIE_API_BASE_URL}/v3/project`,
      body: {
        "slug": "bitcoin"
      }
    }
  ];

  console.log('\n🧪 Testing updated API endpoints...');
  
  for (const testCase of testCases) {
    try {
      console.log(`\n🔄 Testing: ${testCase.name}`);
      
      const requestOptions = {
        method: testCase.method,
        headers: {
          'Authorization': `Bearer ${COOKIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
      };

      if (testCase.body) {
        requestOptions.body = JSON.stringify(testCase.body);
        console.log(`📋 Body: ${JSON.stringify(testCase.body, null, 2)}`);
      }
      
      const response = await fetch(testCase.url, requestOptions);

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Success!`);
        
        // Show response structure
        console.log(`📋 Response keys: ${Object.keys(data).join(', ')}`);
        
        // Show data count if it's an array
        if (data.data && Array.isArray(data.data)) {
          console.log(`📊 Data count: ${data.data.length}`);
          
          // Show first item if available
          if (data.data[0]) {
            const item = data.data[0];
            console.log(`📈 First item: ${item.name || item.slug || 'Unknown'}`);
            if (item.mindshare) console.log(`   Mindshare: ${item.mindshare}`);
            if (item.sector) console.log(`   Sector: ${item.sector}`);
          }
        }
        
      } else {
        const errorText = await response.text();
        console.log(`❌ Error: ${errorText}`);
        
        // Try to parse error as JSON
        try {
          const errorJson = JSON.parse(errorText);
          console.log(`📋 Error details: ${JSON.stringify(errorJson, null, 2)}`);
        } catch (e) {
          // Error text is not JSON
        }
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n📋 Test Summary:');
  console.log('✅ Working endpoints can be used in the application');
  console.log('❌ Failed endpoints may need different authentication or have changed');
}

testUpdatedEndpoints().catch(console.error);
