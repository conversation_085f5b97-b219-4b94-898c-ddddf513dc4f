#!/usr/bin/env node

// Test Cookie.fun mindshare API specifically
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const COOKIE_API_BASE_URL = 'https://api.cookie.fun';

console.log('🍪 Testing Cookie.fun Mindshare API...');
console.log('🔑 API Key configured:', !!COOKIE_API_KEY);

async function testMindshareAPI() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const url = `${COOKIE_API_BASE_URL}/v3/project/search`;
  
  // Test the exact format from Cookie.fun documentation
  const testCases = [
    {
      name: 'Mindshare - All sectors',
      body: {
        mindshareTimeframe: '_30Days',
        sortBy: 'Mindshare',
        sortOrder: 'Descending'
      }
    },
    {
      name: 'Mindshare - DeFi sector',
      body: {
        mindshareTimeframe: '_30Days',
        sortBy: 'Mindshare',
        sortOrder: 'Descending',
        sectorSlug: 'defi'
      }
    },
    {
      name: 'Search with query',
      body: {
        searchQuery: 'bitcoin'
      }
    },
    {
      name: 'Metrics query',
      body: {
        searchQuery: 'bitcoin OR $BTC',
        metricType: 'Impressions',
        granulation: '_24Hours'
      }
    }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n🔄 Testing: ${testCase.name}`);
      console.log(`📋 Body: ${JSON.stringify(testCase.body, null, 2)}`);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${COOKIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.body)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Success: Found ${data?.data?.length || 0} projects`);
        
        // Show first project if available
        if (data?.data?.[0]) {
          const project = data.data[0];
          console.log(`📈 Top project: ${project.name} (Mindshare: ${project.mindshare || 'N/A'})`);
        }
      } else {
        const errorText = await response.text();
        console.log(`❌ Error: ${errorText}`);
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
}

testMindshareAPI().catch(console.error);
