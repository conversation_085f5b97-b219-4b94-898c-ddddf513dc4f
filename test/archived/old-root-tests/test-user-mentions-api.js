#!/usr/bin/env node

/**
 * Test script for TwitterAPI.io user mentions endpoint
 * Tests the exact API spec: GET /twitter/user/mentions
 */

// Use built-in fetch (Node.js 18+)
// Directly use the API key from .env file
const TWITTER_API_KEY = '96e428bef7fa4f078dab3b6c9678b774';
const BASE_URL = 'https://api.twitterapi.io';

if (!TWITTER_API_KEY) {
  console.error('❌ TWITTER_API_KEY not found in environment variables');
  process.exit(1);
}

async function testUserMentions() {
  console.log('🧪 Testing TwitterAPI.io User Mentions Endpoint');
  console.log('='.repeat(50));

  // Test different usernames - you can modify these
  const testUsers = [
    'elonmusk',  // High-volume account
    'vercel',    // Tech company account
    'github'     // Another tech account
  ];

  for (const userName of testUsers) {
    console.log(`\n🐦 Testing mentions for @${userName}`);
    console.log('-'.repeat(30));

    try {
      const url = new URL(`${BASE_URL}/twitter/user/mentions`);
      url.searchParams.append('userName', userName);
      
      console.log(`📡 Request URL: ${url.toString()}`);
      
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'X-API-Key': TWITTER_API_KEY,
          'Content-Type': 'application/json',
        },
      });

      console.log(`📈 Status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`❌ API Error: ${errorText}`);
        continue;
      }

      const data = await response.json();
      
      // Validate response structure according to API spec
      console.log('📊 Response Structure:');
      console.log(`   tweets: ${Array.isArray(data.tweets) ? `Array[${data.tweets.length}]` : 'Invalid'}`);
      console.log(`   has_next_page: ${typeof data.has_next_page === 'boolean' ? data.has_next_page : 'Invalid'}`);
      console.log(`   next_cursor: ${typeof data.next_cursor === 'string' ? `"${data.next_cursor}"` : 'Invalid'}`);
      console.log(`   status: ${data.status}`);
      console.log(`   message: ${data.message || 'N/A'}`);

      if (data.tweets && data.tweets.length > 0) {
        console.log('\n📝 Sample Tweet Structure:');
        const tweet = data.tweets[0];
        console.log(`   id: ${tweet.id}`);
        console.log(`   text: "${tweet.text?.substring(0, 50)}..."`);
        console.log(`   createdAt: ${tweet.createdAt}`);
        console.log(`   author.userName: @${tweet.author?.userName}`);
        console.log(`   author.name: ${tweet.author?.name}`);
        console.log(`   likeCount: ${tweet.likeCount}`);
        console.log(`   retweetCount: ${tweet.retweetCount}`);
        
        // Test pagination if available
        if (data.has_next_page && data.next_cursor) {
          console.log('\n🔄 Testing pagination...');
          await testPagination(userName, data.next_cursor);
        }
      } else {
        console.log('ℹ️  No mentions found for this user');
      }

      console.log('✅ Test completed successfully');

    } catch (error) {
      console.error(`❌ Test failed for @${userName}:`, error.message);
    }
  }
}

async function testPagination(userName, cursor) {
  try {
    const url = new URL(`${BASE_URL}/twitter/user/mentions`);
    url.searchParams.append('userName', userName);
    url.searchParams.append('cursor', cursor);
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`   📄 Page 2: ${data.tweets?.length || 0} tweets`);
      console.log(`   📄 Has more: ${data.has_next_page}`);
    } else {
      console.log('   ⚠️  Pagination test failed');
    }
  } catch (error) {
    console.log('   ⚠️  Pagination error:', error.message);
  }
}

async function testTimeFiltering() {
  console.log('\n⏰ Testing time filtering parameters');
  console.log('-'.repeat(30));

  try {
    const userName = 'vercel';
    const oneDayAgo = Math.floor((Date.now() - 24 * 60 * 60 * 1000) / 1000);
    
    const url = new URL(`${BASE_URL}/twitter/user/mentions`);
    url.searchParams.append('userName', userName);
    url.searchParams.append('sinceTime', oneDayAgo.toString());
    
    console.log(`📡 Testing sinceTime filter (last 24h) for @${userName}`);
    
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Time filter test: ${data.tweets?.length || 0} recent tweets`);
    } else {
      console.log('❌ Time filter test failed');
    }
  } catch (error) {
    console.error('❌ Time filter error:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Twitter User Mentions API Tests\n');
  
  await testUserMentions();
  await testTimeFiltering();
  
  console.log('\n🏁 All tests completed!');
}

runAllTests().catch(console.error);