/**
 * Test script for Twitter Mention Sync functionality
 * 
 * This script tests:
 * 1. TwitterAPI.io user mentions endpoint
 * 2. Mention sync service functionality
 * 3. API endpoints for syncing mentions
 */

const fetch = require('node-fetch');

// Configuration
const TWITTER_API_KEY = process.env.TWITTER_API_KEY || '96e428bef7fa4f078dab3b6c9678b774';
const TWITTER_API_BASE_URL = 'https://api.twitterapi.io';
const SERVER_BASE_URL = 'http://localhost:3000';

/**
 * Test TwitterAPI.io mentions endpoint directly
 */
async function testTwitterAPIMentions() {
  console.log('🐦 Testing TwitterAPI.io user mentions endpoint...');
  
  try {
    const url = new URL(`${TWITTER_API_BASE_URL}/twitter/user/mentions`);
    url.searchParams.append('username', 'elonmusk'); // Test with a popular account
    url.searchParams.append('count', '5');

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    console.log(`📄 Response status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ TwitterAPI.io mentions endpoint working!');
      console.log(`📊 Found ${data.data?.length || 0} mentions`);
      
      if (data.data && data.data.length > 0) {
        console.log('📝 Sample mention:', {
          id: data.data[0].id,
          text: data.data[0].text?.substring(0, 100) + '...',
          author: data.data[0].author?.userName,
          created_at: data.data[0].created_at,
        });
      }
      
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ TwitterAPI.io error:', errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ TwitterAPI.io test failed:', error.message);
    return false;
  }
}

/**
 * Test our sync API endpoint
 */
async function testSyncAPI() {
  console.log('\n🔄 Testing sync API endpoint...');
  
  try {
    const response = await fetch(`${SERVER_BASE_URL}/api/sync/mentions`, {
      method: 'GET', // Test the status endpoint first
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`📄 Sync API status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Sync API endpoint accessible!');
      console.log('📊 Stats:', data.stats);
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ Sync API error:', errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ Sync API test failed:', error.message);
    return false;
  }
}

/**
 * Test tRPC endpoints (requires authentication)
 */
async function testTRPCEndpoints() {
  console.log('\n🔗 Testing tRPC endpoints...');
  
  try {
    // Test the health check endpoint first
    const healthResponse = await fetch(`${SERVER_BASE_URL}/trpc/healthCheck`, {
      method: 'GET',
    });

    console.log(`📄 Health check status: ${healthResponse.status}`);
    
    if (healthResponse.ok) {
      const data = await healthResponse.json();
      console.log('✅ tRPC server accessible!');
      console.log('📊 Health status:', data.result?.data);
      return true;
    } else {
      console.log('❌ tRPC server not accessible');
      return false;
    }
  } catch (error) {
    console.error('❌ tRPC test failed:', error.message);
    return false;
  }
}

/**
 * Test user info endpoint for validation
 */
async function testUserInfoEndpoint() {
  console.log('\n👤 Testing user info endpoint...');
  
  try {
    const url = new URL(`${TWITTER_API_BASE_URL}/twitter/user/info`);
    url.searchParams.append('userName', 'elonmusk');

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'X-API-Key': TWITTER_API_KEY,
        'Content-Type': 'application/json',
      },
    });

    console.log(`📄 User info status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ User info endpoint working!');
      console.log('👤 User data:', {
        id: data.data?.id,
        userName: data.data?.userName,
        name: data.data?.name,
        followers: data.data?.followers,
        verified: data.data?.isVerified,
      });
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ User info error:', errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ User info test failed:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting Twitter Mention Sync Tests\n');
  
  const results = {
    twitterAPIMentions: await testTwitterAPIMentions(),
    userInfo: await testUserInfoEndpoint(),
    syncAPI: await testSyncAPI(),
    tRPCEndpoints: await testTRPCEndpoints(),
  };
  
  console.log('\n📊 Test Summary:');
  console.log('================');
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.values(results).length;
  
  console.log(`\n🎯 Overall: ${passedCount}/${totalCount} tests passed`);
  
  if (passedCount === totalCount) {
    console.log('🎉 All tests passed! Twitter mention sync is ready to use.');
  } else {
    console.log('⚠️ Some tests failed. Check the logs above for details.');
  }
  
  console.log('\n📝 Next Steps:');
  console.log('1. Make sure your server is running: pnpm dev:server');
  console.log('2. Add monitored accounts via the UI');
  console.log('3. Use the "Sync Mentions" button to test the feature');
  console.log('4. Set up a cron job to call /api/sync/mentions for automatic syncing');
}

// Run tests
runAllTests().catch(console.error);