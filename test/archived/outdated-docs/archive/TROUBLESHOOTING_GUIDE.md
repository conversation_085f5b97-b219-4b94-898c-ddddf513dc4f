# BuddyChip Troubleshooting Guide

## 🚨 **Database Connection Issues**

### Symptoms:
- 500 Internal Server Errors
- `PrismaClientInitializationError`
- "Can't reach database server" messages
- Authentication failures

### Quick Fix Checklist:
1. **Check Environment Variables**:
   ```bash
   # Verify these exist in both apps/server/.env and apps/web/.env
   DATABASE_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:6543/postgres"
   DIRECT_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:5432/postgres"
   ```

2. **Verify Supabase Project Status**:
   - Check if project is active in Supabase dashboard
   - Verify pooler is enabled and using port 6543

3. **Regenerate Prisma Client**:
   ```bash
   cd apps/server && bun x prisma generate --schema ./prisma/schema/schema.prisma
   cd apps/web && bun x prisma generate --schema ./prisma/schema/schema.prisma
   ```

4. **Restart Application**:
   ```bash
   # Kill all processes and restart
   pkill -f "next dev"
   # Then restart your dev servers
   ```

## 🔐 **Authentication Issues**

### Symptoms:
- Users can't sign in
- "User not found" errors
- Context creation failures

### Quick Fix:
1. **Check Clerk Configuration**:
   ```bash
   # Verify these exist in .env files
   NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
   CLERK_SECRET_KEY=sk_test_...
   ```

2. **Verify User Creation**:
   - Check if default subscription plan exists
   - Ensure user service is working properly

## 📊 **Rate Limiting Issues**

### Symptoms:
- "Feature limit exceeded" errors
- Blocked API calls
- Usage warnings

### Quick Check:
```sql
-- Check user's current usage
SELECT ul.feature, SUM(ul.amount) as total_usage 
FROM usage_logs ul 
WHERE ul."userId" = 'USER_ID' AND ul."billingPeriod" = '2025-06' 
GROUP BY ul.feature;

-- Check user's plan limits
SELECT pf.feature, pf."limit" 
FROM plan_features pf 
JOIN users u ON u."planId" = pf."planId" 
WHERE u.id = 'USER_ID';
```

## 🔧 **Development Commands**

### Database Operations:
```bash
# Generate Prisma client
bun x prisma generate --schema ./prisma/schema/schema.prisma

# Push schema changes
bun x prisma db push --schema ./prisma/schema/schema.prisma

# Open Prisma Studio
bun x prisma studio --schema ./prisma/schema/schema.prisma

# Reset database (careful!)
bun x prisma migrate reset --schema ./prisma/schema/schema.prisma
```

### Debugging:
```bash
# Enable verbose logging
VERBOSE_LOGGING=true
ENABLE_PRISMA_QUERY_LOGS=true
ENABLE_CONTEXT_LOGS=true
ENABLE_TRPC_REQUEST_LOGS=true
```

## 🚀 **Performance Issues**

### Symptoms:
- Slow API responses
- Database timeouts
- High memory usage

### Solutions:
1. **Check Connection Pooling**: Ensure using pooler URL (port 6543)
2. **Monitor Query Performance**: Enable Prisma query logs
3. **Review Database Indexes**: Check if queries are optimized
4. **Check Rate Limits**: Verify not hitting Supabase limits

## 📱 **Frontend Issues**

### Symptoms:
- tRPC calls failing
- UI not loading
- Network errors

### Quick Fixes:
1. **Check API Endpoints**: Verify tRPC routes are accessible
2. **Clear Browser Cache**: Hard refresh (Cmd+Shift+R)
3. **Check Network Tab**: Look for failed requests
4. **Verify Environment**: Ensure correct API URLs

## 🔍 **Debugging Tools**

### Console Logs to Watch:
```
🔍 DB-Utils: DATABASE_URL exists: true
✅ DB-Utils: Database connection successful
👤 Context: Auth result: { userId: 'user_...' }
✅ UserService: Just-in-time user creation: user_...
```

### Useful Database Queries:
```sql
-- Check active users
SELECT id, email, name, "lastActiveAt" FROM users ORDER BY "lastActiveAt" DESC;

-- Check subscription plans
SELECT name, "displayName", "isActive" FROM subscription_plans;

-- Check recent usage
SELECT * FROM usage_logs ORDER BY "createdAt" DESC LIMIT 10;

-- Check monitored accounts
SELECT "twitterHandle", "isActive", "lastCheckedAt" FROM monitored_accounts;
```

## 🆘 **Emergency Contacts**

### When to Escalate:
- Database completely inaccessible
- Supabase project issues
- Authentication system down
- Data corruption suspected

### Recovery Steps:
1. **Check Supabase Status**: https://status.supabase.com/
2. **Verify Environment Variables**: Ensure no accidental changes
3. **Review Recent Changes**: Check git history for breaking changes
4. **Backup Check**: Verify recent backups exist

---

**Last Updated**: June 20, 2025  
**Version**: 1.0  
**Maintainer**: Development Team
