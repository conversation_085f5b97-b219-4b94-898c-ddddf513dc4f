-- =====================================================
-- Crypto Caching Tables Migration for Supabase
-- =====================================================
-- This migration creates tables and functions for caching crypto data
-- with automatic expiration and cleanup mechanisms.

-- Create crypto_sectors_cache table
-- Purpose: Cache crypto sector data to reduce API calls and improve performance
CREATE TABLE IF NOT EXISTS crypto_sectors_cache (
    id SERIAL PRIMARY KEY,
    data JSONB NOT NULL, -- Stores the actual sector data as JSON
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Cache expiration timestamp
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create crypto_trending_cache table  
-- Purpose: Cache trending crypto data by sector and timeframe
CREATE TABLE IF NOT EXISTS crypto_trending_cache (
    id SERIAL PRIMARY KEY,
    sector_slug VARCHAR(50), -- Sector identifier (nullable for all-sectors queries)
    timeframe VARCHAR(20) NOT NULL, -- Time range: '1h', '24h', '7d', '30d', etc.
    data JSONB NOT NULL, -- Stores the trending crypto data as JSON
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL, -- Cache expiration timestamp
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for efficient cleanup of expired entries in sectors cache
CREATE INDEX IF NOT EXISTS idx_crypto_sectors_cache_expires_at 
ON crypto_sectors_cache (expires_at);

-- Index for efficient cleanup of expired entries in trending cache
CREATE INDEX IF NOT EXISTS idx_crypto_trending_cache_expires_at 
ON crypto_trending_cache (expires_at);

-- Composite index for fast lookups in trending cache
-- This allows efficient queries by sector + timeframe + expiration status
CREATE INDEX IF NOT EXISTS idx_crypto_trending_cache_lookup 
ON crypto_trending_cache (sector_slug, timeframe, expires_at);

-- =====================================================
-- CONSTRAINTS
-- =====================================================

-- Unique constraint to prevent duplicate cache entries for trending data
-- This ensures we don't cache the same sector/timeframe combination multiple times
CREATE UNIQUE INDEX IF NOT EXISTS idx_crypto_trending_cache_unique 
ON crypto_trending_cache (
    COALESCE(sector_slug, ''), -- Handle nullable sector_slug
    timeframe
) WHERE expires_at > NOW(); -- Only apply to non-expired entries

-- =====================================================
-- AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for crypto_sectors_cache
DROP TRIGGER IF EXISTS update_crypto_sectors_cache_updated_at ON crypto_sectors_cache;
CREATE TRIGGER update_crypto_sectors_cache_updated_at
    BEFORE UPDATE ON crypto_sectors_cache
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger for crypto_trending_cache  
DROP TRIGGER IF EXISTS update_crypto_trending_cache_updated_at ON crypto_trending_cache;
CREATE TRIGGER update_crypto_trending_cache_updated_at
    BEFORE UPDATE ON crypto_trending_cache
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- CLEANUP FUNCTIONS
-- =====================================================

-- Function to clean up expired cache entries
-- This should be called periodically to maintain database performance
CREATE OR REPLACE FUNCTION cleanup_expired_crypto_cache()
RETURNS TABLE(
    sectors_deleted INTEGER,
    trending_deleted INTEGER
) AS $$
DECLARE
    sectors_count INTEGER;
    trending_count INTEGER;
BEGIN
    -- Clean up expired sectors cache entries
    DELETE FROM crypto_sectors_cache 
    WHERE expires_at <= NOW();
    GET DIAGNOSTICS sectors_count = ROW_COUNT;
    
    -- Clean up expired trending cache entries
    DELETE FROM crypto_trending_cache 
    WHERE expires_at <= NOW();
    GET DIAGNOSTICS trending_count = ROW_COUNT;
    
    -- Return counts of deleted rows
    RETURN QUERY SELECT sectors_count, trending_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to get cache statistics
CREATE OR REPLACE FUNCTION get_crypto_cache_stats()
RETURNS TABLE(
    table_name TEXT,
    total_entries BIGINT,
    expired_entries BIGINT,
    active_entries BIGINT,
    oldest_entry TIMESTAMP WITH TIME ZONE,
    newest_entry TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Stats for sectors cache
    RETURN QUERY
    SELECT 
        'crypto_sectors_cache'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(*) FILTER (WHERE expires_at <= NOW())::BIGINT,
        COUNT(*) FILTER (WHERE expires_at > NOW())::BIGINT,
        MIN(created_at),
        MAX(created_at)
    FROM crypto_sectors_cache;
    
    -- Stats for trending cache
    RETURN QUERY
    SELECT 
        'crypto_trending_cache'::TEXT,
        COUNT(*)::BIGINT,
        COUNT(*) FILTER (WHERE expires_at <= NOW())::BIGINT,
        COUNT(*) FILTER (WHERE expires_at > NOW())::BIGINT,
        MIN(created_at),
        MAX(created_at)
    FROM crypto_trending_cache;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS ON TABLES AND COLUMNS
-- =====================================================

COMMENT ON TABLE crypto_sectors_cache IS 'Cache table for crypto sector data to reduce API calls and improve response times';
COMMENT ON COLUMN crypto_sectors_cache.data IS 'JSON data containing sector information from external crypto APIs';
COMMENT ON COLUMN crypto_sectors_cache.expires_at IS 'Timestamp when this cache entry expires and should be refreshed';

COMMENT ON TABLE crypto_trending_cache IS 'Cache table for trending crypto data organized by sector and timeframe';
COMMENT ON COLUMN crypto_trending_cache.sector_slug IS 'Sector identifier (null for all-sectors queries)';
COMMENT ON COLUMN crypto_trending_cache.timeframe IS 'Time range for trending data (1h, 24h, 7d, 30d, etc.)';
COMMENT ON COLUMN crypto_trending_cache.data IS 'JSON data containing trending cryptocurrency information';
COMMENT ON COLUMN crypto_trending_cache.expires_at IS 'Timestamp when this cache entry expires and should be refreshed';

-- =====================================================
-- USAGE EXAMPLES (commented out)
-- =====================================================

/*
-- Example: Insert sectors cache data
INSERT INTO crypto_sectors_cache (data, expires_at)
VALUES (
    '{"sectors": [{"name": "DeFi", "market_cap": 1000000}]}',
    NOW() + INTERVAL '1 hour'
);

-- Example: Insert trending cache data
INSERT INTO crypto_trending_cache (sector_slug, timeframe, data, expires_at)
VALUES (
    'defi',
    '24h',
    '{"trending": [{"symbol": "ETH", "price": 2000}]}',
    NOW() + INTERVAL '30 minutes'
);

-- Example: Query active cache entries
SELECT * FROM crypto_trending_cache 
WHERE sector_slug = 'defi' 
AND timeframe = '24h' 
AND expires_at > NOW();

-- Example: Run cleanup
SELECT * FROM cleanup_expired_crypto_cache();

-- Example: Get cache statistics
SELECT * FROM get_crypto_cache_stats();
*/

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Print success message
DO $$
BEGIN
    RAISE NOTICE 'Crypto caching tables migration completed successfully!';
    RAISE NOTICE 'Tables created: crypto_sectors_cache, crypto_trending_cache';
    RAISE NOTICE 'Functions created: cleanup_expired_crypto_cache(), get_crypto_cache_stats()';
    RAISE NOTICE 'Indexes and triggers have been set up for optimal performance';
END $$;