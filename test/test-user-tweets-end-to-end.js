/**
 * End-to-end test for user tweets functionality
 * Run with: bun run test/test-user-tweets-end-to-end.js
 */

const { PrismaClient } = require('../apps/web/prisma/generated');
const { twitterClient } = require('../apps/web/src/lib/twitter-client');

async function testUserTweetsEndToEnd() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing user tweets end-to-end...');
    
    // Step 1: Clear cache to ensure fresh data
    console.log('🧹 Clearing Twitter API cache...');
    twitterClient.clearCache();
    
    // Step 2: Test direct API call
    console.log('\n📡 Step 1: Testing direct API call...');
    const directResult = await twitterClient.getUserLastTweets('reisnertobias', {
      includeReplies: false,
      includeRetweets: true,
    });
    
    console.log(`✅ Direct API call result:`);
    console.log(`   Tweets: ${directResult.tweets.length}`);
    console.log(`   Status: ${directResult.status}`);
    console.log(`   Has next page: ${directResult.has_next_page}`);
    
    if (directResult.tweets.length > 0) {
      console.log(`   Sample tweet: ${directResult.tweets[0].text.substring(0, 100)}...`);
    }
    
    // Step 3: Check database before sync
    console.log('\n📊 Step 2: Checking database before sync...');
    const beforeCount = await prisma.mention.count({
      where: {
        isUserTweet: true,
      },
    });
    console.log(`   User tweets in DB before: ${beforeCount}`);
    
    // Step 4: Test the sync service manually
    console.log('\n🔄 Step 3: Testing sync service...');
    
    // Get the account
    const account = await prisma.monitoredAccount.findFirst({
      where: {
        twitterHandle: 'reisnertobias',
        syncUserTweets: true,
        isActive: true,
      },
    });
    
    if (!account) {
      console.log('❌ No account found with syncUserTweets enabled');
      return;
    }
    
    console.log(`   Found account: @${account.twitterHandle} (syncUserTweets: ${account.syncUserTweets})`);
    
    // Manually call the same API as sync service
    const syncResult = await twitterClient.getUserLastTweets(account.twitterHandle, {
      limit: 20,
      includeReplies: account.syncReplies,
      includeRetweets: account.syncRetweets,
    });
    
    console.log(`   Sync API call result:`);
    console.log(`   Tweets: ${syncResult.tweets.length}`);
    console.log(`   Status: ${syncResult.status}`);
    
    // Step 5: Manually save one user tweet to test database insertion
    if (syncResult.tweets.length > 0) {
      console.log('\n💾 Step 4: Testing database insertion...');
      
      const testTweet = syncResult.tweets[0];
      console.log(`   Saving test tweet: ${testTweet.text.substring(0, 50)}...`);
      
      try {
        const savedMention = await prisma.mention.create({
          data: {
            tweetId: testTweet.id,
            content: testTweet.text,
            authorHandle: testTweet.author.userName,
            authorName: testTweet.author.name,
            authorAvatar: testTweet.author.profilePicture,
            url: testTweet.url,
            createdAt: new Date(testTweet.createdAt),
            accountId: account.id,
            isUserTweet: true, // This is the key field!
            bullishScore: 50,
            importanceScore: 50,
            priority: 'medium',
            keywordCount: 0,
          },
        });
        
        console.log(`   ✅ Successfully saved user tweet with ID: ${savedMention.id}`);
        
        // Verify it was saved correctly
        const verifyCount = await prisma.mention.count({
          where: {
            isUserTweet: true,
          },
        });
        console.log(`   ✅ Total user tweets in DB now: ${verifyCount}`);
        
      } catch (error) {
        console.error(`   ❌ Error saving tweet:`, error.message);
      }
    }
    
    console.log('\n🎉 End-to-end test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testUserTweetsEndToEnd();
