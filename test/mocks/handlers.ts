/**
 * MSW Request Handlers
 * 
 * Mock handlers for external API calls during testing
 */

import { http, HttpResponse } from 'msw'

// Mock data
const mockTwitterUser = {
  id: '123456789',
  userName: 'testuser',
  name: 'Test User',
  profilePicture: 'https://example.com/avatar.jpg',
  isBlueVerified: false,
  followers: 1000,
  following: 500,
  tweets: 250
}

const mockTweet = {
  id: '1234567890123456789',
  text: 'This is a test tweet mentioning @testhandle',
  author: mockTwitterUser,
  createdAt: '2024-01-15T10:30:00.000Z',
  replyCount: 5,
  retweetCount: 10,
  likeCount: 25,
  isReply: false,
  inReplyToId: null
}

const mockMentions = {
  data: [
    mockTweet,
    {
      ...mockTweet,
      id: '1234567890123456790',
      text: 'Another test mention for @testhandle',
      createdAt: '2024-01-15T11:00:00.000Z'
    }
  ],
  meta: {
    result_count: 2,
    newest_id: '1234567890123456790',
    oldest_id: '1234567890123456789'
  }
}

export const handlers = [
  // Twitter API handlers
  http.get('https://api.twitterapi.io/twitter/user/mentions', ({ request }) => {
    const url = new URL(request.url)
    const userName = url.searchParams.get('userName')
    
    console.log('🎭 MSW: Mocking Twitter mentions for:', userName)
    
    if (!userName) {
      return HttpResponse.json(
        { error: 'userName parameter is required' },
        { status: 400 }
      )
    }
    
    return HttpResponse.json(mockMentions)
  }),
  
  http.get('https://api.twitterapi.io/twitter/user/info', ({ request }) => {
    const url = new URL(request.url)
    const userName = url.searchParams.get('userName')
    
    console.log('🎭 MSW: Mocking Twitter user info for:', userName)
    
    if (!userName) {
      return HttpResponse.json(
        { error: 'userName parameter is required' },
        { status: 400 }
      )
    }
    
    return HttpResponse.json({
      data: mockTwitterUser
    })
  }),
  
  // OpenRouter API handlers
  http.post('https://openrouter.ai/api/v1/chat/completions', async ({ request }) => {
    const body = await request.json() as any
    
    console.log('🎭 MSW: Mocking OpenRouter chat completion')
    
    return HttpResponse.json({
      id: 'chatcmpl-test-123',
      object: 'chat.completion',
      created: Date.now(),
      model: body.model || 'google/gemini-2.0-flash-exp',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'This is a mock AI response for testing purposes.'
          },
          finish_reason: 'stop'
        }
      ],
      usage: {
        prompt_tokens: 50,
        completion_tokens: 20,
        total_tokens: 70
      }
    })
  }),
  
  // OpenAI API handlers
  http.post('https://api.openai.com/v1/chat/completions', async ({ request }) => {
    const body = await request.json() as any
    
    console.log('🎭 MSW: Mocking OpenAI chat completion')
    
    return HttpResponse.json({
      id: 'chatcmpl-test-456',
      object: 'chat.completion',
      created: Date.now(),
      model: body.model || 'gpt-4o',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'This is a mock OpenAI response for testing.'
          },
          finish_reason: 'stop'
        }
      ],
      usage: {
        prompt_tokens: 45,
        completion_tokens: 18,
        total_tokens: 63
      }
    })
  }),
  
  http.post('https://api.openai.com/v1/images/generations', async ({ request }) => {
    const body = await request.json() as any
    
    console.log('🎭 MSW: Mocking OpenAI image generation')
    
    return HttpResponse.json({
      created: Date.now(),
      data: [
        {
          url: 'https://example.com/mock-generated-image.png',
          revised_prompt: body.prompt || 'A mock generated image'
        }
      ]
    })
  }),
  
  // XAI (Grok) API handlers
  http.post('https://api.x.ai/v1/chat/completions', async ({ request }) => {
    const body = await request.json() as any
    
    console.log('🎭 MSW: Mocking XAI chat completion')
    
    return HttpResponse.json({
      id: 'chatcmpl-test-789',
      object: 'chat.completion',
      created: Date.now(),
      model: body.model || 'grok-beta',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'This is a mock XAI/Grok response for testing.'
          },
          finish_reason: 'stop'
        }
      ],
      usage: {
        prompt_tokens: 40,
        completion_tokens: 15,
        total_tokens: 55
      }
    })
  }),
  
  // Perplexity API handlers
  http.post('https://api.perplexity.ai/chat/completions', async ({ request }) => {
    const body = await request.json() as any
    
    console.log('🎭 MSW: Mocking Perplexity chat completion')
    
    return HttpResponse.json({
      id: 'chatcmpl-test-perplexity',
      object: 'chat.completion',
      created: Date.now(),
      model: body.model || 'llama-3.1-sonar-small-128k-online',
      choices: [
        {
          index: 0,
          message: {
            role: 'assistant',
            content: 'This is a mock Perplexity response with search capabilities.'
          },
          finish_reason: 'stop'
        }
      ],
      usage: {
        prompt_tokens: 35,
        completion_tokens: 22,
        total_tokens: 57
      }
    })
  }),
  
  // Exa API handlers
  http.post('https://api.exa.ai/search', async ({ request }) => {
    const body = await request.json() as any
    
    console.log('🎭 MSW: Mocking Exa search')
    
    return HttpResponse.json({
      results: [
        {
          id: 'test-result-1',
          title: 'Mock Search Result 1',
          url: 'https://example.com/result1',
          text: 'This is a mock search result for testing purposes.',
          score: 0.95,
          publishedDate: '2024-01-15'
        },
        {
          id: 'test-result-2',
          title: 'Mock Search Result 2',
          url: 'https://example.com/result2',
          text: 'Another mock search result with relevant information.',
          score: 0.87,
          publishedDate: '2024-01-14'
        }
      ],
      autopromptString: body.query || 'test query'
    })
  }),
  
  // UploadThing handlers
  http.post('https://uploadthing.com/api/uploadFiles', async ({ request }) => {
    console.log('🎭 MSW: Mocking UploadThing file upload')
    
    return HttpResponse.json({
      data: [
        {
          key: 'mock-file-key-123',
          url: 'https://utfs.io/f/mock-file-key-123',
          name: 'test-image.png',
          size: 1024000
        }
      ]
    })
  }),
  
  // Clerk API handlers
  http.get('https://api.clerk.com/v1/users/:userId', ({ params }) => {
    console.log('🎭 MSW: Mocking Clerk user fetch for:', params.userId)
    
    return HttpResponse.json({
      id: params.userId,
      email_addresses: [
        {
          email_address: '<EMAIL>',
          verification: { status: 'verified' }
        }
      ],
      first_name: 'Test',
      last_name: 'User',
      image_url: 'https://example.com/avatar.jpg',
      created_at: Date.now() - 86400000, // 1 day ago
      updated_at: Date.now()
    })
  }),
  
  // Fallback handler for unmatched requests
  http.all('*', ({ request }) => {
    console.warn('🎭 MSW: Unhandled request:', request.method, request.url)
    
    return HttpResponse.json(
      { error: 'Mock handler not found for this endpoint' },
      { status: 404 }
    )
  })
]
