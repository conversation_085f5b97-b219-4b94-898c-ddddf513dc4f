/**
 * Test script to check if monitored accounts API returns sync settings
 * Run with: bun run test/test-monitored-accounts-api.js
 */

const { PrismaClient } = require('../apps/web/prisma/generated');

async function testMonitoredAccountsAPI() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🧪 Testing monitored accounts API response...');
    
    // Get monitored accounts directly from database
    const accounts = await prisma.monitoredAccount.findMany({
      where: {
        userId: 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ',
      },
      orderBy: {
        createdAt: "desc",
      },
      include: {
        _count: {
          select: {
            mentions: true,
          },
        },
      },
    });

    console.log('📊 Raw database response:');
    accounts.forEach((account, i) => {
      console.log(`${i + 1}. @${account.twitterHandle}:`);
      console.log(`   syncMentions: ${account.syncMentions}`);
      console.log(`   syncUserTweets: ${account.syncUserTweets}`);
      console.log(`   syncReplies: ${account.syncReplies}`);
      console.log(`   syncRetweets: ${account.syncRetweets}`);
      console.log(`   mentionsCount: ${account._count.mentions}`);
      console.log('');
    });

    // Test the mapped response format (like the API does)
    const mappedAccounts = accounts.map((account) => ({
      id: account.id,
      handle: account.twitterHandle,
      displayName: account.displayName,
      avatar: account.avatarUrl,
      isActive: account.isActive,
      platform: "twitter",
      followerCount: 0,
      createdAt: account.createdAt,
      lastSyncAt: account.lastCheckedAt,
      mentionsCount: account._count.mentions,
      // Include sync settings
      syncMentions: account.syncMentions,
      syncUserTweets: account.syncUserTweets,
      syncReplies: account.syncReplies,
      syncRetweets: account.syncRetweets,
    }));

    console.log('📊 Mapped API response format:');
    mappedAccounts.forEach((account, i) => {
      console.log(`${i + 1}. @${account.handle}:`);
      console.log(`   syncMentions: ${account.syncMentions}`);
      console.log(`   syncUserTweets: ${account.syncUserTweets}`);
      console.log(`   syncReplies: ${account.syncReplies}`);
      console.log(`   syncRetweets: ${account.syncRetweets}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMonitoredAccountsAPI();
