/**
 * Test script to verify the new subscription system
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testSubscriptionSystem() {
  console.log('🧪 Testing BuddyChip Subscription System...\n');

  try {
    // Test 1: Check all subscription plans exist
    console.log('📋 Test 1: Checking subscription plans...');
    const plans = await prisma.subscriptionPlan.findMany({
      include: {
        features: true,
      },
      orderBy: {
        price: 'asc',
      },
    });

    console.log(`Found ${plans.length} subscription plans:`);
    plans.forEach(plan => {
      console.log(`  - ${plan.displayName}: $${plan.price}/month (${plan.features.length} features)`);
    });

    // Test 2: Verify plan structure
    console.log('\n🔍 Test 2: Verifying plan structure...');
    const expectedPlans = ['free', 'reply-guy', 'reply-god', 'team'];
    const actualPlans = plans.map(p => p.name);
    
    const missingPlans = expectedPlans.filter(p => !actualPlans.includes(p));
    if (missingPlans.length > 0) {
      console.error(`❌ Missing plans: ${missingPlans.join(', ')}`);
      return false;
    }
    console.log('✅ All expected plans found');

    // Test 3: Verify feature coverage
    console.log('\n🎯 Test 3: Verifying feature coverage...');
    const expectedFeatures = [
      'AI_CALLS',
      'IMAGE_GENERATIONS', 
      'MONITORED_ACCOUNTS',
      'MENTIONS_PER_MONTH',
      'MENTIONS_PER_SYNC',
      'MAX_TOTAL_MENTIONS',
      'STORAGE_GB',
      'TEAM_MEMBERS',
      'COOKIE_API_CALLS'
    ];

    for (const plan of plans) {
      const planFeatures = plan.features.map(f => f.feature);
      const missingFeatures = expectedFeatures.filter(f => !planFeatures.includes(f));
      
      if (missingFeatures.length > 0) {
        console.error(`❌ Plan ${plan.name} missing features: ${missingFeatures.join(', ')}`);
      } else {
        console.log(`✅ Plan ${plan.name} has all required features`);
      }
    }

    // Test 4: Verify pricing progression
    console.log('\n💰 Test 4: Verifying pricing progression...');
    const prices = plans.map(p => parseFloat(p.price.toString()));
    const expectedPrices = [0, 9, 29, 99];
    
    if (JSON.stringify(prices) === JSON.stringify(expectedPrices)) {
      console.log('✅ Pricing progression is correct');
    } else {
      console.error(`❌ Pricing mismatch. Expected: ${expectedPrices}, Got: ${prices}`);
    }

    // Test 5: Verify feature limits progression
    console.log('\n📊 Test 5: Verifying feature limits progression...');
    const freePlan = plans.find(p => p.name === 'free');
    const replyGuyPlan = plans.find(p => p.name === 'reply-guy');
    const replyGodPlan = plans.find(p => p.name === 'reply-god');
    const teamPlan = plans.find(p => p.name === 'team');

    if (freePlan && replyGuyPlan && replyGodPlan && teamPlan) {
      // Check AI_CALLS progression
      const freeAI = freePlan.features.find(f => f.feature === 'AI_CALLS')?.limit || 0;
      const guyAI = replyGuyPlan.features.find(f => f.feature === 'AI_CALLS')?.limit || 0;
      const godAI = replyGodPlan.features.find(f => f.feature === 'AI_CALLS')?.limit || 0;
      const teamAI = teamPlan.features.find(f => f.feature === 'AI_CALLS')?.limit || 0;

      console.log(`AI_CALLS progression: ${freeAI} → ${guyAI} → ${godAI} → ${teamAI === -1 ? '∞' : teamAI}`);
      
      if (freeAI < guyAI && guyAI < godAI && (teamAI === -1 || teamAI > godAI)) {
        console.log('✅ AI_CALLS limits progress correctly');
      } else {
        console.error('❌ AI_CALLS limits do not progress correctly');
      }

      // Check MONITORED_ACCOUNTS progression
      const freeAccounts = freePlan.features.find(f => f.feature === 'MONITORED_ACCOUNTS')?.limit || 0;
      const guyAccounts = replyGuyPlan.features.find(f => f.feature === 'MONITORED_ACCOUNTS')?.limit || 0;
      const godAccounts = replyGodPlan.features.find(f => f.feature === 'MONITORED_ACCOUNTS')?.limit || 0;
      const teamAccounts = teamPlan.features.find(f => f.feature === 'MONITORED_ACCOUNTS')?.limit || 0;

      console.log(`MONITORED_ACCOUNTS progression: ${freeAccounts} → ${guyAccounts} → ${godAccounts} → ${teamAccounts}`);
      
      if (freeAccounts < guyAccounts && guyAccounts < godAccounts && godAccounts < teamAccounts) {
        console.log('✅ MONITORED_ACCOUNTS limits progress correctly');
      } else {
        console.error('❌ MONITORED_ACCOUNTS limits do not progress correctly');
      }
    }

    console.log('\n🎉 Subscription system test completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSubscriptionSystem()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
  });
