#!/usr/bin/env node

/**
 * Test sending message directly through Telegram Bot API (bypassing our webhook)
 */

const https = require('https');

const BOT_TOKEN = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
const CHAT_ID = '904041730';

async function sendDirectMessage() {
  console.log('📤 Sending message directly through Telegram Bot API...');
  
  const messageData = {
    chat_id: CHAT_ID,
    text: `🧪 *Direct API Test*\n\nThis message was sent directly through Telegram Bot API, bypassing the webhook.\n\nTime: ${new Date().toLocaleString()}\n\n✅ If you see this, the bot token is working correctly!`,
    parse_mode: 'Markdown'
  };

  const data = JSON.stringify(messageData);

  const options = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/sendMessage`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

async function getBotInfo() {
  console.log('🤖 Getting bot information...');
  
  const options = {
    hostname: 'api.telegram.org',
    port: 443,
    path: `/bot${BOT_TOKEN}/getMe`,
    method: 'GET'
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function main() {
  try {
    // Get bot info first
    const botInfo = await getBotInfo();
    console.log('📊 Bot Info:', JSON.stringify(botInfo.data, null, 2));
    
    if (!botInfo.data.ok) {
      console.error('❌ Bot token is invalid!');
      return;
    }
    
    console.log(`✅ Bot is valid: @${botInfo.data.result.username}`);
    console.log();
    
    // Send test message
    const result = await sendDirectMessage();
    console.log('📊 Send Message Result:', JSON.stringify(result.data, null, 2));
    
    if (result.data.ok) {
      console.log('✅ Message sent successfully!');
      console.log('💡 Check your Telegram for the direct API test message.');
    } else {
      console.log('❌ Failed to send message:', result.data.description);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

main();