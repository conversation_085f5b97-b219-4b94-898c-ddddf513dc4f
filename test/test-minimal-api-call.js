/**
 * Test the most minimal API call possible
 * Run with: bun run test/test-minimal-api-call.js
 */

const { twitterClient } = require('../apps/web/src/lib/twitter-client');

async function testMinimalAPICall() {
  try {
    console.log('🧪 Testing minimal API call...');
    
    // Clear cache
    twitterClient.clearCache();
    
    // Test with just userName parameter (no optional params)
    console.log('\n📋 Testing with minimal parameters...');
    
    const result = await twitterClient.getUserLastTweets('elonmusk', {
      // No optional parameters at all
    });
    
    console.log(`✅ Minimal API call result:`);
    console.log(`   Tweets: ${result.tweets.length}`);
    console.log(`   Status: ${result.status}`);
    console.log(`   Has next page: ${result.has_next_page}`);
    console.log(`   Message: ${result.message || 'none'}`);
    
    if (result.tweets.length > 0) {
      console.log(`   ✅ SUCCESS! Found tweets!`);
      console.log(`   Sample: ${result.tweets[0].text.substring(0, 100)}...`);
    } else {
      console.log(`   ❌ Still no tweets returned`);
      
      // Let's also try with includeReplies: true to see if that helps
      console.log('\n📋 Testing with includeReplies: true...');
      
      const resultWithReplies = await twitterClient.getUserLastTweets('elonmusk', {
        includeReplies: true,
      });
      
      console.log(`   With replies - Tweets: ${resultWithReplies.tweets.length}`);
      
      if (resultWithReplies.tweets.length > 0) {
        console.log(`   ✅ SUCCESS with replies! Found tweets!`);
        console.log(`   Sample: ${resultWithReplies.tweets[0].text.substring(0, 100)}...`);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMinimalAPICall();
