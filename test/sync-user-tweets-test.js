/**
 * Test script to verify user tweets sync functionality
 * Run with: node test/sync-user-tweets-test.js
 */

const { PrismaClient } = require('../apps/web/prisma/generated');
const { MentionSyncService } = require('../apps/web/src/lib/mention-sync-service');

async function testUserTweetsSync() {
  const prisma = new PrismaClient();
  const syncService = new MentionSyncService(prisma);
  
  try {
    console.log('🧪 Testing user tweets sync functionality...');
    
    // Get a monitored account with syncUserTweets enabled
    const account = await prisma.monitoredAccount.findFirst({
      where: {
        userId: 'user_2y2xJLvi2ncFa8bEKWJmb94DPTZ',
        syncUserTweets: true,
        isActive: true,
      },
    });
    
    if (!account) {
      console.log('❌ No monitored account found with syncUserTweets enabled');
      return;
    }
    
    console.log(`📋 Found account: @${account.twitterHandle} (syncUserTweets: ${account.syncUserTweets})`);
    
    // Check current mentions count
    const beforeCount = await prisma.mention.count({
      where: {
        accountId: account.id,
        isUserTweet: true,
      },
    });
    
    console.log(`📊 Current user tweets in DB: ${beforeCount}`);
    
    // Trigger sync
    console.log('🔄 Triggering sync...');
    const result = await syncService.syncAccountMentions(account.id, account.userId);
    
    console.log('📊 Sync result:', result);
    
    // Check after sync
    const afterCount = await prisma.mention.count({
      where: {
        accountId: account.id,
        isUserTweet: true,
      },
    });
    
    console.log(`📊 User tweets after sync: ${afterCount}`);
    console.log(`📈 New user tweets added: ${afterCount - beforeCount}`);
    
    // Show some sample user tweets
    const userTweets = await prisma.mention.findMany({
      where: {
        accountId: account.id,
        isUserTweet: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 3,
    });
    
    console.log('\n📝 Sample user tweets:');
    userTweets.forEach((tweet, i) => {
      console.log(`${i + 1}. @${tweet.authorHandle}: ${tweet.content.substring(0, 100)}...`);
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testUserTweetsSync();
