{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"], "env": ["NODE_ENV", "DATABASE_URL", "DIRECT_URL", "CLERK_SECRET_KEY", "OPENROUTER_API_KEY", "OR_SITE_URL", "OR_APP_NAME", "OPENAI_API_KEY", "XAI_API_KEY", "PERPLEXITY_API_KEY", "EXA_API_KEY", "UPLOADTHING_TOKEN", "TWITTER_API_KEY", "SUPABASE_URL", "SUPABASE_ANON_KEY", "SUPABASE_SERVICE_ROLE", "MEM0_API_KEY", "KV_URL", "KV_TOKEN", "SENTRY_AUTH_TOKEN", "VERBOSE_LOGGING", "ENABLE_PRISMA_QUERY_LOGS", "ENABLE_CONTEXT_LOGS", "ENABLE_TRPC_REQUEST_LOGS", "CLERK_WEBHOOK_SIGNING_SECRET", "CORS_ORIGIN", "TELEGRAM_BOT_TOKEN", "TELEGRAM_WEBHOOK_SECRET", "COOKIE_API_KEY"]}, "lint": {"dependsOn": ["^lint"]}, "lint:fix": {"dependsOn": ["^lint:fix"]}, "format": {"dependsOn": ["^format"]}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:push": {"cache": false, "persistent": true}, "db:studio": {"cache": false, "persistent": true}, "db:migrate": {"cache": false, "persistent": true}, "db:generate": {"cache": false, "persistent": true}}}