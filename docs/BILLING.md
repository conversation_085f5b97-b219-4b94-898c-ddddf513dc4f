# BuddyChip Billing System Documentation

## 📋 Overview

BuddyChip uses a hybrid billing system that supports both legacy subscription plans and Clerk billing integration. This document provides a comprehensive guide to understanding and implementing the billing system.

## 🏗️ Architecture

### Billing System Components

1. **Database Layer** - Subscription plans, features, and usage tracking
2. **Business Logic** - Feature gating, usage limits, and plan management
3. **Payment Processing** - Clerk billing integration (planned)
4. **User Interface** - Pricing pages, upgrade prompts, and billing management

### Database Schema

```sql
-- Core subscription tables
subscription_plans (id, name, displayName, price, isActive)
plan_features (planId, feature, limit)
usage_logs (userId, feature, amount, billingPeriod)
users (clerkPlanId, clerkPlanName, subscriptionStatus)
```

## 💰 Subscription Plans

### Current Plan Structure

| Plan | Price | AI Calls | Images | Accounts | Storage | Team Members |
|------|-------|----------|--------|----------|---------|--------------|
| **Free** | $0/month | 50 | 5 | 1 | 1GB | 1 |
| **Reply Guy** | $9/month | 1,000 | 20 | 3 | 1GB | 1 |
| **Reply God** | $29/month | 5,000 | 100 | 10 | 5GB | 3 |
| **Team** | $99/month | Unlimited | Unlimited | 50 | 20GB | 10 |

### Feature Types

- `AI_CALLS` - Monthly AI response generations
- `IMAGE_GENERATIONS` - Monthly image creations
- `MONITORED_ACCOUNTS` - Number of Twitter accounts to monitor
- `MENTIONS_PER_MONTH` - Monthly mention processing limit
- `STORAGE_GB` - Storage space in gigabytes
- `TEAM_MEMBERS` - Number of team members allowed
- `COOKIE_API_CALLS` - Monthly crypto data API calls

## 🔧 Implementation Status

### ✅ Completed Components

1. **Database Schema** - Fully implemented with proper relationships
2. **Feature Gating** - `FeatureGate` component restricts access
3. **Usage Tracking** - Real-time usage monitoring and limits
4. **Plan Management** - tRPC endpoints for subscription operations
5. **UI Components** - Pricing section, upgrade prompts, billing management
6. **Dual Billing Support** - Legacy plans + Clerk billing preparation

### ⚠️ Pending Implementation

1. **Payment Processing** - Clerk billing API integration
2. **Webhook Handlers** - Real subscription event processing
3. **Billing Portal** - Customer billing management interface
4. **Invoice Management** - Payment history and receipts

## 🚀 Usage Examples

### Check Feature Access

```typescript
import { trpc } from "@/lib/trpc";

// Check if user can use a feature
const { data: canUseFeature } = trpc.billing.canUseFeature.useQuery({
  feature: "AI_CALLS"
});

if (canUseFeature?.allowed) {
  // User can use the feature
} else {
  // Show upgrade prompt
}
```

### Feature Gating Component

```tsx
import FeatureGate from "@/components/feature-gate";

<FeatureGate feature="MONITORED_ACCOUNTS">
  <MonitoredAccountsComponent />
</FeatureGate>
```

### Get Current Subscription

```typescript
const { data: subscription } = trpc.billing.getSubscription.useQuery();

const currentPlan = subscription?.isClerkBilling 
  ? subscription.clerkPlan.name 
  : subscription?.legacyPlan.name;
```

## 🔄 Upgrade Flow

### Current Implementation

1. User clicks "Upgrade" button
2. `changePlan` mutation is called
3. Redirects to pricing page (placeholder)
4. **TODO**: Integrate with Clerk billing API

### Planned Flow

1. User selects plan on pricing page
2. Clerk billing API creates subscription
3. Webhook updates user's plan in database
4. User gains immediate access to new features

## 📊 Usage Tracking

### How It Works

1. **Feature Usage** - Each feature use is logged to `usage_logs`
2. **Billing Period** - Usage is tracked per month (e.g., "2024-01")
3. **Limit Checking** - Real-time validation against plan limits
4. **Reset Cycle** - Usage resets at the start of each billing period

### Usage Monitoring

```typescript
// Get current usage for all features
const { data: usage } = trpc.billing.getUsage.useQuery();

usage?.forEach(feature => {
  console.log(`${feature.feature}: ${feature.currentUsage}/${feature.limit}`);
});
```

## 🔐 Security & Access Control

### Feature Gating Strategy

- **Server-side validation** - All feature access checked in tRPC procedures
- **Client-side UI** - Components hidden/disabled based on plan limits
- **Database constraints** - Usage limits enforced at the data layer

### Rate Limiting

Plans include rate limiting per minute:
- Free: 5 requests/minute
- Reply Guy: 10 requests/minute  
- Reply God: 30 requests/minute
- Team: 60 requests/minute

## 🛠️ Development Guide

### Adding New Features

1. **Add feature type** to `FeatureType` enum in Prisma schema
2. **Update plan limits** in database via migration
3. **Add feature gating** in relevant components
4. **Update usage tracking** in feature implementation

### Testing Billing

```bash
# Test subscription functionality
npm run test:billing

# Test feature gating
npm run test:features

# Test usage tracking
npm run test:usage
```

## 🚨 Troubleshooting

### Common Issues

1. **User not found** - Ensure JIT user creation is working
2. **Feature access denied** - Check plan limits and usage logs
3. **Usage not tracking** - Verify `logUsage` calls in feature code
4. **Plan not updating** - Check Clerk webhook configuration

### Debug Commands

```typescript
// Check user's current plan and usage
const user = await prisma.user.findUnique({
  where: { id: userId },
  include: { plan: { include: { features: true } } }
});

// Check usage logs
const usage = await prisma.usageLog.findMany({
  where: { userId, billingPeriod: "2024-01" }
});
```

## 🔮 Future Enhancements

### Planned Features

1. **Prorated Billing** - Mid-cycle plan changes
2. **Usage Alerts** - Notifications when approaching limits
3. **Billing History** - Complete payment and invoice history
4. **Team Management** - Multi-user team billing
5. **Custom Plans** - Enterprise-level custom pricing

### Integration Roadmap

1. **Phase 1** - Complete Clerk billing setup
2. **Phase 2** - Implement real payment processing
3. **Phase 3** - Add advanced billing features
4. **Phase 4** - Enterprise and team features

## 📚 Related Documentation

- [Clerk Billing Setup Guide](../test/archived/outdated-docs/archive/CLERK_BILLING_SETUP.md)
- [Database Schema](./PRISMA.md)
- [API Documentation](./STACK.md)

---

*Last updated: January 2025*
*Status: Implementation in progress*
