[{"table_name": "ai_models", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "displayName", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "description", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "provider", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "modelId", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "costTier", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "speed", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_models", "column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "ai_models", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "ai_models", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "ai_responses", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_responses", "column_name": "content", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_responses", "column_name": "model", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "prompt", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "tokensUsed", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "mentionId", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "ai_responses", "column_name": "userId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "confidence", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "rating", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "used", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "ai_responses", "column_name": "processingTime", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "version", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "ai_responses", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "ai_responses", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "crypto_sectors_cache", "column_name": "id", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('crypto_sectors_cache_id_seq'::regclass)"}, {"table_name": "crypto_sectors_cache", "column_name": "data", "data_type": "jsonb", "is_nullable": "NO", "column_default": null}, {"table_name": "crypto_sectors_cache", "column_name": "expires_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "crypto_sectors_cache", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "crypto_sectors_cache", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "crypto_trending_cache", "column_name": "id", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('crypto_trending_cache_id_seq'::regclass)"}, {"table_name": "crypto_trending_cache", "column_name": "sector_slug", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"table_name": "crypto_trending_cache", "column_name": "timeframe", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"table_name": "crypto_trending_cache", "column_name": "data", "data_type": "jsonb", "is_nullable": "NO", "column_default": null}, {"table_name": "crypto_trending_cache", "column_name": "expires_at", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "crypto_trending_cache", "column_name": "created_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "crypto_trending_cache", "column_name": "updated_at", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"table_name": "images", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "images", "column_name": "url", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "images", "column_name": "filename", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "fileSize", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "mimeType", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "uploadKey", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "width", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "height", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "altText", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "aiResponseId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "mentionId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "userId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "images", "column_name": "processed", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "images", "column_name": "isPublic", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "images", "column_name": "uploadedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "images", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "content", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "link", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "<PERSON><PERSON><PERSON>", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "authorId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "authorAvatar<PERSON><PERSON>", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "authorVerified", "data_type": "boolean", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "mentionedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "replyCount", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "mentions", "column_name": "retweetCount", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "mentions", "column_name": "likeCount", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"table_name": "mentions", "column_name": "isReply", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "mentions", "column_name": "parentTweetId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "bullishScore", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "analysisData", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "keywords", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "accountId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "userId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "processed", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "mentions", "column_name": "processingError", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "mentions", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "mentions", "column_name": "archived", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "mentions", "column_name": "archivedAt", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "importanceScore", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"table_name": "mentions", "column_name": "isUserTweet", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "monitored_accounts", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "twitter<PERSON><PERSON>le", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "twitterId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "displayName", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "avatarUrl", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "monitored_accounts", "column_name": "userId", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "lastCheckedAt", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "totalMentions", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"table_name": "monitored_accounts", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "monitored_accounts", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "monitored_accounts", "column_name": "syncMentions", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "monitored_accounts", "column_name": "syncUserTweets", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "monitored_accounts", "column_name": "syncReplies", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "monitored_accounts", "column_name": "syncRetweets", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "monitored_accounts", "column_name": "sector", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "personality_profiles", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "personality_profiles", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "personality_profiles", "column_name": "description", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "personality_profiles", "column_name": "systemPrompt", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "personality_profiles", "column_name": "isDefault", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "personality_profiles", "column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "personality_profiles", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "personality_profiles", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "plan_features", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "plan_features", "column_name": "planId", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "plan_features", "column_name": "feature", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_name": "plan_features", "column_name": "limit", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"table_name": "plan_features", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "plan_features", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "subscription_plans", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "subscription_plans", "column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "subscription_plans", "column_name": "displayName", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "subscription_plans", "column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "subscription_plans", "column_name": "price", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"table_name": "subscription_plans", "column_name": "baseUsers", "data_type": "integer", "is_nullable": "NO", "column_default": "1"}, {"table_name": "subscription_plans", "column_name": "additionalUserPrice", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"table_name": "subscription_plans", "column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "subscription_plans", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "subscription_plans", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "usage_logs", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "usage_logs", "column_name": "userId", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "usage_logs", "column_name": "feature", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": null}, {"table_name": "usage_logs", "column_name": "amount", "data_type": "integer", "is_nullable": "NO", "column_default": "1"}, {"table_name": "usage_logs", "column_name": "metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"table_name": "usage_logs", "column_name": "billingPeriod", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "usage_logs", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "users", "column_name": "id", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "users", "column_name": "email", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "avatar", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "isAdmin", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"table_name": "users", "column_name": "planId", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"table_name": "users", "column_name": "createdAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"table_name": "users", "column_name": "updatedAt", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"table_name": "users", "column_name": "lastActiveAt", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "customSystemPrompt", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "personalityId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "modelId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"table_name": "users", "column_name": "clerk<PERSON>lanId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "subscriptionStatus", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"table_name": "users", "column_name": "subscriptionUpdatedAt", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}]