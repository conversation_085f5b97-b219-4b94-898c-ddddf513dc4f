Thanks! I’ll now create a detailed Product Requirements Document (PRD) and PostgreSQL schema tailored to your stack, UI, and business logic.

This will cover:

* Product goals, use cases, user flows, and feature breakdowns
* API access and rate limits per subscription tier
* A complete PostgreSQL schema using Prisma with Supabase compatibility
* Clerk-based route protection strategy
* Integration notes for Mem0, Benji Agent (AISDK), OpenRouter, xAI, Exa, and UploadThing

I’ll let you know as soon as it’s ready for your review.


# Product Requirements Document (PRD)

## Overview

We are building **BuddyChip**, a social media assistance platform. Users can monitor one or more Twitter accounts and see all incoming **mentions/replies** to those accounts in real time. For each mention, the dashboard displays the tweet’s content, author, timestamp, and a link. <PERSON><PERSON> then allows the user to generate an AI-suggested reply (without auto-posting) and shows a “bullish score” (1–100) indicating the sentiment or positivity of the mention. A **Quick Reply** box lets the user paste any tweet’s link and get an instant AI-generated response. All operations (especially AI calls and image generation) are rate-limited per user according to a three-tier subscription model (<PERSON><PERSON> <PERSON>, <PERSON><PERSON> God, Team). Administrators can adjust feature availability and rate limits for each tier via the database. The system uses a Next.js frontend (Turborepo monorepo with pnpm), Next.js API routes (protected by Clerk for authentication) for the backend, Prisma ORM with PostgreSQL (hosted on Supabase) for data storage, and a variety of AI tools (Vercel AI SDK with OpenRouter, xAI Live Search, Exa search, OpenAI for images, Mem0 for user memory). Sentry (with Sentry MCP) is used for error monitoring. UploadThing is used to store AI-generated images. Twitter data is fetched via twitterapi.io endpoints for mentions and replies.

## Objectives

* **Monitor Twitter Accounts:** Allow users to add or remove Twitter handles to watch.
* **View Mentions Feed:** Display a combined feed of recent and historical tweets that mention or reply to the monitored accounts.
* **Display Tweet Details:** For each mention/reply, show tweet text, author name/handle, the mentioned monitored account, timestamp, and a link to the tweet.
* **AI Response Suggestion:** Enable a “Generate AI Answer” action per mention. This invokes the Benji agent (using Gemini and other models) to produce a suggested reply. The reply is shown to the user in the UI but not auto-posted to Twitter.
* **Quick Reply:** Provide an input box where the user can paste any tweet URL; clicking “Reply” runs the Benji agent on that tweet and shows the suggested reply.
* **Dashboard UI:** Present a unified interface. On the left, a **Monitored Accounts** sidebar lists all accounts the user is watching and their status; on the right, a **Latest Mentions** panel lists tweets mentioning those accounts, sorted by date. Each mention entry shows the tweet content and author (with link) and action buttons for replying/enhancing/deleting or generating an AI answer. Screenshots of the intended UI are provided for reference.
* **Subscription & Rate Limits:** Implement three paid tiers: **Reply Guy** (\$20/mo), **Reply God** (\$50/mo), and **Team Plan** (\$79 + \$50 per additional user). Each tier grants a quota of resource-intensive operations (AI text generations, image generations, tweet queries). The system enforces usage limits (e.g. “AI calls per month”) based on the user’s plan. Administrators can update plan parameters and limits in the database at any time.

## Technical Stack

* **Frontend:** Next.js (React) within a Turborepo monorepo (managed by pnpm) with built-in routing. Next.js supports “API Routes” (Node.js serverless functions) for backend endpoints. Clerk provides authentication; we will use Clerk’s React SDK (`<ClerkProvider>`) and middleware to protect client and server routes.
* **Backend/API:** Next.js API routes handle data operations and third-party API calls. All protected routes use Clerk’s `auth.protect()` or `auth()` helpers to ensure the user is signed in. For example, `await auth().userId` can retrieve the current user’s Clerk ID. This aligns with Clerk’s documentation for Next.js integrations.
* **ORM & Database:** We use **Prisma** as the ORM with PostgreSQL. The PostgreSQL database is hosted on Supabase (an open-source Firebase alternative providing Postgres with the `pgvector` extension for vector search). Prisma’s schema (in `schema.prisma`) declares the data source and all application models and relations. Prisma will connect via a connection string (from Supabase) provided in `DATABASE_URL`. Supabase also supports vector search (used by Mem0 for memory storage).
* **Memory Store:** **Mem0** uses the Supabase database for persistent AI memory (chat history embeddings). It stores user-chat memory in PostgreSQL with pgvector for similarity search. We’ll configure Mem0 using the Supabase connection string (e.g. via `MEM0_SUPABASE_URL` and `MEM0_SUPABASE_KEY` environment variables).
* **AI Agent:** We use Vercel’s AI SDK framework integrated with the **OpenRouter** provider. OpenRouter gives unified access to many LLMs (including Google Gemini). We will install `@openrouter/ai-sdk-provider` and use its `streamText()` API to run OpenAI o3 (via OpenRouter) or Gemini 2.5 Flash as needed. Preferred models: *Gemini 2.5 Flash* for simple completions, and *OpenAI o3* for advanced reasoning (accessed via OpenRouter API).
* **AI Tools:** The Benji agent has access to several tools:

  * **xAI Live Search:** For real-time web search, using xAI (Grok) models. We will use the Grok SDK; its API key is stored in `XAI_API_KEY`.
  * **Exa:** A web search engine for AI. We will call Exa’s API for knowledge retrieval; its key is `EXA_API_KEY`.
  * **OpenAI Image:** For any image generation tasks, we use OpenAI’s image API (with `OPENAI_API_KEY`).
  * **Mem0 User Memory:** Personal user memory is persisted via Mem0 (using Supabase). We won’t store these in our own tables, but they use the same DB.
* **Debugging:** Sentry is integrated for error monitoring. We use `@sentry/nextjs`. The `SENTRY_DSN` environment variable configures the project. For source-map upload, we also set `SENTRY_AUTH_TOKEN` during builds. Sentry MCP (Model Context Protocol) is enabled, allowing the AI developer tools to query Sentry issues.
* **Twitter Data:** We use [twitterapi.io](https://twitterapi.io) endpoints for Twitter data (no direct OAuth needed). Specifically:

  * **Get User Mentions:** `GET /twitter/user/mentions` (returns tweets mentioning a given user).
  * **Get Tweet Replies:** `GET /twitter/tweet/replies` (returns replies to a given tweet ID).
    All Twitter API calls include the header `X-API-Key: <key>`, where the key (`TWITTER_API_KEY`) is obtained from twitterapi.io. The API returns tweet objects including `id`, `url`, `text`, `createdAt`, and nested `author` information, which we display in the UI.
* **File Uploads (Images):** We use **UploadThing** to store AI-generated images. UploadThing requires a secret token (`UPLOADTHING_TOKEN`) configured via environment. Generated images are uploaded through UploadThing’s APIs and we store their references (URLs or file keys) in our database.

## Design System & Color Palette

The application implements a cohesive design system with a custom color palette that creates a modern, accessible interface. All colors are defined as CSS custom properties and integrated with Tailwind CSS v4 using the `@theme` directive.

### Color Palette
The color scheme follows a carefully designed palette documented in `docs/PALETTE.md`:

| Color Name | Hex Code | Usage | CSS Variable | Tailwind Class |
|------------|----------|-------|--------------|----------------|
| Background | `#d4d8f0` | Main app background | `--app-background` | `bg-app-background` |
| Headline | `#232946` | Primary text, headings | `--app-headline` | `text-app-headline` |
| Sub Headline | `#232946` | Secondary text | `--app-sub-headline` | `text-app-sub-headline` |
| Card Background | `#fffffe` | Card containers | `--app-card` | `bg-app-card` |
| Card Heading | `#232946` | Card titles | `--app-card-heading` | `text-app-card-heading` |
| Card Paragraph | `#232946` | Card body text | `--app-card-paragraph` | `text-app-card-paragraph` |
| Stroke | `#121629` | Borders, dividers | `--app-stroke` | `border-app-stroke` |
| Main | `#b8c1ec` | Primary accent, CTAs | `--app-main` | `bg-app-main` |
| Highlight | `#eebbc3` | Secondary accent, alerts | `--app-highlight` | `bg-app-highlight` |
| Secondary | `#fffffe` | Secondary buttons | `--app-secondary` | `bg-app-secondary` |
| Tertiary | `#eebbc3` | Tertiary elements | `--app-tertiary` | `bg-app-tertiary` |

### Implementation
Colors are implemented using CSS custom properties in `/apps/web/src/index.css`:

```css
:root {
  /* Custom app colors from PALETTE.md */
  --app-background: #d4d8f0;
  --app-headline: #232946;
  --app-sub-headline: #232946;
  --app-card: #fffffe;
  --app-card-heading: #232946;
  --app-card-paragraph: #232946;
  --app-stroke: #121629;
  --app-main: #b8c1ec;
  --app-highlight: #eebbc3;
  --app-secondary: #fffffe;
  --app-tertiary: #eebbc3;
}

@theme inline {
  /* Custom app colors for Tailwind */
  --color-app-background: var(--app-background);
  --color-app-headline: var(--app-headline);
  /* ... other color mappings */
}
```

## UI Framework & Styling

### Tailwind CSS v4
The application uses **Tailwind CSS v4.1.10** with modern features:
- **@theme directive** for CSS custom properties integration
- **Custom color variables** mapped to Tailwind utilities
- **tw-animate-css** package for enhanced animations
- **tailwindcss-animate** for component animations

### Component System
Built on **shadcn/ui** with the "new-york" style variant:
- **Card-based layouts** for content organization
- **Lucide React icons** for consistent iconography
- **Button variants** with custom styling using app colors
- **Form components** with integrated validation
- **Responsive design** using Tailwind's responsive utilities

### Typography & Spacing
- **Font**: Inter/Geist font stack for modern readability
- **Spacing**: 8pt grid system for consistent layouts
- **Design principles**: 60/30/10 color rule (neutrals/complementary/accent)

## Code Quality & Development Tools

### Biome Integration
The project uses **Biome v2.0.0** for comprehensive code quality management:

#### Configuration (`biome.json`)
```json
{
  "$schema": "https://biomejs.dev/schemas/2.0.0/schema.json",
  "formatter": {
    "indentStyle": "space",
    "indentWidth": 2,
    "lineWidth": 80
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true,
      "a11y": { "recommended": true },
      "complexity": { "recommended": true },
      "correctness": { "recommended": true },
      "security": { "recommended": true }
    }
  }
}
```

#### Integrated Scripts
All packages include Biome commands:
- `pnpm lint` - Check code quality across monorepo
- `pnpm lint:fix` - Auto-fix issues
- `pnpm format` - Format code consistently
- `pnpm check` - CI-ready quality checks

#### Rules & Standards
- **TypeScript**: Strict type checking, no explicit `any`
- **React**: Hook rules, accessibility guidelines
- **Performance**: Optimized patterns, no excessive complexity
- **Security**: Best practices enforcement
- **Accessibility**: WCAG compliance checks

### Development Workflow
- **Turborepo**: Monorepo task orchestration with caching
- **PNPM**: Fast, efficient package management
- **Hot reload**: Turbopack for instant development feedback
- **Type safety**: Full-stack TypeScript with tRPC integration

## Error Monitoring & Observability

### Sentry Integration
Comprehensive error tracking with separate projects:

#### Web Application (`buddychip-web`)
- **Session Replay**: Full user interaction recording
- **Error Boundaries**: React error boundary components
- **Performance Monitoring**: Web vitals and load times
- **Configuration**: `apps/web/sentry.client.config.ts`

#### Server Application (`buddychip-server`)
- **API Error Tracking**: tRPC and Next.js API route errors
- **Performance Monitoring**: Database and external API latency
- **Configuration**: `apps/web/sentry.server.config.ts`

#### Instrumentation
```typescript
// apps/web/instrumentation.ts
export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    await import('./sentry.server.config')
  }
  if (process.env.NEXT_RUNTIME === 'edge') {
    await import('./sentry.edge.config')
  }
}
```

#### Environment Configuration
- **Production**: Full error tracking and source maps
- **Development**: Conditional loading to avoid noise
- **Source Maps**: Automatic upload via Sentry webpack plugin

## Functional Requirements

1. **Account Monitoring:**

   * *Add/Remove Accounts:* Users can search for a Twitter handle and add it to their monitored list. They can also remove an account. Each monitored account is stored in the database linked to the user.
   * *Status:* The UI should indicate whether monitoring for each account is active.

2. **Mentions/Replies Feed:**

   * The system continuously fetches new tweets that mention any monitored account (using Twitter API webhooks or polling). Historical mentions are also stored. All mentions (and direct replies) are listed chronologically.
   * Each mention entry shows: tweet text, a link to the tweet (built from the tweet ID), tweet author (name/handle), which monitored account was mentioned, and the timestamp when the tweet was created.

3. **AI-Generated Response:**

   * Next to each mention, a **“Generate AI Answer”** button triggers the Benji agent. Benji uses the context of the mention (including conversation if any) to craft a reply suggestion. This reply text is shown to the user beneath the mention. (It is **not** posted to Twitter automatically.)
   * The “bullish score” (an integer 1–100) is shown alongside each mention. This score is computed by an AI model to indicate positivity (higher = more positive/“bullish”). The score is stored in the database with the mention.

4. **Quick Reply:**

   * On the dashboard, a text input labeled “Paste tweet link” allows the user to enter any tweet URL. Upon submitting, the Benji agent runs on that tweet as if it were a mention, and a suggested reply appears below. Quick replies are treated similarly to mention replies, but may not be stored under a monitored account.

5. **Dashboard UI:**

   * The main interface is a two-pane dashboard. The left sidebar lists all **Monitored Accounts** with their Twitter handles (and a toggle or status indicator). The right pane is the **Latest Mentions** feed. (Example mockup below.)
   * Each row in the mentions feed shows tweet content, author name, time, and action buttons (“Reply”, “Enhance”, “Delete”, “Generate AI Answer”). The “Reply” button opens a reply form, “Enhance” allows editing the AI prompt, and “Delete” removes the mention from view. “Generate AI Answer” runs Benji.

   &#x20;*Figure: Example dashboard. Left: Monitored accounts. Right: Recent mentions of the selected account. Below is the Quick Reply box.*

   &#x20;*Figure: Mention list for a specific account. Each tweet shows author, text, timestamp, and action buttons. Users can trigger Benji to generate a reply.*

### Current Implementation Status

The application currently has the following pages implemented:

#### Homepage (`/`)
- **Layout**: Modern card-based design with responsive grid
- **Monitored Accounts Section**: Displays accounts with status indicators (Green/Red lights)
- **Latest Mentions Section**: Scrollable list of 10 most recent mentions
- **Quick Reply Section**: Central feature with URL input and AI response generation
- **Navigation**: Global nav bar with LOGO, REPLY GUY, COPIUM, and PROFILE links
- **Color Scheme**: Full implementation of the custom palette with app-* CSS variables

#### Reply Guy Page (`/reply-guy`)
- **Mentions Feed**: Detailed view of all mentions with full content
- **Action Buttons**: Reply, Enhance, Delete, and Generate AI Answer for each mention
- **Monitored Accounts Dropdown**: Expandable list showing account status and light indicators
- **AI Response Interface**: Textarea for editing generated replies with "Use this reply" action
- **Status Indicators**: Loading states with animated spinners during AI generation
- **Enhanced Features**: Mention enhancement functionality with progress tracking

#### Component Architecture
```typescript
// Main page structure
interface MonitoredAccount {
  id: number
  handle: string
  status: "Monitored" | "Not Monitored"
  light: "Green" | "Red"
}

interface Mention {
  id: string
  handle: string
  content: string
  platform: "X" | "Twitter"
  aiGeneratedAnswer?: string
  isGenerating?: boolean
}
```

#### UI Patterns
- **Card Components**: `Card`, `CardContent`, `CardHeader`, `CardFooter` from shadcn/ui
- **Interactive Elements**: Buttons with hover states using app-main/app-highlight colors
- **Form Controls**: Input fields with app-background styling and app-stroke borders
- **Loading States**: Custom spinners with app-highlight color theming
- **Responsive Design**: Mobile-first approach with md: breakpoints

#### Styling Implementation
- **CSS Variables**: All app-* colors properly mapped to Tailwind utilities
- **Hover Effects**: Smooth transitions between app-headline → app-main
- **Visual Hierarchy**: Bold headlines, subtle opacity for secondary text
- **Accessibility**: High contrast ratios, proper focus states

6. **Subscription Tiers & Rate Limiting:**

   * **Plans:** There are three plans:

     * *Reply Guy* – \$20/mo (single user).
     * *Reply God* – \$50/mo (single user).
     * *Team Plan* – \$79/mo for the first user + \$50/mo for each additional user on the same team account.

   * **Feature Limits:** Each plan entitles the user to a certain monthly quota of expensive operations (example: 100 AI text generations and 20 image generations for Reply Guy; higher for God; Team plan scales with users). Admins can configure limits per plan (e.g. “AI calls per month”) in the database.

   * **Enforcement:** The backend checks the user’s plan and current usage before allowing a costly action. Excess usage is blocked or prompts an “upgrade plan” message.

   * **Administrative Control:** An admin interface (or direct DB access) lets admins update a plan’s price or feature limits. For example, an admin can change “images/month” limit for the Reply Guy tier. These values are stored in the **SubscriptionPlan** and **PlanFeature** tables (see schema).

   | Plan Name | Monthly Price    | Base Users | Additional User Price | Key Features (per month)                |
   | --------- | ---------------- | ---------- | --------------------- | --------------------------------------- |
   | Reply Guy | \$20             | 1          | N/A                   | e.g. 100 AI replies, 20 images          |
   | Reply God | \$50             | 1          | N/A                   | e.g. 500 AI replies, 50 images          |
   | Team      | \$79 + \$50/p.u. | 1          | \$50 per extra user   | (Higher limits, includes team accounts) |

## Data Model (Database Schema)

The application’s data is stored in PostgreSQL via Prisma. Key entities and their relations:

* **User:** Represents an application user (linked to Clerk). Stores the user’s Clerk ID, role (admin or regular), and subscription plan. (Clerk handles auth; we store Clerk’s `userId` as primary key.) Each user has one subscription plan.
* **SubscriptionPlan:** Defines each pricing tier (name, price, base user count, extra-user price) and general features. Relates to many users.
* **PlanFeature:** (Optional) Links a plan to specific feature limits (e.g. “AI\_calls”, “Image\_generations” with numeric limits). Admins can update these.
* **MonitoredAccount:** A Twitter account the user is watching. Fields include the Twitter handle (or user ID), and a relation to the owner user. One user may have many monitored accounts.
* **Mention (Tweet):** A tweet that mentions a monitored account (or was selected for quick reply). Key fields: tweet ID (primary key), content (text), link, author info (e.g. `authorName`, `authorHandle`), `createdAt`, `bullishScore`. Each mention may belong to *one* MonitoredAccount (nullable for quick-replies) and has many AI-generated responses.
* **AIResponse:** Stores an AI-generated reply. Fields: ID, content (text), timestamp, and foreign key to the Mention (or Quick Reply) it is answering. Multiple responses can be stored per mention (if user generates multiple suggestions).
* **Image:** For AI-generated images (via UploadThing). Fields: ID, file URL or key, creation time, and optional link to the AIResponse or Mention that triggered it. (One image may be associated with an AIResponse.)
* **\[Optional] UsageLog:** To enforce rate limits, we may log each use of a limited feature (date, type, user) or maintain counters. This can be derived or stored in a separate table.

All relationships are enforced via Prisma relations. For example, a `User` model has `monitoredAccounts: MonitoredAccount[]` and a `SubscriptionPlan` model has `users: User[]`. Mentions link to accounts and responses, etc.

**Access Control:** All API routes will check Clerk authentication first. For example, in Next.js route handlers we will use:

```ts
import { auth } from '@clerk/nextjs/server';
// ...
export async function GET(req) {
  await auth().requireAuth(); 
  // Handler logic...
}
```

This ensures only signed-in users (and optionally admins) can call the APIs. In practice, we may also store `user.role` or `isAdmin` to allow admin-only endpoints (like adjusting plan limits).

## APIs and Integration

* **Twitter Integration:** Using the Twitter API wrapper (`twitterapi.io`), we call `/twitter/user/mentions` and `/twitter/tweet/replies` with the user’s API key in headers. These endpoints return JSON tweets (with fields `text`, `id`, `createdAt`, `author`, etc.). We parse this into our `Mention` records. For example, a mention record stores the tweet’s `text` and constructs its link (e.g. `https://twitter.com/i/web/status/{id}`).
* **AI Agent:** When “Generate AI Answer” is clicked, the frontend calls a Next.js API route (protected by Clerk). The backend constructs a prompt from the mention context and calls the Vercel AI SDK with `streamText()` using the appropriate model (Gemini). Configuration (API keys, site URL, app name) is set via environment vars: `OPENROUTER_API_KEY`, `OR_SITE_URL`, `OR_APP_NAME`.
* **Image Upload:** For any image generation (via OpenAI’s DALL·E or similar), the resulting image file is uploaded via UploadThing, which requires `UPLOADTHING_TOKEN`. We then save the returned file URL in our `Image` table.
* **Mem0 Memory:** The Benji agent may use Mem0 to recall prior conversations. Mem0 is configured with the Supabase connection (using `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY` or a connection string). The Prisma schema need not include Mem0 tables (these are managed by the Mem0 library), but we ensure to include the pgvector extension in migrations.

## Environment Variables

The application requires the following environment variables to be set (e.g. in a `.env` file):

| Variable                | Description                                                               |
| ----------------------- | ------------------------------------------------------------------------- |
| `DATABASE_URL`          | Connection string to Supabase PostgreSQL (includes `pgvector` extension). |
| `CLERK_PUBLISHABLE_KEY` | Clerk frontend publishable key (`pk_…`).                                  |
| `CLERK_SECRET_KEY`      | Clerk backend secret key (`sk_…`).                                        |
| `SENTRY_DSN`            | Sentry project DSN for error reporting.                                   |
| `SENTRY_AUTH_TOKEN`     | Sentry auth token (for uploading source maps).                            |
| `OPENAI_API_KEY`        | OpenAI API key (for GPT-4, image generation, etc.).                       |
| `OPENROUTER_API_KEY`    | OpenRouter API key (for Gemini 2.5 Flash).                          |
| `OR_SITE_URL`           | OpenRouter site URL (as per account settings).                            |
| `OR_APP_NAME`           | OpenRouter app name (as per account settings).                            |
| `XAI_API_KEY`           | xAI (Grok) API key (for Live Search).                                     |
| `EXA_API_KEY`           | Exa API key (for search and enrichment).                                  |
| `UPLOADTHING_TOKEN`     | UploadThing secret token for file uploads.                                |
| `TWITTER_API_KEY`       | Twitter API key from twitterapi.io for fetching tweets.                   |
| `SUPABASE_URL`          | (Optional) Supabase project URL (for Mem0 config).                        |
| `SUPABASE_SERVICE_ROLE` | (Optional) Supabase service-role key (for Mem0 write access).             |

Environment variables prefixed with `NEXT_PUBLIC_` (e.g. `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`) will be exposed to the browser; sensitive keys (secret keys, DB URL) must remain server-side. Clerk’s documentation specifies that `CLERK_PUBLISHABLE_KEY` is used client-side and `CLERK_SECRET_KEY` server-side.

---

## Current Dependencies & Development Setup

### Core Framework Versions
The application is built on the latest stable versions of key technologies:

| Technology | Version | Purpose |
|------------|---------|---------|
| Next.js | 15.3.0 | Frontend framework and API routes |
| React | 19.0.0 | UI library with latest features |
| TypeScript | 5.x | Type safety across full stack |
| tRPC | 11.0.0 | Type-safe API layer |
| Prisma | 6.9.0 | Database ORM and migrations |
| Tailwind CSS | 4.1.10 | Styling framework with @theme support |
| Biome | 2.0.0 | Code quality and formatting |

### Package Management & Monorepo
- **PNPM**: v10.12.1 for efficient dependency management
- **Turborepo**: v2.5.4 for monorepo task orchestration
- **Workspaces**: `apps/*` and `packages/*` structure

### Development Commands
```bash
# Start development servers
pnpm dev                 # Both web (3001) and server (3000)
pnpm dev:web            # Web app only
pnpm dev:server         # Server only

# Database operations
pnpm db:push            # Push schema changes
pnpm db:studio          # Open Prisma Studio
pnpm db:generate        # Generate Prisma client
pnpm db:migrate         # Run migrations

# Code quality
pnpm lint               # Check all code
pnpm lint:fix           # Auto-fix issues
pnpm format             # Format code
pnpm check-types        # TypeScript validation

# Build and deployment
pnpm build              # Build all apps
```

### Application Architecture
- **Web App** (`apps/web`): Next.js frontend on port 3001
- **Server App** (`apps/server`): Next.js API backend on port 3000
- **Database**: PostgreSQL with Prisma at `apps/server/prisma/schema`
- **Generated Client**: Custom location at `apps/server/prisma/generated/`

### Production Dependencies
Key runtime dependencies across the monorepo:

#### Web App
- `@sentry/nextjs`, `@sentry/react` for error monitoring
- `@tanstack/react-query` for state management
- `@trpc/client`, `@trpc/tanstack-react-query` for API integration
- `tailwindcss-animate`, `tw-animate-css` for animations
- `lucide-react` for icons, `next-themes` for theming

#### Server App
- `@sentry/node` for backend error tracking
- `@prisma/client` for database operations
- `@trpc/server` for API implementation

### Development Experience
- **Turbopack**: Fast development builds
- **Hot Module Replacement**: Instant code updates
- **TypeScript Strict Mode**: Full type safety
- **ESM Modules**: Modern JavaScript standards
- **Error Boundaries**: Graceful error handling

## Prisma Schema

Below is a suggested Prisma schema (`schema.prisma`) defining the database models and relations. This schema uses PostgreSQL (`provider = "postgresql"`) and expects `DATABASE_URL` in the environment. Adjust field types and relations as needed:

```prisma
// Prisma schema for Benji app
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL") // Supabase PostgreSQL
}

model User {
  id                 String             @id @default(cuid())          // Clerk user ID
  email              String?            @unique
  name               String?
  isAdmin            Boolean            @default(false)
  plan               SubscriptionPlan   @relation(fields: [planId], references: [id])
  planId             String
  monitoredAccounts  MonitoredAccount[]
  aiResponses        AIResponse[]
  createdAt          DateTime           @default(now())
}

model SubscriptionPlan {
  id                  String              @id @default(cuid())
  name                String              // e.g. "Reply Guy"
  price               Float               // monthly price in USD
  baseUsers           Int                 @default(1) // for Team plan
  additionalUserPrice Float?             // price per extra user (Team plan)
  features            PlanFeature[]
  users               User[]
}

model PlanFeature {
  id        String   @id @default(cuid())
  plan      SubscriptionPlan @relation(fields: [planId], references: [id])
  planId    String
  feature   String   // e.g. "AI_calls", "Images"
  limit     Int      // numeric limit per billing cycle (0 or -1 for unlimited)
}

model MonitoredAccount {
  id             String    @id @default(cuid())
  twitterHandle  String    // e.g. "elonmusk"
  twitterId      String?   // optional Twitter numeric ID
  user           User      @relation(fields: [userId], references: [id])
  userId         String
  mentions       Mention[]
  createdAt      DateTime  @default(now())
}

model Mention {
  id               String           @id                 // Tweet ID
  content          String           // Tweet text
  link             String           // Full URL to tweet
  authorName       String           // e.g. "Elon Musk"
  authorHandle     String           // e.g. "elonmusk"
  mentionedAt      DateTime         // Tweet timestamp
  bullishScore     Int?             // 1-100 sentiment score
  account          MonitoredAccount? @relation(fields: [accountId], references: [id])
  accountId        String?
  responses        AIResponse[]
  createdAt        DateTime         @default(now())
}

model AIResponse {
  id           String    @id @default(cuid())
  mention      Mention   @relation(fields: [mentionId], references: [id])
  mentionId    String
  user         User?     @relation(fields: [userId], references: [id])
  userId       String?
  content      String    // AI-generated reply text
  createdAt    DateTime  @default(now())
  images       Image[]
}

model Image {
  id           String    @id @default(cuid())
  url          String    // UploadThing file URL or key
  uploadedAt   DateTime  @default(now())
  aiResponse   AIResponse? @relation(fields: [aiResponseId], references: [id])
  aiResponseId String?
  // Optionally link directly to mention if needed:
  mention      Mention? @relation(fields: [mentionId], references: [id])
  mentionId    String?
}
```

**Notes on schema:**

* We use string IDs (`cuid()`) for user and plan IDs. The `User.id` should match Clerk’s user ID (you could also store Clerk’s ID in a separate field if needed).
* The `plan` relation on `User` links each user to their current plan.
* `MonitoredAccount` belongs to a single user, and has many `Mention` tweets.
* The `Mention` model uses the tweet’s ID as its own ID. It optionally links to one `MonitoredAccount` (if this mention was for that account). Quick-replies can be recorded with `accountId = null`.
* Each `AIResponse` ties to a single `Mention`. We record which user triggered it (nullable).
* `PlanFeature` lets us store customizable feature limits per plan. For example, we might have entries like `{ plan: "Reply Guy", feature: "AI_calls", limit: 100 }`. The backend will check these limits to enforce quotas.
* `Image` records any file generated/uploaded via UploadThing. It can be linked to an `AIResponse` (for images generated as part of a reply) or directly to a `Mention`.

This schema, together with Clerk’s authentication middleware and the described tech stack, will fulfill the requirements. All routes accessing these models must check Clerk auth and plan limits as described.

**Sources:** We used official documentation for Next.js API Routes, Clerk auth in Next.js routes, Prisma schema definitions, Supabase/PostgreSQL features, OpenRouter/Vercel AI integration, Twitter API responses, and UploadThing env configuration as references for this design. These citations ensure that our design aligns with the capabilities of the chosen tools and services.
