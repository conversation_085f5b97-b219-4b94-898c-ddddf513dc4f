# 🔧 Prisma Troubleshooting Guide

## 📋 Quick Diagnosis

### **🚨 Emergency Checklist**
1. ✅ Check database connection: `curl http://localhost:3001/api/test-db`
2. ✅ Verify environment variables: `DATABASE_URL`, `DIRECT_URL`
3. ✅ Check Prisma client generation: `pnpm db:generate`
4. ✅ Verify database schema: `pnpm db:push`
5. ✅ Check logs for specific error codes

---

## 🔍 Common Issues & Solutions

### **1. Connection Issues**

#### **❌ "Can't reach database server"**
```
Error: Can't reach database server at `localhost:5432`
```

**Diagnosis**:
```bash
# Check if database is running
pg_isready -h localhost -p 5432

# Check connection string
echo $DATABASE_URL
```

**Solutions**:
```bash
# 1. Start local PostgreSQL
brew services start postgresql
# or
sudo systemctl start postgresql

# 2. Check Supabase status (if using Supabase)
# Visit: https://status.supabase.com/

# 3. Verify connection string format
DATABASE_URL="postgresql://user:password@host:port/database?schema=public"
```

#### **❌ "Connection pool timeout"**
```
Error: Timed out fetching a new connection from the connection pool
```

**Diagnosis**:
```sql
-- Check active connections
SELECT count(*) FROM pg_stat_activity WHERE datname='your_database';

-- Check connection limits
SHOW max_connections;
```

**Solutions**:
```typescript
// 1. Reduce connection pool size in config
const client = createPrismaClient({
  // Add connection pool settings if needed
  datasources: {
    db: {
      url: `${process.env.DATABASE_URL}?connection_limit=5`
    }
  }
});

// 2. Ensure proper cleanup in scripts
await runScript("my-script", async (prisma) => {
  // Work is automatically cleaned up
});
```

#### **❌ "SSL connection required"**
```
Error: connection requires SSL
```

**Solutions**:
```bash
# Add SSL parameters to connection string
DATABASE_URL="postgresql://user:pass@host:port/db?sslmode=require"
# or
DATABASE_URL="postgresql://user:pass@host:port/db?ssl=true"
```

### **2. Schema Issues**

#### **❌ "Table doesn't exist"**
```
Error: Table 'User' doesn't exist in the current database
```

**Diagnosis & Solutions**:
```bash
# 1. Check if schema is applied
pnpm db:push

# 2. Check database tables
psql $DATABASE_URL -c "\dt"

# 3. Reset database (development only)
pnpm db:reset

# 4. Run migrations (production)
pnpm prisma migrate deploy
```

#### **❌ "Column doesn't exist"**
```
Error: column "newField" of relation "User" does not exist
```

**Solutions**:
```bash
# 1. Regenerate Prisma client
pnpm db:generate

# 2. Push schema changes
pnpm db:push

# 3. Check for schema drift
pnpm prisma db pull
```

### **3. Import/Module Issues**

#### **❌ "Cannot find module '../lib/db-utils'"**
```
Error: Cannot find module '../lib/db-utils'
```

**Solutions**:
```typescript
// Check import path relative to your file
// From apps/web/src/routers/user.ts:
import { prisma } from '../lib/db-utils'; // ✅ Correct

// From apps/web/src/components/UserList.tsx:
import { prisma } from '../lib/db-utils'; // ❌ Wrong - don't use in components

// From apps/web/src/scripts/seed.ts:
import { runScript } from '../lib/script-utils'; // ✅ Correct
```

#### **❌ "PrismaClient is not a constructor"**
```
TypeError: PrismaClient is not a constructor
```

**Solutions**:
```typescript
// ❌ Wrong import
import PrismaClient from '../prisma/generated';

// ✅ Correct import
import { PrismaClient } from '../prisma/generated';

// ✅ Better - use our utilities
import { prisma } from '../lib/db-utils';
import { runScript } from '../lib/script-utils';
```

### **4. Script Execution Issues**

#### **❌ Script hangs or doesn't exit**
```
Script runs but process doesn't exit
```

**Diagnosis**:
```bash
# Check for hanging connections
lsof -i :5432 | grep node
```

**Solutions**:
```typescript
// ✅ Use script utilities (automatic cleanup)
import { runScript } from '../lib/script-utils';

await runScript("my-script", async (prisma) => {
  // Work here - cleanup is automatic
});

// ❌ If you must use direct instance
const prisma = new PrismaClient();
try {
  await doWork();
} finally {
  await prisma.$disconnect(); // Essential!
}
```

#### **❌ "Script failed with no error message"**
```
Script exits with code 1 but no error shown
```

**Solutions**:
```typescript
// Add verbose logging
await runScript("my-script", myFunction, {
  verbose: true,
  maxRetries: 3,
});

// Add try-catch in script function
async function myScript(prisma: any) {
  try {
    await doWork(prisma);
  } catch (error) {
    console.error("Detailed error:", error);
    throw error;
  }
}
```

### **5. Performance Issues**

#### **❌ Slow queries**
```
🐌 Slow Query [web-app-singleton]: User.findMany took 2500ms
```

**Diagnosis**:
```sql
-- Check query execution plan
EXPLAIN ANALYZE SELECT * FROM "User" WHERE email = '<EMAIL>';

-- Check missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE tablename = 'User';
```

**Solutions**:
```typescript
// 1. Add database indexes (in schema.prisma)
model User {
  email String @unique
  planId String
  
  @@index([planId])
  @@index([email, planId])
}

// 2. Optimize queries
// ❌ Slow - fetches everything
const users = await prisma.user.findMany({
  include: { monitoredAccounts: { include: { mentions: true } } }
});

// ✅ Fast - only fetch what you need
const users = await prisma.user.findMany({
  select: { id: true, email: true, planId: true }
});

// 3. Use pagination
const users = await prisma.user.findMany({
  take: 20,
  skip: page * 20,
  orderBy: { createdAt: 'desc' }
});
```

#### **❌ Memory leaks**
```
Process memory keeps growing
```

**Diagnosis**:
```bash
# Monitor memory usage
ps aux | grep node

# Check for multiple Prisma instances
lsof -p <node_pid> | grep postgres
```

**Solutions**:
```typescript
// ✅ Use singleton pattern
import { prisma } from '../lib/db-utils';

// ❌ Don't create multiple instances
// const myPrisma = new PrismaClient();

// ✅ Proper cleanup in scripts
await runScript("my-script", async (prisma) => {
  // Cleanup is automatic
});
```

---

## 🔍 Debugging Techniques

### **1. Enable Detailed Logging**
```bash
# .env.local
ENABLE_PRISMA_QUERY_LOGS=true
VERBOSE_LOGGING=true
DEBUG=prisma:*
```

### **2. Health Check Endpoint**
```bash
# Test database connectivity
curl http://localhost:3001/api/test-db

# Expected response:
{
  "success": true,
  "message": "Database connection successful",
  "details": {
    "connected": true,
    "planCount": 3,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

### **3. Connection Monitoring**
```sql
-- Monitor active connections
SELECT 
  pid,
  usename,
  application_name,
  client_addr,
  state,
  query_start,
  query
FROM pg_stat_activity 
WHERE datname = 'your_database'
ORDER BY query_start DESC;
```

### **4. Query Analysis**
```typescript
// Add custom timing
async function debugQuery() {
  const start = Date.now();
  
  const result = await prisma.user.findMany({
    where: { isActive: true },
    include: { plan: true }
  });
  
  console.log(`Query took: ${Date.now() - start}ms`);
  console.log(`Returned ${result.length} records`);
  
  return result;
}
```

---

## 🚨 Error Code Reference

### **Prisma Error Codes**

| Code | Description | Solution |
|------|-------------|----------|
| P2002 | Unique constraint violation | Check for duplicate data |
| P2025 | Record not found | Verify record exists |
| P2003 | Foreign key constraint | Check related records |
| P2016 | Query interpretation error | Review query syntax |
| P2021 | Table doesn't exist | Run migrations |
| P2022 | Column doesn't exist | Update schema |

### **Connection Error Patterns**

```typescript
// Handle specific error types
try {
  await prisma.user.create({ data });
} catch (error) {
  if (error.code === 'P2002') {
    throw new Error('Email already exists');
  }
  if (error.message.includes('connection')) {
    throw new Error('Database connection failed');
  }
  if (error.message.includes('timeout')) {
    throw new Error('Database operation timed out');
  }
  throw error;
}
```

---

## 🛠️ Recovery Procedures

### **1. Database Connection Recovery**
```bash
# 1. Check database status
pg_isready -h localhost -p 5432

# 2. Restart application
pnpm dev

# 3. Test health endpoint
curl http://localhost:3001/api/test-db

# 4. If still failing, check logs
tail -f logs/application.log
```

### **2. Schema Recovery**
```bash
# 1. Backup current data (if needed)
pg_dump $DATABASE_URL > backup.sql

# 2. Reset schema (development)
pnpm db:reset

# 3. Or apply migrations (production)
pnpm prisma migrate deploy

# 4. Regenerate client
pnpm db:generate

# 5. Verify schema
pnpm db:studio
```

### **3. Performance Recovery**
```bash
# 1. Restart database connections
# Kill hanging connections
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE datname = 'your_database' 
AND state = 'idle in transaction';

# 2. Restart application
pnpm dev

# 3. Monitor performance
# Check slow query logs
```

---

## 📞 Getting Help

### **1. Gather Information**
```bash
# System info
node --version
pnpm --version

# Database info
psql $DATABASE_URL -c "SELECT version();"

# Prisma info
pnpm prisma --version

# Environment check
echo "DATABASE_URL exists: $([ -n "$DATABASE_URL" ] && echo "Yes" || echo "No")"
```

### **2. Check Logs**
```bash
# Application logs
tail -f logs/application.log

# Database logs (if accessible)
tail -f /var/log/postgresql/postgresql.log

# System logs
journalctl -u postgresql -f
```

### **3. Test Isolation**
```typescript
// Create minimal test case
import { createPrismaClient } from '../lib/prisma-config';

async function testConnection() {
  const testClient = createPrismaClient({
    instanceId: "debug-test",
    forceQueryLogs: true,
  });
  
  try {
    const result = await testClient.$queryRaw`SELECT 1 as test`;
    console.log("Connection test passed:", result);
  } catch (error) {
    console.error("Connection test failed:", error);
  } finally {
    await testClient.$disconnect();
  }
}

testConnection();
```

---

## 🎯 Prevention Tips

1. **Always use script utilities** for database scripts
2. **Monitor connection counts** regularly
3. **Enable query logging** in development
4. **Test schema changes** in staging first
5. **Keep backups** of production data
6. **Monitor performance metrics** continuously
7. **Use health checks** in production deployments

---

## 🆘 Emergency Contacts

- **Database Issues**: Check Supabase status page
- **Performance Issues**: Review query logs and connection counts
- **Schema Issues**: Verify migrations and regenerate client
- **Connection Issues**: Check environment variables and network connectivity

Remember: **Most issues can be resolved by following the patterns in our architecture documentation.** 🚀
