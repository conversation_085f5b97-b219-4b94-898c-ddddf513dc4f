# Supabase Database Connection Fix - Step by Step Documentation

## 🚨 Problem Description
- **Error**: `Can't reach database server at aws-0-us-east-2.pooler.supabase.com:5432`
- **Location**: Vercel deployment (local development working fine)
- **Symptoms**: Database connection failures, user authentication issues, tRPC errors

## 🔍 Root Cause Analysis

### Investigation Steps:
1. **Analyzed error logs** - Found port mismatch (5432 vs 6543)
2. **Checked local .env file** - Configuration was correct
3. **Tested local database connection** - Working perfectly
4. **Identified issue** - Vercel environment variables had outdated connection strings

### Key Findings:
- **Local environment**: Correctly configured with port 6543 for pooler
- **Vercel environment**: Using incorrect port 5432 for pooler connection
- **Supabase instance**: Active and responsive

## 🛠️ Solution Steps

### Step 1: Verify Local Configuration
**File**: `apps/web/.env`
```bash
# Correct configuration found:
DATABASE_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1"
DIRECT_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:5432/postgres"
```

### Step 2: Test Local Database Connection
**Command executed**:
```bash
node -e "
const { PrismaClient } = require('./prisma/generated');
const prisma = new PrismaClient();
async function testConnection() {
  try {
    console.log('🔍 Testing database connection...');
    await prisma.$queryRaw\`SELECT 1 as test\`;
    console.log('✅ Database connection successful!');
    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    await prisma.$disconnect();
    process.exit(1);
  }
}
testConnection();
"
```
**Result**: ✅ Database connection successful!

### Step 3: Identify Vercel Configuration Issue
**Problem**: Vercel environment variables had incorrect connection strings
- **DATABASE_URL**: Using port 5432 instead of 6543
- **DIRECT_URL**: Potentially pointing to wrong endpoint

## 🚀 Fix Implementation

### Required Vercel Environment Variable Updates:

1. **Navigate to Vercel Dashboard**:
   - Go to: https://vercel.com/dashboard
   - Select project: `buddychip-web`
   - Go to: Settings → Environment Variables

2. **Update These Variables**:
   ```bash
   DATABASE_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1"
   
   DIRECT_URL="postgresql://postgres.ojxlpqerellfuiiidddj:<EMAIL>:5432/postgres"
   ```

3. **Trigger Redeploy**:
   - After updating variables, trigger a new deployment

## 📋 Key Differences Explained

### DATABASE_URL (Pooler Connection):
- **Correct**: `aws-0-us-east-2.pooler.supabase.com:6543`
- **Incorrect**: `aws-0-us-east-2.pooler.supabase.com:5432`
- **Purpose**: Connection pooling for better performance

### DIRECT_URL (Direct Connection):
- **Correct**: `db.ojxlpqerellfuiiidddj.supabase.co:5432`
- **Purpose**: Direct database access for migrations and admin operations

## 🔧 Technical Details

### Port Usage:
- **Port 6543**: Supabase pooler (pgBouncer)
- **Port 5432**: Direct PostgreSQL connection

### Connection Parameters:
- **Pooler**: Includes `?pgbouncer=true&connection_limit=1`
- **Direct**: No special parameters needed

## 🎯 Expected Results After Fix

1. **Eliminated Errors**:
   - No more "Can't reach database server" errors
   - Resolved tRPC connection failures
   - Fixed user authentication issues

2. **Restored Functionality**:
   - User creation and authentication
   - Database queries and operations
   - AI agent operations

## 📝 Prevention Measures

### For Future Reference:
1. **Environment Variable Validation**: Add checks for correct connection strings
2. **Documentation**: Keep connection string formats documented
3. **Monitoring**: Set up database connection health checks
4. **Testing**: Regular verification of both local and production connections

## 🏷️ Tags
- **Issue Type**: Database Connectivity
- **Environment**: Vercel Production
- **Resolution**: Environment Variable Correction
- **Status**: Resolved
- **Date**: 2025-06-28

---
*Documentation generated during debugging session with Claude Code*