# Monitored Accounts Data Inconsistency Fix

## Issue Summary

Users experiencing a 403 error when trying to add monitored accounts, with the error message saying they've reached their limit (e.g., "1 monitored account"), but the UI shows 0 accounts.

## Root Cause Analysis

The issue stems from a **query inconsistency** between two different parts of the system:

### 1. UI Query (accounts router `getMonitored`)
```typescript
// Returns ALL accounts regardless of isActive status
const accounts = await ctx.prisma.monitoredAccount.findMany({
  where: {
    userId: ctx.userId!,
  },
  // No isActive filter
});
```

### 2. Count Check (user service `canUserAddMonitoredAccount`)
```typescript
// Only counts ACTIVE accounts
const currentCount = await prisma.monitoredAccount.count({
  where: {
    userId: clerkUserId,
    isActive: true, // Only active accounts
  },
});
```

## Potential Scenarios Leading to This Issue

1. **Orphaned Active Records**: Accounts with `isActive: true` exist in database but don't show in UI due to data corruption or failed operations
2. **Race Conditions**: Between account creation/deletion and count checks
3. **Duplicate Records**: Multiple accounts for the same Twitter handle
4. **Invalid Data**: Accounts with malformed Twitter handles or other invalid data

## Files Modified

### 1. Enhanced User Service (`/apps/web/src/lib/user-service.ts`)
- Added comprehensive logging for debugging data inconsistencies
- Enhanced `canUserAddMonitoredAccount` function to provide detailed account information
- Added detection for data inconsistencies in development mode

### 2. Enhanced Accounts Router (`/apps/web/src/routers/accounts.ts`)
- Added detailed logging to `getMonitored` query
- Better error reporting for debugging

### 3. Data Consistency Script (`/apps/web/src/scripts/fix-monitored-accounts-consistency.ts`)
- Comprehensive script to identify and fix data inconsistencies
- Handles duplicate accounts, invalid handles, orphaned records
- Enforces plan limits by deactivating excess accounts
- Provides detailed reporting of all issues found

### 4. Diagnostic SQL (`/debug-monitored-accounts.sql`)
- SQL queries to manually investigate data inconsistencies
- Useful for direct database debugging

## How to Fix the Issue

### Step 1: Run the Consistency Fix Script
```bash
cd apps/web
pnpm fix:monitored-accounts
```

### Step 2: Monitor Logs
The enhanced logging will now show:
- Exact account details in both UI and count queries
- Data inconsistency warnings
- Account status and creation timestamps

### Step 3: Manual Investigation (if needed)
If issues persist, run the diagnostic SQL:
```bash
# Connect to your database and run:
psql $DATABASE_URL -f debug-monitored-accounts.sql
```

## Prevention Measures

### 1. Enhanced Logging
- The system now logs detailed information about all monitored account queries
- Inconsistencies are automatically detected and logged
- Verbose logging can be enabled with `VERBOSE_LOGGING=true`

### 2. Data Validation
- The consistency script validates Twitter handles format
- Duplicate detection and cleanup
- Orphaned record cleanup

### 3. Plan Limit Enforcement
- Automatic deactivation of excess accounts
- Clear logging when limits are exceeded

## Testing the Fix

### 1. Before the Fix
- User sees 0 accounts in UI
- Adding account fails with "reached limit" error
- Logs show count mismatch

### 2. After the Fix
- Data inconsistencies are resolved
- UI and count queries return consistent results
- Clear logging shows exact account states
- Users can add accounts when UI shows they have capacity

## Monitoring

To monitor for future issues:

1. **Check Logs**: Look for "Data inconsistency detected!" warnings
2. **Regular Consistency Checks**: Run the fix script periodically
3. **Database Constraints**: The unique constraint on `(userId, twitterHandle)` prevents most duplicates

## Environment Variables

For enhanced debugging, set:
```env
VERBOSE_LOGGING=true
ENABLE_PRISMA_QUERY_LOGS=true
ENABLE_CONTEXT_LOGS=true
```

## Related Files

- `/apps/web/src/lib/user-service.ts` - Count logic
- `/apps/web/src/routers/accounts.ts` - UI data queries  
- `/apps/web/src/scripts/fix-monitored-accounts-consistency.ts` - Cleanup script
- `/apps/web/prisma/schema/schema.prisma` - Database schema
- `/debug-monitored-accounts.sql` - Diagnostic queries
- `/MONITORED_ACCOUNTS_FIX.md` - This documentation

## Success Criteria

✅ UI and count queries return consistent results  
✅ No more false "limit reached" errors when UI shows 0 accounts  
✅ Enhanced logging provides clear debugging information  
✅ Data consistency script resolves common issues automatically  
✅ Prevention measures reduce likelihood of future inconsistencies