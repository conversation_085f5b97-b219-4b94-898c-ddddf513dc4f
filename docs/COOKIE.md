Cookie.fun API 1.0 - Documentation

Overview
The ProjectsV3 section of the Cookie.fun API provides comprehensive data about crypto projects, Twitter accounts, mindshare metrics, and social media analytics. These endpoints enable users to retrieve detailed information about projects.
errorMessage: Human-readable error description
errorData: Additional error context (if applicable)

Endpoints
Sectors
Get All Sectors
Retrieves all available sectors in the system.

GET /v3/sectors

Response:
Returns a list of sectors with their details:

| Field       | Type   | Description                       |
| :---------- | :----- | :-------------------------------- |
| id          | string | Unique identifier for the sector  |
| name        | string | Display name of the sector        |
| slug        | string | URL friendly identifier for the sector |
| description | string | Detailed description of the sector |

Example Request:
GET /v3/sectors

- Follower and following counts
- Engagement metrics for various time periods (7 days, 30 days, 90 days)
- Smart metrics and mindshare data
- Account verification status

Get Smart Followers of an Account
Retrieves the top smart followers for a given Twitter account.

POST /v3/account/smart-followers

Request Body Parameters:

| Parameter | Type   | Required                               | Description             |
| :-------- | :----- | :------------------------------------- | :---------------------- |
| username  | string | One of username or userId must be provided | Twitter username (without @) |
| userId    | string | One of username or userId must be provided | Twitter user ID         |

Example Request:
```json
{
  "username": "blknoiz06"
}
```

Response:
Returns a list of the most influential followers of the specified account, with details about each follower.

Get Account Feed
Retrieves a Twitter account's feed with various filtering options.

POST /v3/account/feed

Request Body Parameters:

| Parameter  | Type     | Required | Description                                                    |
| :--------- | :------- | :------- | :------------------------------------------------------------- |
| username   | string   | One of username or userId must be provided | Twitter username (without @)                                   |
| userId     | string   | One of username or userId must be provided | Twitter user ID                                                |
| startDate  | datetime | No       | Start date for the feed range                                  |
| endDate    | datetime | No       | End date for the feed range                                    |
| type       | enum     | No       | Tweet type filter: "Original", "Reply", or "Quote"             |
| hasMedia   | boolean  | No       | Filter for tweets with media                                   |
| sortBy     | enum     | No       | Sort by: "CreatedAt" or "Impressions"                          |
| sortOrder  | enum     | No       | Sort order for results: "Ascending" or "Descending"            |

Example Request:
```json
{
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-01-31T23:59:59Z",
  "type": "Original",
  "hasMedia": true,
  "sortBy": "Impressions",
  "sortOrder": "Descending"
}
```

Limitations:
- Results are limited to 20 items per request.
- The time range between StartDate and EndDate cannot exceed 180 days.

Search Projects
Searches for projects based on a query string.

POST /v3/project/search

Request Body Parameters:

| Parameter       | Type     | Required | Description                                                    |
| :-------------- | :------- | :------- | :------------------------------------------------------------- |
| searchCategory  | string   | Yes      | Search term to match in tweets                                 |
| type            | enum     | No       | Tweet type filter: "Original", "Reply", or "Quote"             |
| startDate       | datetime | No       | Start date for the search range                                |
| endDate         | datetime | No       | End date for the search range                                  |
| sortBy          | enum     | No       | Sort by: "SmartEngagementPoints", "Impressions", or "MatchingTweetsCount" |
| sortOrder       | enum     | No       | Sort order for results: "Ascending" or "Descending"            |
| mindshareTimeframe | enum     | No       | Timeframe for mindshare data: "7Days", "30Days", or "90Days"   |
| sectorSlug      | string   | No       | Filter by sector slug. Use "Get All Sectors" to find available slugs. |

Example Request:
```json
{
  "mindshareTimeframe": "_30Days",
  "sortBy": "Mindshare",
  "sortOrder": "Descending",
  "sectorSlug": "defi"
}
```

Limitations:
- Results are limited to 20 items per request.
- The time range between StartDate and EndDate cannot exceed 180 days.

Response:
Returns a ranked list of projects based on mindshare metrics, including mindshare scores.

| Field          | Type     | Required | Description                                                    |
| :------------- | :------- | :------- | :------------------------------------------------------------- |
| granulation    | enum     | Yes      | Time granularity: "_1Hour" or "_24Hours"                         |
| projectSlug    | string   | At least one of projectSlug or searchQuery must be provided | Project slug identifier                                        |
| searchQuery    | string   | At least one of projectSlug or searchQuery must be provided | Search term for tweets                                         |
| startDate      | datetime | No       | Start date for the metrics range                               |
| endDate        | datetime | No       | End date for the metrics range                                 |
| metricType     | enum     | Yes      | Type of metric to retrieve: "EngagementRate", "Impressions", or "Mentions" |
| projectSlugs   | array of strings | Yes      | Project slug identifiers for multiple projects to compare      |
| compareBy      | enum     | Yes      | Compare projects by: "EngagementRate" or "Mentions"             |
| interval       | enum     | Yes      | Interval for comparison: "daily" or "weekly"                     |
| startDate      | datetime | Yes      | Start date for the comparison range                            |
| endDate        | datetime | Yes      | End date for the comparison range                              |

Example Request with Search Query:
```json
{
  "metricType": "Impressions",
  "granulation": "_24Hours",
  "searchQuery": "bitcoin OR $BTC",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-01-31T23:59:59Z"
}
```

Request Body Parameters:

| Parameter    | Type   | Required                               | Description                                                    |
| :----------- | :----- | :------------------------------------- | :------------------------------------------------------------- |
| searchQuery  | string | At least one of projectSlug or search-query must be provided | Search term for tweets                                         |
| projectSlug  | string | At least one of projectSlug or search-query must be provided | Project slug identifier                                        |
| type         | enum   | No                                     | Tweet type filter: "Original", "Reply", or "Quote"             |
| startDate    | datetime | No                                     | Start date for the tweets range                                |
| endDate      | datetime | No                                     | End date for the tweets range                                  |
| sortBy       | enum   | No                                     | Sort by: "CreatedAt", "Impressions", or "EngagementRate"       |
| sortOrder    | enum   | No                                     | Sort order for results: "Ascending" or "Descending"            |

Example Request:
```json
{
  "searchQuery": "Layer 2",
  "type": "Original",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-01-31T23:59:59Z",
  "sortBy": "CreatedAt",
  "sortOrder": "Descending"
}
```

Example Request with Project:
```json
{
  "searchQuery": "halving",
  "projectSlug": "bitcoin",
  "type": "Original",
  "startDate": "2023-01-01T00:00:00Z",
  "endDate": "2023-01-31T23:59:59Z"
}
```

Limitations:
- Results are limited to 20 items per request.
- The time range between StartDate and EndDate cannot exceed 180 days.

Example Request with Chain and Contract Address:
```json
{
  "chain": 1,
  "contractAddress": "******************************************"
}
```

Sector
```json
{
  "id": "123",
  "name": "Decentralized Finance",
  "slug": "defi"
}
```

Rate Limiting
API requests are subject to rate limiting based on your API key's configuration. When you exceed your rate limit, the API will return a 429 Too Many Requests status code.

Best Practices
1. Minimize API Calls: Cache responses when appropriate to reduce API usage.
2. Use Specific Filters: Narrow your search parameters to get more relevant results and reduce payload size.
3. Handle Errors Gracefully: Implement error handling to manage API errors and provide a smooth user experience.

Support
For additional assistance or to report issues, please contact our support team.