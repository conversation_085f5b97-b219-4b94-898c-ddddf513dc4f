# 🧪 Prisma Testing Guide

## 📋 Overview

This guide covers testing strategies for our centralized Prisma architecture, including unit tests, integration tests, and performance testing patterns.

---

## 🏗️ Testing Architecture

### **Test Environment Isolation**

```
┌─────────────────────────────────────────────────────────────┐
│                    Testing Architecture                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Unit Tests    │    │ Integration     │                │
│  │  (Isolated)     │    │    Tests        │                │
│  └─────────┬───────┘    └─────────┬───────┘                │
│            │                      │                        │
│            ▼                      ▼                        │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │  Test Prisma    │    │  Test Database  │                │
│  │  (In-Memory)    │    │  (Isolated)     │                │
│  └─────────────────┘    └─────────────────┘                │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┤
│  │              Shared Test Utilities                      │
│  │  • Database Cleanup  • Test Data Factory               │
│  │  • Mock Generators   • Performance Helpers             │
│  └─────────────────────────────────────────────────────────┘
└─────────────────────────────────────────────────────────────┘
```

---

## 🔬 Unit Testing

### **Service Layer Testing**

```typescript
// tests/services/user.service.test.ts
import { createPrismaClient } from '../../apps/web/src/lib/prisma-config';
import { UserService } from '../../apps/web/src/services/user.service';

describe('UserService', () => {
  let testPrisma: PrismaClient;
  let userService: UserService;

  beforeAll(async () => {
    testPrisma = createPrismaClient({
      instanceId: "test-user-service",
      databaseUrl: process.env.TEST_DATABASE_URL,
      disableMiddleware: true, // Faster tests
      logLevels: [], // No logging in tests
    });
    
    userService = new UserService(testPrisma);
  });

  afterAll(async () => {
    await testPrisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up test data
    await testPrisma.user.deleteMany({
      where: { email: { startsWith: 'test-' } }
    });
  });

  describe('createUser', () => {
    it('should create user with valid data', async () => {
      const userData = {
        id: 'test-user-1',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const user = await userService.createUser(userData);

      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
      
      // Verify in database
      const dbUser = await testPrisma.user.findUnique({
        where: { id: user.id }
      });
      expect(dbUser).toBeTruthy();
    });

    it('should throw error for duplicate email', async () => {
      const userData = {
        id: 'test-user-1',
        email: '<EMAIL>',
        name: 'Test User',
      };

      await userService.createUser(userData);

      await expect(
        userService.createUser({ ...userData, id: 'test-user-2' })
      ).rejects.toThrow('Email already exists');
    });
  });

  describe('getUserWithPlan', () => {
    it('should return user with plan details', async () => {
      // Setup test data
      const plan = await testPrisma.subscriptionPlan.create({
        data: { name: 'test-plan', displayName: 'Test Plan' }
      });
      
      const user = await testPrisma.user.create({
        data: {
          id: 'test-user-1',
          email: '<EMAIL>',
          planId: plan.id,
        }
      });

      const result = await userService.getUserWithPlan(user.id);

      expect(result.plan.name).toBe('test-plan');
      expect(result.email).toBe('<EMAIL>');
    });
  });
});
```

### **Repository Pattern Testing**

```typescript
// tests/repositories/mention.repository.test.ts
import { createPrismaClient } from '../../apps/web/src/lib/prisma-config';
import { MentionRepository } from '../../apps/web/src/repositories/mention.repository';

describe('MentionRepository', () => {
  let testPrisma: PrismaClient;
  let mentionRepo: MentionRepository;

  beforeAll(async () => {
    testPrisma = createPrismaClient({
      instanceId: "test-mention-repo",
      databaseUrl: process.env.TEST_DATABASE_URL,
    });
    
    mentionRepo = new MentionRepository(testPrisma);
  });

  afterAll(async () => {
    await testPrisma.$disconnect();
  });

  describe('findByUserId', () => {
    it('should return paginated mentions', async () => {
      // Create test data
      const mentions = await createTestMentions(testPrisma, 'test-user-1', 25);

      const result = await mentionRepo.findByUserId('test-user-1', {
        page: 1,
        limit: 10
      });

      expect(result.data).toHaveLength(10);
      expect(result.total).toBe(25);
      expect(result.hasMore).toBe(true);
    });
  });
});
```

---

## 🔗 Integration Testing

### **API Route Testing**

```typescript
// tests/integration/api/user.test.ts
import { testPrisma, cleanupDatabase } from '../../test/utils/database';
import { createMockRequest, createMockResponse } from '../../test/utils/mocks';
import { handler } from '../../apps/web/src/app/api/user/route';

describe('/api/user', () => {
  beforeEach(async () => {
    await cleanupDatabase();
  });

  describe('POST /api/user', () => {
    it('should create user and return 201', async () => {
      const userData = {
        email: '<EMAIL>',
        name: 'Integration Test User',
      };

      const req = createMockRequest('POST', userData);
      const res = createMockResponse();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      
      // Verify in database
      const user = await testPrisma.user.findUnique({
        where: { email: userData.email }
      });
      expect(user).toBeTruthy();
      expect(user.name).toBe(userData.name);
    });

    it('should handle validation errors', async () => {
      const invalidData = {
        email: 'invalid-email',
        name: '', // Empty name
      };

      const req = createMockRequest('POST', invalidData);
      const res = createMockResponse();

      await handler(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
    });
  });
});
```

### **tRPC Integration Testing**

```typescript
// tests/integration/trpc/user.test.ts
import { createTRPCMsw } from 'msw-trpc';
import { appRouter } from '../../apps/web/src/routers';
import { createContext } from '../../apps/web/src/lib/context';

describe('User tRPC Router', () => {
  let caller: ReturnType<typeof appRouter.createCaller>;

  beforeEach(async () => {
    await cleanupDatabase();
    
    // Create authenticated context
    const ctx = await createContext({
      userId: 'test-user-1',
      // ... other context
    });
    
    caller = appRouter.createCaller(ctx);
  });

  describe('user.getProfile', () => {
    it('should return user profile with plan', async () => {
      // Setup test data
      await createTestUser('test-user-1');

      const profile = await caller.user.getProfile();

      expect(profile.id).toBe('test-user-1');
      expect(profile.plan).toBeDefined();
    });

    it('should throw error for non-existent user', async () => {
      await expect(caller.user.getProfile()).rejects.toThrow();
    });
  });

  describe('user.updateProfile', () => {
    it('should update user profile', async () => {
      await createTestUser('test-user-1');

      const updated = await caller.user.updateProfile({
        name: 'Updated Name'
      });

      expect(updated.name).toBe('Updated Name');
      
      // Verify in database
      const dbUser = await testPrisma.user.findUnique({
        where: { id: 'test-user-1' }
      });
      expect(dbUser.name).toBe('Updated Name');
    });
  });
});
```

---

## 🏃‍♂️ Performance Testing

### **Query Performance Testing**

```typescript
// tests/performance/query.performance.test.ts
import { createPrismaClient } from '../../apps/web/src/lib/prisma-config';
import { performance } from 'perf_hooks';

describe('Query Performance', () => {
  let testPrisma: PrismaClient;

  beforeAll(async () => {
    testPrisma = createPrismaClient({
      instanceId: "perf-test",
      databaseUrl: process.env.TEST_DATABASE_URL,
      forceQueryLogs: true,
    });

    // Setup large dataset for testing
    await setupLargeDataset(testPrisma);
  });

  afterAll(async () => {
    await testPrisma.$disconnect();
  });

  describe('User queries', () => {
    it('should find users efficiently', async () => {
      const start = performance.now();

      const users = await testPrisma.user.findMany({
        where: { isActive: true },
        include: { plan: true },
        take: 100,
      });

      const duration = performance.now() - start;

      expect(users).toHaveLength(100);
      expect(duration).toBeLessThan(100); // Should complete in <100ms
    });

    it('should handle pagination efficiently', async () => {
      const pageSize = 20;
      const pages = 5;
      const durations: number[] = [];

      for (let page = 0; page < pages; page++) {
        const start = performance.now();

        await testPrisma.user.findMany({
          skip: page * pageSize,
          take: pageSize,
          orderBy: { createdAt: 'desc' },
        });

        durations.push(performance.now() - start);
      }

      // All pages should have similar performance
      const avgDuration = durations.reduce((a, b) => a + b) / durations.length;
      expect(avgDuration).toBeLessThan(50);

      // No page should be significantly slower
      durations.forEach(duration => {
        expect(duration).toBeLessThan(avgDuration * 2);
      });
    });
  });

  describe('Complex queries', () => {
    it('should handle joins efficiently', async () => {
      const start = performance.now();

      const result = await testPrisma.user.findMany({
        where: {
          monitoredAccounts: {
            some: { isActive: true }
          }
        },
        include: {
          plan: { include: { features: true } },
          monitoredAccounts: {
            where: { isActive: true },
            take: 5,
          }
        },
        take: 50,
      });

      const duration = performance.now() - start;

      expect(result.length).toBeGreaterThan(0);
      expect(duration).toBeLessThan(200); // Complex query should still be fast
    });
  });
});
```

### **Connection Pool Testing**

```typescript
// tests/performance/connection.performance.test.ts
describe('Connection Pool Performance', () => {
  it('should handle concurrent connections efficiently', async () => {
    const concurrentQueries = 20;
    const promises: Promise<any>[] = [];

    const start = performance.now();

    // Create multiple concurrent queries
    for (let i = 0; i < concurrentQueries; i++) {
      promises.push(
        testPrisma.user.findMany({
          take: 10,
          skip: i * 10,
        })
      );
    }

    const results = await Promise.all(promises);
    const duration = performance.now() - start;

    expect(results).toHaveLength(concurrentQueries);
    expect(duration).toBeLessThan(1000); // Should handle concurrency well
  });

  it('should not leak connections', async () => {
    const initialConnections = await getActiveConnections();

    // Perform many operations
    for (let i = 0; i < 100; i++) {
      await testPrisma.user.count();
    }

    const finalConnections = await getActiveConnections();

    // Connection count should not grow significantly
    expect(finalConnections - initialConnections).toBeLessThan(5);
  });
});

async function getActiveConnections(): Promise<number> {
  const result = await testPrisma.$queryRaw<[{ count: bigint }]>`
    SELECT count(*) FROM pg_stat_activity 
    WHERE datname = current_database()
  `;
  return Number(result[0].count);
}
```

---

## 🎭 Mocking & Test Utilities

### **Test Data Factory**

```typescript
// test/utils/factories.ts
import { faker } from '@faker-js/faker';

export class TestDataFactory {
  static createUser(overrides: Partial<User> = {}) {
    return {
      id: faker.string.uuid(),
      email: faker.internet.email(),
      name: faker.person.fullName(),
      isActive: true,
      createdAt: new Date(),
      ...overrides,
    };
  }

  static createPlan(overrides: Partial<SubscriptionPlan> = {}) {
    return {
      id: faker.string.uuid(),
      name: faker.lorem.slug(),
      displayName: faker.commerce.productName(),
      price: faker.number.int({ min: 10, max: 100 }),
      ...overrides,
    };
  }

  static async createTestUser(
    prisma: PrismaClient, 
    overrides: Partial<User> = {}
  ) {
    const userData = this.createUser(overrides);
    return prisma.user.create({ data: userData });
  }

  static async createUserWithPlan(
    prisma: PrismaClient,
    userOverrides: Partial<User> = {},
    planOverrides: Partial<SubscriptionPlan> = {}
  ) {
    const plan = await prisma.subscriptionPlan.create({
      data: this.createPlan(planOverrides)
    });

    return prisma.user.create({
      data: this.createUser({ ...userOverrides, planId: plan.id }),
      include: { plan: true }
    });
  }
}
```

### **Database Cleanup Utilities**

```typescript
// test/utils/cleanup.ts
export async function cleanupDatabase(prisma: PrismaClient) {
  // Delete in correct order to respect foreign key constraints
  await prisma.$transaction([
    prisma.usageLog.deleteMany({
      where: { userId: { startsWith: 'test-' } }
    }),
    prisma.aIResponse.deleteMany({
      where: { userId: { startsWith: 'test-' } }
    }),
    prisma.mention.deleteMany({
      where: { userId: { startsWith: 'test-' } }
    }),
    prisma.monitoredAccount.deleteMany({
      where: { userId: { startsWith: 'test-' } }
    }),
    prisma.user.deleteMany({
      where: { id: { startsWith: 'test-' } }
    }),
    // Clean up test plans
    prisma.subscriptionPlan.deleteMany({
      where: { name: { startsWith: 'test-' } }
    }),
  ]);
}

export async function resetTestDatabase(prisma: PrismaClient) {
  // More aggressive cleanup for integration tests
  const tablenames = await prisma.$queryRaw<Array<{ tablename: string }>>`
    SELECT tablename FROM pg_tables WHERE schemaname='public'
  `;

  for (const { tablename } of tablenames) {
    if (tablename !== '_prisma_migrations') {
      await prisma.$executeRawUnsafe(`TRUNCATE TABLE "public"."${tablename}" CASCADE;`);
    }
  }
}
```

---

## 🚀 Test Execution Strategies

### **Test Environment Setup**

```bash
# .env.test
DATABASE_URL="postgresql://user:pass@localhost:5432/buddychip_test"
DIRECT_URL="postgresql://user:pass@localhost:5432/buddychip_test"
NODE_ENV="test"
ENABLE_PRISMA_QUERY_LOGS="false"
```

### **Jest Configuration**

```javascript
// jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/test/setup/jest.setup.ts'],
  globalSetup: '<rootDir>/test/setup/global-setup.ts',
  globalTeardown: '<rootDir>/test/setup/global-teardown.ts',
  testMatch: [
    '<rootDir>/tests/**/*.test.ts',
    '<rootDir>/apps/web/src/**/*.test.ts',
  ],
  collectCoverageFrom: [
    'apps/web/src/**/*.ts',
    '!apps/web/src/**/*.test.ts',
    '!apps/web/src/prisma/generated/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### **Test Scripts**

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:integration": "jest --testPathPattern=integration",
    "test:unit": "jest --testPathPattern=unit",
    "test:performance": "jest --testPathPattern=performance",
    "test:db:setup": "tsx test/setup/test-db-setup.ts",
    "test:db:teardown": "tsx test/setup/test-db-teardown.ts"
  }
}
```

---

## 🎯 Testing Best Practices

### **✅ DO: Isolate Tests**
```typescript
// Each test should be independent
beforeEach(async () => {
  await cleanupDatabase(testPrisma);
});
```

### **✅ DO: Use Transactions for Test Data**
```typescript
// Faster cleanup with transactions
await testPrisma.$transaction(async (tx) => {
  const user = await tx.user.create({ data: userData });
  const account = await tx.monitoredAccount.create({ 
    data: { ...accountData, userId: user.id } 
  });
  
  // Test logic here
  
  // Transaction automatically rolls back
});
```

### **✅ DO: Test Error Conditions**
```typescript
it('should handle database connection failure', async () => {
  // Mock connection failure
  jest.spyOn(testPrisma, '$connect').mockRejectedValue(
    new Error('Connection failed')
  );

  await expect(userService.createUser(userData)).rejects.toThrow();
});
```

### **❌ DON'T: Share State Between Tests**
```typescript
// ❌ Wrong - shared state
let sharedUser: User;

beforeAll(async () => {
  sharedUser = await createTestUser(); // Bad!
});

// ✅ Correct - isolated state
beforeEach(async () => {
  await cleanupDatabase();
});
```

### **❌ DON'T: Test Implementation Details**
```typescript
// ❌ Wrong - testing internal implementation
expect(userService.prisma.user.create).toHaveBeenCalled();

// ✅ Correct - testing behavior
expect(result.email).toBe(expectedEmail);
```

---

## 📊 Test Metrics & Monitoring

### **Coverage Goals**
- **Unit Tests**: >90% coverage
- **Integration Tests**: >80% coverage  
- **Critical Paths**: 100% coverage
- **Performance Tests**: Key queries benchmarked

### **Performance Benchmarks**
- **Simple Queries**: <50ms
- **Complex Queries**: <200ms
- **Bulk Operations**: <500ms per 100 records
- **Connection Pool**: <10 active connections

---

## 🎉 Summary

This testing strategy ensures:
- **Reliability** through comprehensive test coverage
- **Performance** through benchmarking and monitoring
- **Maintainability** through isolated, repeatable tests
- **Confidence** in database operations and business logic

Remember: **Test behavior, not implementation. Keep tests fast, isolated, and deterministic.** 🚀
