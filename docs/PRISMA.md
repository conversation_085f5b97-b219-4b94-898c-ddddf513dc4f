# PRISMA.md - Database Schema Management Guide

**BuddyChip Project - Prisma Workflow Documentation**

## 🎯 Overview

This document explains exactly what happens every time we push a new Prisma schema in the BuddyChip project, including the cascading effects, file changes, and potential issues that may arise.

---

## 📋 Table of Contents

1. [Project-Specific Prisma Setup](#project-specific-prisma-setup)
2. [What Happens During Schema Push](#what-happens-during-schema-push)
3. [File Changes and Regeneration](#file-changes-and-regeneration)
4. [Development vs Production Workflow](#development-vs-production-workflow)
5. [Common Issues and Solutions](#common-issues-and-solutions)
6. [Best Practices](#best-practices)
7. [Emergency Recovery](#emergency-recovery)

---

## 🏗️ Project-Specific Prisma Setup

### **Custom Configuration**
```typescript
// apps/web/prisma.config.ts
export default {
  schema: './prisma/schema/schema.prisma',  // Custom schema location
  output: './prisma/generated',             // Custom client output
}
```

### **Key Locations**
- **Schema**: `apps/web/prisma/schema/schema.prisma`
- **Generated Client**: `apps/web/prisma/generated/`
- **Seed Script**: `apps/web/prisma/seed.ts`
- **Configuration**: `apps/web/prisma.config.ts`

### **Database Configuration**
```env
DATABASE_URL=postgresql://...     # Connection string for queries
DIRECT_URL=postgresql://...       # Direct connection for migrations
```

---

## ⚡ What Happens During Schema Push

### **1. Command Execution**
```bash
pnpm db:push  # Runs: prisma db push --schema ./prisma/schema/schema.prisma
```

### **2. Schema Validation Phase**
```
🔍 Prisma validates schema syntax
├── Checks model definitions
├── Validates field types and constraints
├── Verifies relationship integrity
├── Ensures unique constraint compliance
└── Validates enum values
```

### **3. Database Introspection**
```
🔎 Prisma compares schema vs current database state
├── Reads current database structure
├── Identifies structural differences
├── Detects new tables, columns, indexes
├── Finds removed or modified fields
└── Plans necessary changes
```

### **4. Migration Planning**
```
📝 Prisma generates SQL commands
├── CREATE TABLE statements for new models
├── ALTER TABLE for field modifications
├── CREATE INDEX for new indexes
├── DROP statements for removed items
└── Data migration scripts (if needed)
```

### **5. Database Update Execution**
```sql
-- Example generated SQL for recent persona integration:
ALTER TABLE "PersonaGenerationJob" 
ADD COLUMN "memoriesStored" INTEGER DEFAULT 0,
ADD COLUMN "memoryStorageProgress" INTEGER DEFAULT 0,
ADD COLUMN "memoryErrors" JSONB;

ALTER TYPE "FeatureType" 
ADD VALUE 'PERSONA_MEMORY_OPS';

-- Index creation for performance
CREATE INDEX "PersonalityProfile_isUserGenerated_idx" 
ON "PersonalityProfile"("isUserGenerated");
```

### **6. Client Regeneration**
```
🔄 Prisma regenerates client with new types
├── TypeScript interfaces updated
├── Query methods added/modified  
├── Enum types synchronized
├── Relationship helpers updated
└── Runtime validation updated
```

---

## 📁 File Changes and Regeneration

### **Automatically Generated Files**
```
apps/web/prisma/generated/
├── index.js                    # Main client entry point
├── index.d.ts                  # TypeScript definitions (1.5MB+)
├── index-browser.js            # Browser-compatible client
├── edge.js                     # Edge runtime client
├── wasm.js                     # WebAssembly client
├── package.json                # Generated package info
├── schema.prisma               # Copy of source schema
├── runtime/                    # Prisma runtime files
│   ├── library.js
│   ├── edge.js
│   └── wasm-***.js
└── libquery_engine-***.so.node # Native query engines
```

### **Files That Import Prisma Client**
These files get new TypeScript types automatically:
```typescript
// All these files get updated types:
- apps/web/src/lib/db-utils.ts
- apps/web/src/routers/*.ts 
- apps/web/src/lib/*-service.ts
- apps/web/prisma/seed.ts
```

### **Seed File Updates**
```typescript
// apps/web/prisma/seed.ts may need updates for:
- New enum values (FeatureType.PERSONA_MEMORY_OPS)
- New plan features
- Default data for new models
```

---

## 🔄 Development vs Production Workflow

### **Development Workflow**
```bash
# 1. Modify schema
vim apps/web/prisma/schema/schema.prisma

# 2. Push changes (development only)
pnpm db:push

# 3. Regenerate client (if not auto-triggered)
pnpm db:generate

# 4. Update seed data if needed
pnpm db:seed

# 5. Run type checking
pnpm check-types
```

### **Production Workflow**
```bash
# 1. Create migration files
pnpm db:migrate dev --name describe_changes

# 2. Commit migration files to git
git add prisma/migrations/

# 3. Deploy to production
pnpm db:migrate deploy

# 4. Generate client in production
pnpm db:generate
```

### **Key Differences**
| Aspect | Development (`db:push`) | Production (`db:migrate`) |
|--------|------------------------|---------------------------|
| **Migration Files** | ❌ Not created | ✅ Created in `prisma/migrations/` |
| **Version Control** | ❌ Changes not tracked | ✅ Tracked in git |
| **Rollback** | ❌ Manual database restore | ✅ Migration rollback possible |
| **Team Sync** | ❌ Others need to pull schema | ✅ Migration files shared |
| **Safety** | ⚠️ Data loss possible | ✅ Safer, reviewable |

---

## ⚠️ Common Issues and Solutions

### **1. TypeScript Compilation Errors**
```bash
# Error: Property 'newField' does not exist on type...
# Solution: Regenerate Prisma client
pnpm db:generate
```

### **2. Enum Value Conflicts**
```prisma
// Problem: Adding enum value
enum FeatureType {
  AI_CALLS
  PERSONA_MEMORY_OPS  // New value
}

// Database Error: enum value already exists
// Solution: Check if value already exists in DB
```

### **3. Foreign Key Constraint Violations**
```sql
-- Problem: Adding required foreign key to existing data
model User {
  personalityId String  // Adding this as required field
}

-- Solution: Make field optional first, then add data, then make required
```

### **4. Index Name Conflicts**
```bash
# Error: index "idx_name" already exists
# Solution: Drop conflicting index first or use different name
```

### **5. Large Migration Timeouts**
```bash
# Problem: Migration takes too long
# Solution: Break into smaller migrations or increase timeout
```

### **6. Client Generation Failures**
```bash
# Error: Unable to generate Prisma Client
# Solutions:
rm -rf apps/web/prisma/generated/
pnpm db:generate
```

---

## 🚀 Best Practices

### **1. Schema Change Strategy**
```prisma
// ✅ Good: Additive changes
model User {
  id    String @id
  email String
  newField String? // Optional new field
}

// ❌ Risky: Breaking changes
model User {
  id       String @id
  username String // Renamed from 'email' - breaks existing code
}
```

### **2. Migration Naming**
```bash
# ✅ Descriptive names
pnpm db:migrate dev --name "add_persona_memory_tracking"
pnpm db:migrate dev --name "update_user_subscription_fields"

# ❌ Generic names
pnpm db:migrate dev --name "changes"
pnpm db:migrate dev --name "update"
```

### **3. Data Migration Strategy**
```sql
-- For data transformations, use custom migration
-- Example: Converting string to enum
UPDATE "User" SET "status" = 'ACTIVE' WHERE "status" IS NULL;
ALTER TABLE "User" ALTER COLUMN "status" SET NOT NULL;
```

### **4. Testing Schema Changes**
```bash
# Always test locally first
pnpm db:push              # Test schema
pnpm check-types          # Verify TypeScript
pnpm test                 # Run tests
pnpm db:seed              # Test with data
```

### **5. Backup Before Major Changes**
```bash
# Backup production database
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Test migration on copy first
```

---

## 🆘 Emergency Recovery

### **1. Schema Push Failure**
```bash
# If push fails mid-way:
pnpm db:push --force-reset  # WARNING: Deletes all data
# Then restore from backup
```

### **2. Broken TypeScript Types**
```bash
# Reset and regenerate everything:
rm -rf apps/web/prisma/generated/
pnpm db:generate
pnpm check-types
```

### **3. Database Corruption**
```bash
# Restore from backup:
psql $DATABASE_URL < backup_file.sql

# Then push schema again:
pnpm db:push
```

### **4. Migration Conflicts**
```bash
# Reset migration state (DANGEROUS):
pnpm db:migrate reset
pnpm db:push
pnpm db:seed
```

---

## 📊 Schema Change Checklist

### **Before Schema Changes**
- [ ] Backup production database
- [ ] Document the changes and rationale
- [ ] Plan for data migration if needed
- [ ] Review impact on existing code

### **During Schema Changes**
- [ ] Use descriptive field names
- [ ] Add proper indexes for performance
- [ ] Consider nullable fields for backwards compatibility
- [ ] Validate enum values don't conflict

### **After Schema Push**
- [ ] Regenerate Prisma client (`pnpm db:generate`)
- [ ] Run TypeScript compilation (`pnpm check-types`)
- [ ] Update seed data if needed (`pnpm db:seed`)
- [ ] Run tests to verify functionality
- [ ] Update any affected API endpoints
- [ ] Update documentation

### **Production Deployment**
- [ ] Create proper migration files
- [ ] Test migration on staging environment
- [ ] Schedule maintenance window if needed
- [ ] Run migration in production
- [ ] Verify application functionality
- [ ] Monitor for errors after deployment

---

## 🔧 Troubleshooting Commands

```bash
# View current database schema
pnpm db:studio

# Reset database completely (DANGEROUS)
pnpm db:reset

# View migration status
pnpm db:migrate status

# Regenerate client only
pnpm db:generate

# Force push schema (overwrites DB)
pnpm db:push --force-reset

# Create migration from schema diff
pnpm db:migrate dev

# Apply pending migrations
pnpm db:migrate deploy

# Introspect existing database
pnpm db:pull
```

---

## 📈 Performance Considerations

### **Index Strategy**
```prisma
// Add indexes for frequently queried fields
model User {
  email String @unique
  planId String
  
  @@index([planId])        // For plan-based queries
  @@index([createdAt])     // For date-based queries  
  @@index([email, planId]) // Compound index for complex queries
}
```

### **Query Optimization**
```prisma
// Use include/select to fetch only needed data
const users = await prisma.user.findMany({
  select: {
    id: true,
    email: true,
    plan: { select: { name: true } }
  }
})
```

---

## 🎯 Conclusion

Understanding the Prisma schema push workflow is crucial for maintaining database integrity and application stability. Always test changes locally, use migrations for production, and maintain proper backups.

**Key Takeaways:**
- `db:push` is for development iteration
- `db:migrate` is for production deployment  
- Always regenerate client after schema changes
- Test TypeScript compilation after changes
- Backup before major modifications

For questions or issues, refer to the [Prisma Documentation](https://www.prisma.io/docs) or consult the team lead.

---

*Last Updated: 2025-01-07*
*Project: BuddyChip Ultimate*
*Prisma Version: 6.10.0*