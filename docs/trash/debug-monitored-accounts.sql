-- Debug script to investigate monitored accounts data inconsistency
-- This script helps identify the root cause of the 403 error vs UI showing 0 accounts

-- First, let's see all monitored accounts for all users
SELECT 
  ma.id,
  ma.user_id,
  ma.twitter_handle,
  ma.display_name,
  ma.is_active,
  ma.created_at,
  ma.updated_at,
  u.email as user_email,
  u.name as user_name
FROM monitored_accounts ma
JOIN users u ON ma.user_id = u.id
ORDER BY ma.user_id, ma.created_at DESC;

-- Count by user and active status
SELECT 
  ma.user_id,
  u.email as user_email,
  COUNT(*) as total_accounts,
  COUNT(CASE WHEN ma.is_active = true THEN 1 END) as active_accounts,
  COUNT(CASE WHEN ma.is_active = false THEN 1 END) as inactive_accounts
FROM monitored_accounts ma
JOIN users u ON ma.user_id = u.id
GROUP BY ma.user_id, u.email
ORDER BY ma.user_id;

-- Check for duplicate accounts per user
SELECT 
  user_id,
  twitter_handle,
  COUNT(*) as duplicate_count
FROM monitored_accounts
GROUP BY user_id, twitter_handle
HAVING COUNT(*) > 1;

-- Check for accounts with NULL values that might affect queries
SELECT 
  id,
  user_id,
  twitter_handle,
  is_active,
  CASE WHEN is_active IS NULL THEN 'NULL' ELSE is_active::text END as is_active_status
FROM monitored_accounts
WHERE is_active IS NULL;

-- Check user plan limits
SELECT 
  u.id as user_id,
  u.email,
  sp.name as plan_name,
  pf.feature,
  pf.limit
FROM users u
JOIN subscription_plans sp ON u.plan_id = sp.id
JOIN plan_features pf ON sp.id = pf.plan_id
WHERE pf.feature = 'MONITORED_ACCOUNTS'
ORDER BY u.email;