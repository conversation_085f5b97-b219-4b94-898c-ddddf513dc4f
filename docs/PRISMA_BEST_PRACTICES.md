# 🎯 Prisma Best Practices Guide

## 📋 Overview

This guide provides comprehensive best practices for working with our centralized Prisma architecture. Follow these patterns to ensure optimal performance, security, and maintainability.

---

## 🏗️ Architecture Patterns

### **✅ Web Application Code**

**Use the Singleton Pattern**
```typescript
// ✅ CORRECT - Import singleton instance
import { prisma } from '../lib/db-utils';

export async function getUserById(id: string) {
  return prisma.user.findUnique({
    where: { id },
    include: { plan: true }
  });
}

export async function createUser(data: CreateUserData) {
  return prisma.user.create({ data });
}
```

**tRPC Router Example**
```typescript
// ✅ CORRECT - Use singleton in routers
import { prisma } from '../lib/db-utils';
import { createTRPCRouter, protectedProcedure } from '../lib/trpc';

export const userRouter = createTRPCRouter({
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    return prisma.user.findUnique({
      where: { id: ctx.userId },
      include: { plan: { include: { features: true } } }
    });
  }),
});
```

### **✅ Database Scripts**

**Simple Script Pattern**
```typescript
// ✅ CORRECT - Use script utilities
import { runScript } from '../lib/script-utils';

async function seedAIModels(prisma: any) {
  const models = [
    { name: "Workhorse", modelId: "google/gemini-2.5-flash" },
    { name: "Smarty", modelId: "google/gemini-2.5-pro" },
  ];

  for (const model of models) {
    await prisma.aIModel.upsert({
      where: { name: model.name },
      update: model,
      create: model,
    });
    console.log(`✅ Seeded model: ${model.name}`);
  }
}

// Execute with proper lifecycle management
runScript("seed-ai-models", seedAIModels, {
  verbose: true,
  maxRetries: 3,
});
```

**Migration Script Pattern**
```typescript
// ✅ CORRECT - Use migration utilities
import { runMigration } from '../lib/script-utils';

async function migrateUserData() {
  return runMigration(
    "migrate-user-plans",
    // Get data to migrate
    (prisma) => prisma.user.findMany({ where: { planId: null } }),
    // Migrate each item
    async (user, prisma) => {
      await prisma.user.update({
        where: { id: user.id },
        data: { planId: "default-plan-id" }
      });
    },
    {
      batchSize: 50,
      continueOnError: false,
      verbose: true,
    }
  );
}

migrateUserData();
```

**Batch Processing Pattern**
```typescript
// ✅ CORRECT - Process large datasets efficiently
import { processBatches } from '../lib/script-utils';

async function updateManyUsers() {
  const userIds = await getUserIdsToUpdate(); // Get large list
  
  await processBatches(
    userIds,
    100, // Process 100 at a time
    async (batch, prisma) => {
      await prisma.user.updateMany({
        where: { id: { in: batch } },
        data: { lastProcessed: new Date() }
      });
    },
    {
      scriptName: "bulk-user-update",
      verbose: true,
    }
  );
}
```

### **✅ Testing Patterns**

**Unit Tests**
```typescript
// ✅ CORRECT - Create test-specific instance
import { createPrismaClient } from '../lib/prisma-config';

describe('User Service', () => {
  let testPrisma: PrismaClient;

  beforeAll(async () => {
    testPrisma = createPrismaClient({
      instanceId: "test-user-service",
      databaseUrl: process.env.TEST_DATABASE_URL,
      disableMiddleware: true, // Skip middleware for faster tests
    });
  });

  afterAll(async () => {
    await testPrisma.$disconnect();
  });

  it('should create user', async () => {
    const user = await testPrisma.user.create({
      data: { id: "test-user", email: "<EMAIL>" }
    });
    expect(user.email).toBe("<EMAIL>");
  });
});
```

**Integration Tests**
```typescript
// ✅ CORRECT - Use shared test utilities
import { testPrisma, cleanupDatabase } from '../../test/utils/database';

describe('User Integration', () => {
  beforeEach(async () => {
    await cleanupDatabase();
  });

  it('should handle user workflow', async () => {
    // Use testPrisma for integration tests
    const user = await testPrisma.user.create({ data: userData });
    // ... test workflow
  });
});
```

---

## 🚀 Performance Best Practices

### **Query Optimization**

**✅ Use Efficient Includes**
```typescript
// ✅ CORRECT - Only include what you need
const user = await prisma.user.findUnique({
  where: { id },
  include: {
    plan: {
      select: { name: true, features: true } // Only needed fields
    }
  }
});

// ❌ AVOID - Over-fetching data
const user = await prisma.user.findUnique({
  where: { id },
  include: { 
    plan: true, 
    monitoredAccounts: true, 
    aiResponses: true // Potentially huge dataset
  }
});
```

**✅ Use Pagination for Large Datasets**
```typescript
// ✅ CORRECT - Implement cursor-based pagination
async function getUserMentions(userId: string, cursor?: string) {
  return prisma.mention.findMany({
    where: { userId },
    take: 20,
    ...(cursor && { cursor: { id: cursor }, skip: 1 }),
    orderBy: { createdAt: 'desc' }
  });
}
```

**✅ Optimize Batch Operations**
```typescript
// ✅ CORRECT - Use transactions for related operations
await prisma.$transaction(async (tx) => {
  const user = await tx.user.create({ data: userData });
  await tx.monitoredAccount.createMany({
    data: accounts.map(acc => ({ ...acc, userId: user.id }))
  });
});

// ✅ CORRECT - Use createMany for bulk inserts
await prisma.mention.createMany({
  data: mentions,
  skipDuplicates: true
});
```

### **Connection Management**

**✅ Proper Transaction Usage**
```typescript
// ✅ CORRECT - Use withTransaction utility
import { withTransaction } from '../lib/db-utils';

await withTransaction(async (tx) => {
  const user = await tx.user.update({ where: { id }, data: updates });
  await tx.usageLog.create({ data: { userId: id, action: 'update' } });
  return user;
});
```

**✅ Health Checks in Critical Paths**
```typescript
// ✅ CORRECT - Check connection health before critical operations
import { checkPrismaConnection } from '../lib/prisma-config';

async function criticalOperation() {
  const isHealthy = await checkPrismaConnection(prisma, "critical-op");
  if (!isHealthy) {
    throw new Error("Database connection unhealthy");
  }
  
  // Proceed with critical operation
  return await performCriticalWork();
}
```

---

## 🔒 Security Best Practices

### **Input Validation**

**✅ Validate User Input**
```typescript
// ✅ CORRECT - Validate before database operations
import { z } from 'zod';

const CreateUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
});

async function createUser(input: unknown) {
  const data = CreateUserSchema.parse(input); // Throws if invalid
  return prisma.user.create({ data });
}
```

**✅ Use Parameterized Queries**
```typescript
// ✅ CORRECT - Prisma automatically parameterizes
const users = await prisma.user.findMany({
  where: {
    email: { contains: searchTerm }, // Safe - parameterized
    isActive: true
  }
});

// ✅ CORRECT - Even with raw queries
const result = await prisma.$queryRaw`
  SELECT * FROM users 
  WHERE email LIKE ${`%${searchTerm}%`}
  AND is_active = true
`;
```

### **Access Control**

**✅ Implement Row-Level Security**
```typescript
// ✅ CORRECT - Always filter by user context
async function getUserMentions(userId: string, requestingUserId: string) {
  if (userId !== requestingUserId) {
    throw new Error("Unauthorized access");
  }
  
  return prisma.mention.findMany({
    where: { userId } // Ensures user can only see their data
  });
}
```

**✅ Sanitize Error Messages**
```typescript
// ✅ CORRECT - Don't expose internal details
try {
  return await prisma.user.create({ data });
} catch (error) {
  if (error.code === 'P2002') {
    throw new Error("Email already exists");
  }
  // Log full error internally, return generic message
  console.error("Database error:", error);
  throw new Error("Unable to create user");
}
```

---

## 📊 Monitoring & Debugging

### **Performance Monitoring**

**✅ Enable Query Logging in Development**
```bash
# .env.local
ENABLE_PRISMA_QUERY_LOGS=true
VERBOSE_LOGGING=true
```

**✅ Monitor Slow Queries**
```typescript
// Automatic slow query detection is built-in
// Look for logs like:
// 🐌 Slow Query [web-app-singleton]: User.findMany took 1250ms
```

**✅ Custom Performance Tracking**
```typescript
// ✅ CORRECT - Add custom timing for complex operations
async function complexUserOperation(userId: string) {
  const startTime = Date.now();
  
  try {
    const result = await performComplexOperation(userId);
    const duration = Date.now() - startTime;
    
    if (duration > 500) {
      console.warn(`Slow operation for user ${userId}: ${duration}ms`);
    }
    
    return result;
  } catch (error) {
    console.error(`Operation failed for user ${userId}:`, error);
    throw error;
  }
}
```

### **Error Handling**

**✅ Comprehensive Error Handling**
```typescript
// ✅ CORRECT - Handle different error types
async function safeUserOperation(userId: string) {
  try {
    return await prisma.user.findUniqueOrThrow({
      where: { id: userId }
    });
  } catch (error) {
    if (error.code === 'P2025') {
      throw new Error(`User ${userId} not found`);
    }
    if (error.code === 'P2002') {
      throw new Error("Duplicate entry");
    }
    if (error.message.includes('connection')) {
      throw new Error("Database connection failed");
    }
    throw error; // Re-throw unknown errors
  }
}
```

---

## 🚫 Anti-Patterns to Avoid

### **❌ DON'T: Create Multiple Instances**
```typescript
// ❌ WRONG - Creates separate connection pool
import { PrismaClient } from '../prisma/generated';
const myPrisma = new PrismaClient();
```

### **❌ DON'T: Forget Cleanup in Scripts**
```typescript
// ❌ WRONG - No cleanup, potential connection leak
const prisma = new PrismaClient();
await doWork();
// Missing: await prisma.$disconnect();
```

### **❌ DON'T: Over-fetch Data**
```typescript
// ❌ WRONG - Fetches unnecessary data
const user = await prisma.user.findUnique({
  where: { id },
  include: {
    monitoredAccounts: {
      include: {
        mentions: {
          include: {
            aiResponses: true // Potentially massive dataset
          }
        }
      }
    }
  }
});
```

### **❌ DON'T: Ignore Error Handling**
```typescript
// ❌ WRONG - No error handling
const user = await prisma.user.create({ data });
// What if email already exists? Connection fails?
```

### **❌ DON'T: Use Raw Queries Without Parameterization**
```typescript
// ❌ WRONG - SQL injection risk
const result = await prisma.$queryRawUnsafe(
  `SELECT * FROM users WHERE email = '${email}'`
);
```

---

## 🎯 Quick Reference

### **Import Patterns**
```typescript
// Web app code
import { prisma } from '../lib/db-utils';

// Scripts
import { runScript, runMigration } from '../lib/script-utils';

// Tests
import { createPrismaClient } from '../lib/prisma-config';
```

### **Common Operations**
```typescript
// Simple query
const users = await prisma.user.findMany();

// With relations
const user = await prisma.user.findUnique({
  where: { id },
  include: { plan: true }
});

// Transaction
await withTransaction(async (tx) => {
  // Multiple operations
});

// Script execution
await runScript("script-name", async (prisma) => {
  // Script logic
});
```

### **Environment Variables**
```bash
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."
ENABLE_PRISMA_QUERY_LOGS="true"  # Development only
VERBOSE_LOGGING="true"           # Development only
```

---

## 🎉 Summary

Following these best practices ensures:
- **Optimal Performance** through efficient queries and connection management
- **Security** through proper validation and access control
- **Maintainability** through consistent patterns and error handling
- **Reliability** through comprehensive monitoring and testing

Remember: **Use the singleton for web app code, use script utilities for scripts, and create test instances for testing.** 🚀
