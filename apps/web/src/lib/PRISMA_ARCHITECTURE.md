# Prisma Client Architecture

## 🏗️ **Architecture Overview**

This document describes the centralized Prisma client architecture that ensures consistent database connections, logging, and middleware across the entire BuddyChip application.

## 📁 **File Structure**

```
apps/web/src/lib/
├── prisma-config.ts      # Centralized configuration factory
├── db-utils.ts           # Singleton instance for web app
├── script-utils.ts       # Utilities for database scripts
└── PRISMA_ARCHITECTURE.md # This documentation
```

## 🔧 **Core Components**

### 1. **Prisma Configuration Factory** (`prisma-config.ts`)

**Purpose**: Centralized configuration for all Prisma client instances

**Key Features**:
- ✅ Standardized logging configuration
- ✅ Performance monitoring middleware
- ✅ Soft delete middleware
- ✅ Connection health monitoring
- ✅ Environment-specific settings

**Usage**:
```typescript
import { createPrismaClient } from './prisma-config';

const client = createPrismaClient({
  instanceId: "my-service",
  databaseUrl: process.env.DATABASE_URL,
  forceQueryLogs: true,
});
```

### 2. **Web App Singleton** (`db-utils.ts`)

**Purpose**: Single Prisma instance for the web application

**Key Features**:
- ✅ Singleton pattern with hot-reload support
- ✅ Automatic middleware registration
- ✅ Consistent configuration
- ✅ Memory optimization

**Usage**:
```typescript
import { prisma } from './db-utils';

// Use throughout the web application
const users = await prisma.user.findMany();
```

### 3. **Script Utilities** (`script-utils.ts`)

**Purpose**: Utilities for database scripts with proper lifecycle management

**Key Features**:
- ✅ Automatic connection management
- ✅ Health checks with retries
- ✅ Proper cleanup on exit
- ✅ Error handling and logging
- ✅ Transaction support
- ✅ Batch processing utilities

**Usage**:
```typescript
import { runScript } from './script-utils';

// Simple script
await runScript("my-script", async (prisma) => {
  await prisma.user.updateMany({ data: { isActive: true } });
});

// Migration script
await runMigration("migrate-users", 
  (prisma) => prisma.user.findMany(),
  (user, prisma) => migrateUser(user, prisma)
);
```

## 🎯 **Usage Patterns**

### **Web Application Code**
```typescript
// ✅ CORRECT - Use singleton
import { prisma } from '../lib/db-utils';

export async function getUser(id: string) {
  return prisma.user.findUnique({ where: { id } });
}
```

### **Database Scripts**
```typescript
// ✅ CORRECT - Use script utilities
import { runScript } from '../lib/script-utils';

async function seedData(prisma: any) {
  await prisma.user.createMany({ data: users });
}

runScript("seed-users", seedData, { verbose: true });
```

### **Test Files**
```typescript
// ✅ CORRECT - Tests can create separate instances
import { createPrismaClient } from '../lib/prisma-config';

const testPrisma = createPrismaClient({
  instanceId: "test",
  databaseUrl: process.env.TEST_DATABASE_URL,
  disableMiddleware: true,
});
```

## 🚫 **Anti-Patterns**

### **❌ DON'T: Create direct instances in app code**
```typescript
// ❌ WRONG - Creates separate connection pool
import { PrismaClient } from '../prisma/generated';
const prisma = new PrismaClient();
```

### **❌ DON'T: Skip script utilities**
```typescript
// ❌ WRONG - No proper cleanup or error handling
import { PrismaClient } from '../prisma/generated';
const prisma = new PrismaClient();
await prisma.user.updateMany({});
// Missing: await prisma.$disconnect();
```

## 🔍 **Middleware Features**

### **Performance Monitoring**
- Logs query execution time
- Warns about slow queries (>1000ms)
- Tracks database performance

### **Soft Deletes**
- Automatically converts `delete` to `update` with `isDeleted: true`
- Filters out soft-deleted records from queries
- Applies to: User, Account, AIResponse models

### **Connection Health**
- Monitors connection errors
- Logs connection issues with context
- Provides health check utilities

## 📊 **Benefits**

### **Consistency**
- ✅ Same configuration across all environments
- ✅ Unified logging and monitoring
- ✅ Consistent middleware behavior

### **Performance**
- ✅ Single connection pool for web app
- ✅ Optimized connection management
- ✅ Memory efficiency with singleton pattern

### **Reliability**
- ✅ Automatic error handling
- ✅ Connection health monitoring
- ✅ Proper cleanup in scripts

### **Developer Experience**
- ✅ Simple import patterns
- ✅ Comprehensive error messages
- ✅ Built-in utilities for common tasks

## 🔧 **Configuration Options**

### **PrismaConfigOptions**
```typescript
interface PrismaConfigOptions {
  databaseUrl?: string;        // Custom database URL
  forceQueryLogs?: boolean;    // Enable query logging
  disableMiddleware?: boolean; // Skip middleware (tests)
  logLevels?: Prisma.LogLevel[]; // Custom log levels
  instanceId?: string;         // Instance identifier
}
```

### **ScriptOptions**
```typescript
interface ScriptOptions {
  scriptName?: string;      // Script name for logging
  verbose?: boolean;        // Enable verbose logging
  databaseUrl?: string;     // Custom database URL
  skipHealthCheck?: boolean; // Skip connection check
  maxRetries?: number;      // Connection retry attempts
}
```

## 🚀 **Migration Guide**

### **From Direct Imports**
```typescript
// Before
import { PrismaClient } from '../prisma/generated';
const prisma = new PrismaClient();

// After
import { prisma } from '../lib/db-utils';
```

### **From Script Direct Usage**
```typescript
// Before
const prisma = new PrismaClient();
await doWork();
await prisma.$disconnect();

// After
import { runScript } from '../lib/script-utils';
await runScript("my-script", doWork);
```

## 📈 **Monitoring**

### **Health Checks**
```typescript
import { checkPrismaConnection } from './prisma-config';

const isHealthy = await checkPrismaConnection(prisma, "web-app");
```

### **Performance Logs**
```
⏱️ Prisma [web-app-singleton]: User.findMany took 45ms
🐌 Slow Query [web-app-singleton]: User.findMany took 1250ms
```

### **Connection Logs**
```
🔍 Prisma Config [web-app-singleton]: Initializing client
✅ Prisma Health Check [web-app-singleton]: Connection healthy
🔌 Prisma [web-app-singleton]: Disconnected successfully
```

## 🎉 **Result**

This architecture provides:
- **Single source of truth** for Prisma configuration
- **Consistent behavior** across web app, scripts, and tests
- **Proper lifecycle management** with automatic cleanup
- **Enhanced observability** with comprehensive logging
- **Better error handling** with context and retries
- **Developer-friendly** utilities for common patterns

The inconsistent client initialization issue is now **completely resolved** with a robust, maintainable architecture that scales with the application.
