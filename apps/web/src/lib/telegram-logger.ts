/**
 * Telegram Agent Logging System
 * 
 * Comprehensive logging for Telegram bot operations with structured output
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export interface TelegramLogContext {
  updateId?: number;
  chatId?: number | string;
  userId?: string;
  telegramUserId?: string;
  username?: string;
  messageId?: number;
  command?: string;
  action?: string;
  processingTime?: number;
  error?: Error | string;
  metadata?: Record<string, any>;
}

export class TelegramLogger {
  private static instance: TelegramLogger;
  private logLevel: LogLevel;
  private enableConsoleColors: boolean;

  private constructor() {
    this.logLevel = this.getLogLevelFromEnv();
    this.enableConsoleColors = process.env.NODE_ENV !== 'production';
  }

  public static getInstance(): TelegramLogger {
    if (!TelegramLogger.instance) {
      TelegramLogger.instance = new TelegramLogger();
    }
    return TelegramLogger.instance;
  }

  private getLogLevelFromEnv(): LogLevel {
    const level = process.env.TELEGRAM_LOG_LEVEL?.toUpperCase();
    switch (level) {
      case 'DEBUG': return LogLevel.DEBUG;
      case 'INFO': return LogLevel.INFO;
      case 'WARN': return LogLevel.WARN;
      case 'ERROR': return LogLevel.ERROR;
      default: return LogLevel.INFO;
    }
  }

  private formatMessage(level: LogLevel, message: string, context?: TelegramLogContext): string {
    const timestamp = new Date().toISOString();
    const levelStr = LogLevel[level];
    
    let logMessage = `[${timestamp}] [TELEGRAM-${levelStr}] ${message}`;
    
    if (context) {
      const contextStr = this.formatContext(context);
      if (contextStr) {
        logMessage += ` | ${contextStr}`;
      }
    }
    
    return logMessage;
  }

  private formatContext(context: TelegramLogContext): string {
    const parts: string[] = [];
    
    if (context.updateId) parts.push(`updateId=${context.updateId}`);
    if (context.chatId) parts.push(`chatId=${context.chatId}`);
    if (context.userId) parts.push(`userId=${context.userId}`);
    if (context.telegramUserId) parts.push(`telegramUserId=${context.telegramUserId}`);
    if (context.username) parts.push(`username=${context.username}`);
    if (context.messageId) parts.push(`messageId=${context.messageId}`);
    if (context.command) parts.push(`command=${context.command}`);
    if (context.action) parts.push(`action=${context.action}`);
    if (context.processingTime) parts.push(`processingTime=${context.processingTime}ms`);
    
    if (context.metadata) {
      Object.entries(context.metadata).forEach(([key, value]) => {
        parts.push(`${key}=${JSON.stringify(value)}`);
      });
    }
    
    return parts.join(', ');
  }

  private getColorCode(level: LogLevel): string {
    if (!this.enableConsoleColors) return '';
    
    switch (level) {
      case LogLevel.DEBUG: return '\x1b[36m'; // Cyan
      case LogLevel.INFO: return '\x1b[32m';  // Green
      case LogLevel.WARN: return '\x1b[33m';  // Yellow
      case LogLevel.ERROR: return '\x1b[31m'; // Red
      default: return '';
    }
  }

  private getResetCode(): string {
    return this.enableConsoleColors ? '\x1b[0m' : '';
  }

  private log(level: LogLevel, message: string, context?: TelegramLogContext): void {
    if (level < this.logLevel) return;
    
    const formattedMessage = this.formatMessage(level, message, context);
    const colorCode = this.getColorCode(level);
    const resetCode = this.getResetCode();
    
    const output = `${colorCode}${formattedMessage}${resetCode}`;
    
    switch (level) {
      case LogLevel.ERROR:
        console.error(output);
        if (context?.error instanceof Error) {
          console.error(`${colorCode}Stack trace:${resetCode}`, context.error.stack);
        }
        break;
      case LogLevel.WARN:
        console.warn(output);
        break;
      default:
        console.log(output);
    }
  }

  // Public logging methods
  public debug(message: string, context?: TelegramLogContext): void {
    this.log(LogLevel.DEBUG, message, context);
  }

  public info(message: string, context?: TelegramLogContext): void {
    this.log(LogLevel.INFO, message, context);
  }

  public warn(message: string, context?: TelegramLogContext): void {
    this.log(LogLevel.WARN, message, context);
  }

  public error(message: string, context?: TelegramLogContext): void {
    this.log(LogLevel.ERROR, message, context);
  }

  // Specialized logging methods for common Telegram operations
  public logWebhookReceived(updateId: number, updateType: string, chatId?: number): void {
    this.info('Webhook update received', {
      updateId,
      chatId,
      action: 'webhook_received',
      metadata: { updateType }
    });
  }

  public logCommandReceived(command: string, userId: string, chatId: number, username?: string): void {
    this.info('Command received', {
      command,
      userId,
      chatId,
      username,
      action: 'command_received'
    });
  }

  public logMessageProcessed(chatId: number, userId: string, processingTime: number, success: boolean): void {
    const level = success ? LogLevel.INFO : LogLevel.ERROR;
    const message = success ? 'Message processed successfully' : 'Message processing failed';
    
    this.log(level, message, {
      chatId,
      userId,
      processingTime,
      action: 'message_processed',
      metadata: { success }
    });
  }

  public logAIResponse(userId: string, chatId: number, model: string, tokensUsed?: number): void {
    this.info('AI response generated', {
      userId,
      chatId,
      action: 'ai_response_generated',
      metadata: { model, tokensUsed }
    });
  }

  public logSecurityEvent(event: string, ip?: string, chatId?: number, details?: any): void {
    this.warn('Security event detected', {
      chatId,
      action: 'security_event',
      metadata: { event, ip, ...details }
    });
  }

  public logRateLimitHit(userId: string, chatId: number, feature: string, limit: number): void {
    this.warn('Rate limit exceeded', {
      userId,
      chatId,
      action: 'rate_limit_exceeded',
      metadata: { feature, limit }
    });
  }

  public logDatabaseOperation(operation: string, table: string, success: boolean, processingTime?: number): void {
    const level = success ? LogLevel.DEBUG : LogLevel.ERROR;
    const message = `Database ${operation} ${success ? 'succeeded' : 'failed'}`;
    
    this.log(level, message, {
      action: 'database_operation',
      processingTime,
      metadata: { operation, table, success }
    });
  }

  public logTwitterIntegration(userId: string, chatId: number, tweetUrl: string, success: boolean): void {
    const level = success ? LogLevel.INFO : LogLevel.ERROR;
    const message = `Twitter integration ${success ? 'succeeded' : 'failed'}`;
    
    this.log(level, message, {
      userId,
      chatId,
      action: 'twitter_integration',
      metadata: { tweetUrl, success }
    });
  }

  public logAccountLinking(telegramUserId: string, buddyChipUserId: string, success: boolean): void {
    const level = success ? LogLevel.INFO : LogLevel.ERROR;
    const message = `Account linking ${success ? 'succeeded' : 'failed'}`;
    
    this.log(level, message, {
      telegramUserId,
      userId: buddyChipUserId,
      action: 'account_linking',
      metadata: { success }
    });
  }

  // Performance monitoring
  public startTimer(): () => number {
    const startTime = Date.now();
    return () => Date.now() - startTime;
  }

  // Health check logging
  public logHealthCheck(status: 'healthy' | 'unhealthy', details?: any): void {
    const level = status === 'healthy' ? LogLevel.INFO : LogLevel.ERROR;
    this.log(level, `Health check: ${status}`, {
      action: 'health_check',
      metadata: { status, ...details }
    });
  }
}

// Export singleton instance
export const telegramLogger = TelegramLogger.getInstance();

// Convenience functions for backward compatibility
export const logTelegramActivity = (action: string, userId?: string, chatId?: string, details?: any) => {
  telegramLogger.info(action, {
    userId,
    chatId: chatId ? parseInt(chatId) : undefined,
    action,
    metadata: details
  });
};

export const handleTelegramError = (error: any, context: string): string => {
  telegramLogger.error(`Error in ${context}`, { error, action: context });
  
  // Return user-friendly error messages
  if (error.code === "ETELEGRAM") {
    switch (error.response?.body?.error_code) {
      case 400:
        return "Invalid request. Please try again.";
      case 401:
        return "Bot authentication failed. Please contact support.";
      case 403:
        return "Bot was blocked by user or lacks permissions.";
      case 429:
        return "Too many requests. Please wait a moment and try again.";
      default:
        return "Telegram service error. Please try again later.";
    }
  }

  return "An unexpected error occurred. Please try again.";
};
