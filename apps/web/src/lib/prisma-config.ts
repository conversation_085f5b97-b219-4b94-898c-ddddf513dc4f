/**
 * Prisma Configuration Factory
 * 
 * Centralized configuration for all Prisma client instances.
 * Ensures consistent logging, middleware, and connection settings
 * across web app, scripts, and tests.
 */

import { PrismaClient, type Prisma } from "../../prisma/generated";

export interface PrismaConfigOptions {
  /** Custom database URL (defaults to process.env.DATABASE_URL) */
  databaseUrl?: string;
  /** Enable query logging regardless of environment */
  forceQueryLogs?: boolean;
  /** Disable performance middleware (useful for tests) */
  disableMiddleware?: boolean;
  /** Custom log levels */
  logLevels?: Prisma.LogLevel[];
  /** Instance identifier for logging */
  instanceId?: string;
}

/**
 * Create standardized Prisma client configuration
 */
export function createPrismaConfig(options: PrismaConfigOptions = {}): Prisma.PrismaClientOptions {
  const {
    databaseUrl = process.env.DATABASE_URL,
    forceQueryLogs = false,
    logLevels,
    instanceId = "default"
  } = options;

  // Determine log levels based on environment and options
  const getLogLevels = (): Prisma.LogLevel[] => {
    if (logLevels) return logLevels;
    
    if (forceQueryLogs || process.env.ENABLE_PRISMA_QUERY_LOGS === "true") {
      return ["query", "error", "warn", "info"];
    }
    
    if (process.env.NODE_ENV === "development") {
      return ["warn", "error"];
    }
    
    return ["error"];
  };

  // Safe logging that doesn't expose credentials
  if (process.env.NODE_ENV === "development" || process.env.VERBOSE_LOGGING === "true") {
    console.log(`🔍 Prisma Config [${instanceId}]: Initializing client`);
    console.log(`🔍 Prisma Config [${instanceId}]: DATABASE_URL exists:`, !!databaseUrl);
    console.log(`🔍 Prisma Config [${instanceId}]: Log levels:`, getLogLevels());
  }

  return {
    log: getLogLevels(),
    errorFormat: "pretty",
    datasources: {
      db: {
        url: databaseUrl,
      },
    },
  };
}

/**
 * Create a Prisma client with standard configuration and middleware
 */
export function createPrismaClient(options: PrismaConfigOptions = {}): PrismaClient {
  const { disableMiddleware = false, instanceId = "default" } = options;
  const config = createPrismaConfig(options);
  
  const client = new PrismaClient(config);

  if (!disableMiddleware) {
    // Add performance monitoring middleware
    client.$use(async (params, next) => {
      const startTime = Date.now();
      const result = await next(params);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (process.env.ENABLE_PRISMA_QUERY_LOGS === "true" || options.forceQueryLogs) {
        console.log(`⏱️ Prisma [${instanceId}]: ${params.model}.${params.action} took ${duration}ms`);
      }
      
      // Warn about slow queries (>1000ms)
      if (duration > 1000) {
        console.warn(`🐌 Slow Query [${instanceId}]: ${params.model}.${params.action} took ${duration}ms`);
      }
      
      return result;
    });

    // Add soft delete middleware for applicable models
    client.$use(async (params, next) => {
      const softDeleteModels = ['User', 'MonitoredAccount', 'AIResponse'];
      
      if (!params.model || !softDeleteModels.includes(params.model)) {
        return next(params);
      }

      // Handle soft deletes
      if (params.action === 'delete') {
        params.action = 'update';
        params.args['data'] = { isDeleted: true };
      }
      
      if (params.action === 'deleteMany') {
        params.action = 'updateMany';
        if (params.args.data != undefined) {
          params.args.data['isDeleted'] = true;
        } else {
          params.args['data'] = { isDeleted: true };
        }
      }

      // Filter out soft deleted records for read operations
      if (params.action === 'findUnique' || params.action === 'findFirst') {
        params.args.where = { ...params.args.where, isDeleted: false };
      }
      
      if (params.action === 'findMany') {
        if (params.args.where) {
          if (params.args.where.isDeleted === undefined) {
            params.args.where['isDeleted'] = false;
          }
        } else {
          params.args['where'] = { isDeleted: false };
        }
      }

      return next(params);
    });

    // Add connection health monitoring
    client.$use(async (params, next) => {
      try {
        return await next(params);
      } catch (error) {
        // Log connection errors with context
        if (error instanceof Error) {
          if (error.message.includes('connection') || error.message.includes('timeout')) {
            console.error(`🔴 Connection Error [${instanceId}]:`, {
              model: params.model,
              action: params.action,
              error: error.message,
              timestamp: new Date().toISOString()
            });
          }
        }
        throw error;
      }
    });
  }

  return client;
}

/**
 * Connection health check utility
 */
export async function checkPrismaConnection(
  client: PrismaClient, 
  instanceId: string = "default"
): Promise<boolean> {
  try {
    await client.$queryRaw`SELECT 1 as health_check`;
    console.log(`✅ Prisma Health Check [${instanceId}]: Connection healthy`);
    return true;
  } catch (error) {
    console.error(`❌ Prisma Health Check [${instanceId}]: Connection failed`, error);
    return false;
  }
}

/**
 * Graceful disconnect with error handling
 */
export async function disconnectPrisma(
  client: PrismaClient, 
  instanceId: string = "default"
): Promise<void> {
  try {
    await client.$disconnect();
    console.log(`🔌 Prisma [${instanceId}]: Disconnected successfully`);
  } catch (error) {
    console.error(`❌ Prisma [${instanceId}]: Disconnect failed`, error);
  }
}
