/**
 * OpenAI Image Generation Tool
 *
 * Generates images using OpenAI's DALL-E models and stores them via UploadThing
 */

import { tool } from "ai";
import { z } from "zod";

export const imageGenerationTool = tool({
  description: "Generate images using OpenAI DALL-E models",
  parameters: z.object({
    prompt: z.string().describe("Detailed image generation prompt"),
    size: z
      .enum(["1024x1024", "1792x1024", "1024x1792"])
      .optional()
      .default("1024x1024"),
    quality: z.enum(["standard", "hd"]).optional().default("standard"),
    style: z.enum(["vivid", "natural"]).optional().default("vivid"),
  }),
  execute: async ({ prompt, size, quality, style }) => {
    try {
      if (!process.env.OPENAI_API_KEY) {
        throw new Error("OPENAI_API_KEY not configured");
      }

      // Generate image with OpenAI DALL-E
      const response = await fetch(
        "https://api.openai.com/v1/images/generations",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model: "dall-e-3",
            prompt,
            size,
            quality,
            style,
            n: 1, // Generate one image
          }),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        console.error("OpenAI Image API error:", response.status, errorText);

        if (response.status === 401) {
          throw new Error("OpenAI API key is invalid or expired");
        } else if (response.status === 429) {
          throw new Error("OpenAI rate limit exceeded");
        } else if (response.status === 400) {
          throw new Error("Invalid image generation prompt or parameters");
        } else {
          throw new Error(
            `Image generation failed with status ${response.status}`
          );
        }
      }

      const data = await response.json();
      const imageUrl = data.data[0]?.url;
      const revisedPrompt = data.data[0]?.revised_prompt;

      if (!imageUrl) {
        throw new Error("No image URL returned from OpenAI");
      }

      // TODO: Upload to UploadThing in future implementation
      // For now, return the OpenAI URL directly
      const uploadedImageUrl = imageUrl; // This would be replaced with UploadThing URL

      return {
        imageUrl: uploadedImageUrl,
        originalPrompt: prompt,
        revisedPrompt,
        size,
        quality,
        style,
        timestamp: new Date().toISOString(),
        source: "OpenAI DALL-E 3",
        // TODO: Add UploadThing metadata
        uploadThing: {
          url: uploadedImageUrl,
          key: null, // Would be set after UploadThing upload
        },
      };
    } catch (error) {
      console.error("Image generation tool error:", error);

      // Return graceful error response
      return {
        originalPrompt: prompt,
        error:
          error instanceof Error ? error.message : "Failed to generate image",
        timestamp: new Date().toISOString(),
        source: "OpenAI DALL-E 3",
      };
    }
  },
});
