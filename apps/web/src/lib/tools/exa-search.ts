/**
 * Exa Search Tool
 *
 * Provides semantic web search and content extraction using Exa AI
 */

import { tool } from "ai";
import { z } from "zod";

export const exaSearchTool = tool({
  description:
    "Search and extract content using Exa for semantic web search and knowledge retrieval",
  parameters: z.object({
    query: z.string().describe("Semantic search query"),
    numResults: z
      .number()
      .optional()
      .default(5)
      .describe("Number of results to return"),
    includeContent: z
      .boolean()
      .optional()
      .default(true)
      .describe("Whether to include page content"),
    category: z
      .enum(["news", "research", "company", "general"])
      .optional()
      .default("general"),
    dateCutoff: z
      .string()
      .optional()
      .describe("ISO date string for filtering recent content"),
  }),
  execute: async ({
    query,
    numResults,
    includeContent,
    category,
    dateCutoff,
  }) => {
    try {
      if (
        !process.env.EXA_API_KEY ||
        process.env.EXA_API_KEY.includes("your_exa_api_key")
      ) {
        return {
          query,
          results: [],
          error: "EXA_API_KEY not configured - please add a valid API key",
          timestamp: new Date().toISOString(),
          source: "Exa Search",
        };
      }

      const searchParams: any = {
        query,
        numResults,
        includeContent,
        type: "neural", // Use neural search for better semantic understanding
      };

      // Add date filter if specified
      if (dateCutoff) {
        searchParams.startPublishedDate = dateCutoff;
      }

      // Adjust search based on category
      if (category === "news") {
        searchParams.includeDomains = [
          "cnn.com",
          "bbc.com",
          "reuters.com",
          "techcrunch.com",
          "theverge.com",
        ];
      } else if (category === "research") {
        searchParams.includeDomains = [
          "arxiv.org",
          "scholar.google.com",
          "researchgate.net",
          "pubmed.ncbi.nlm.nih.gov",
        ];
      } else if (category === "company") {
        searchParams.excludeDomains = [
          "reddit.com",
          "twitter.com",
          "facebook.com",
        ];
      }

      const response = await fetch("https://api.exa.ai/search", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.EXA_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(searchParams),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Exa Search API error:", response.status, errorText);

        if (response.status === 401) {
          throw new Error("Exa API key is invalid or expired");
        } else if (response.status === 429) {
          throw new Error("Exa Search rate limit exceeded");
        } else {
          throw new Error(`Exa Search failed with status ${response.status}`);
        }
      }

      const data = await response.json();

      // Transform results to standardized format
      const results =
        data.results?.map((result: any) => ({
          title: result.title || "",
          url: result.url || "",
          content: result.text?.substring(0, 1000) || "", // Limit content length
          snippet: result.text?.substring(0, 200) || "",
          publishedDate: result.publishedDate,
          score: result.score,
          id: result.id,
        })) || [];

      return {
        query,
        results,
        autopromptString: data.autopromptString,
        category,
        timestamp: new Date().toISOString(),
        source: "Exa Search",
        totalResults: results.length,
      };
    } catch (error) {
      console.error("Exa Search tool error:", error);

      // Return graceful error response
      return {
        query,
        results: [],
        error:
          error instanceof Error ? error.message : "Failed to search with Exa",
        timestamp: new Date().toISOString(),
        source: "Exa Search",
      };
    }
  },
});
