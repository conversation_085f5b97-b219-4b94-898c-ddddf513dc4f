/**
 * Mem0 Memory Tool for Benji AI Agent
 *
 * Provides memory search and storage capabilities for personalized AI conversations
 * using self-hosted mem0 with Supabase vector database
 */

import { tool } from "ai";
import { z } from "zod";
import mem0Service from "../mem0-service";

export const mem0MemoryTool = tool({
  description:
    "Search and manage conversation memories for personalized AI responses. Use this to recall previous interactions, user preferences, and conversation context.",
  parameters: z.object({
    action: z
      .enum(["search", "add", "context"])
      .describe("Memory operation to perform"),

    // For search operations
    query: z
      .string()
      .optional()
      .describe("Search query to find relevant memories"),
    limit: z
      .number()
      .optional()
      .default(3)
      .describe("Maximum number of memories to retrieve"),
    memoryType: z
      .string()
      .optional()
      .describe(
        "Type of memory to search for (conversation, preference, fact, etc.)"
      ),

    // For add operations
    content: z.string().optional().describe("Content to store as memory"),
    metadata: z
      .record(z.any())
      .optional()
      .describe("Additional metadata for the memory"),

    // Common parameters
    userId: z.string().describe("User ID for memory isolation"),
    sessionId: z
      .string()
      .optional()
      .describe("Session ID for conversation tracking"),
    mentionId: z
      .string()
      .optional()
      .describe("Mention ID if this relates to a specific tweet"),
  }),
  execute: async ({
    action,
    query,
    limit,
    memoryType,
    content,
    metadata,
    userId,
    sessionId,
    mentionId,
  }) => {
    try {
      console.log(`🧠 Mem0MemoryTool: Executing ${action} for user ${userId}`, {
        query,
        limit,
        memoryType,
        sessionId,
        mentionId,
      });

      // Validate required parameters
      if (!userId) {
        return {
          action,
          success: false,
          error: "User ID is required for memory operations",
          timestamp: new Date().toISOString(),
          source: "Mem0 Memory",
        };
      }

      switch (action) {
        case "search": {
          if (!query) {
            return {
              action,
              success: false,
              error: "Query is required for memory search",
              timestamp: new Date().toISOString(),
              source: "Mem0 Memory",
            };
          }

          const memories = await mem0Service.searchMemories(userId, {
            query,
            limit: Math.min(limit || 3, 5), // Cap at 5 memories
            memoryType,
            metadata: metadata ? { ...metadata } : undefined,
          });

          return {
            action: "search",
            success: true,
            query,
            memories: memories.map((memory) => ({
              id: memory.id,
              content: memory.content,
              similarity: memory.similarity,
              memoryType: memory.memoryType,
              createdAt: memory.createdAt,
            })),
            count: memories.length,
            timestamp: new Date().toISOString(),
            source: "Mem0 Memory",
          };
        }

        case "add": {
          if (!content) {
            return {
              action,
              success: false,
              error: "Content is required for adding memories",
              timestamp: new Date().toISOString(),
              source: "Mem0 Memory",
            };
          }

          // Format content as a conversation message
          const messages = [
            {
              role: "user",
              content: content,
            },
          ];

          const context = {
            userId,
            sessionId,
            mentionId,
          };

          await mem0Service.addMemories(userId, messages, context, {
            memoryType: memoryType || "conversation",
            metadata: metadata || {},
          });

          return {
            action: "add",
            success: true,
            message: "Memory added successfully",
            memoryType: memoryType || "conversation",
            timestamp: new Date().toISOString(),
            source: "Mem0 Memory",
          };
        }

        case "context": {
          if (!query) {
            return {
              action,
              success: false,
              error: "Query is required for context generation",
              timestamp: new Date().toISOString(),
              source: "Mem0 Memory",
            };
          }

          const memoryContext = await mem0Service.getUserMemoryContext(
            userId,
            query,
            limit || 3
          );

          return {
            action: "context",
            success: true,
            query,
            context: memoryContext,
            hasMemories: memoryContext.length > 0,
            timestamp: new Date().toISOString(),
            source: "Mem0 Memory",
          };
        }

        default:
          return {
            action,
            success: false,
            error: `Unknown action: ${action}`,
            timestamp: new Date().toISOString(),
            source: "Mem0 Memory",
          };
      }
    } catch (error) {
      console.error("❌ Mem0MemoryTool error:", error);

      // Handle rate limiting errors specifically
      if (error instanceof Error && error.message.includes("rate limit")) {
        return {
          action,
          success: false,
          error:
            "Memory operation rate limited. Please upgrade your plan or wait for the next billing period.",
          rateLimited: true,
          timestamp: new Date().toISOString(),
          source: "Mem0 Memory",
        };
      }

      // Return graceful error response for other errors
      return {
        action,
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to perform memory operation",
        timestamp: new Date().toISOString(),
        source: "Mem0 Memory",
      };
    }
  },
});

/**
 * Helper function to format memories for AI consumption
 */
export function formatMemoriesForPrompt(memories: any[]): string {
  if (!memories || memories.length === 0) {
    return "";
  }

  const formattedMemories = memories
    .map((memory, index) => {
      const similarity = memory.similarity
        ? ` (${Math.round(memory.similarity * 100)}% relevant)`
        : "";
      const type = memory.memoryType ? ` [${memory.memoryType}]` : "";
      return `${index + 1}. ${memory.content}${type}${similarity}`;
    })
    .join("\n");

  return `\n\nRelevant memories from previous conversations:\n${formattedMemories}\n`;
}

/**
 * Helper function to create memory context for system prompts
 */
export function createMemorySystemPrompt(memories: any[]): string {
  if (!memories || memories.length === 0) {
    return "";
  }

  const memoryCount = memories.length;
  const memoryTypes = [
    ...new Set(memories.map((m) => m.memoryType).filter(Boolean)),
  ];

  let prompt = `\n\nYou have access to ${memoryCount} relevant memories from previous conversations with this user.`;

  if (memoryTypes.length > 0) {
    prompt += ` These include: ${memoryTypes.join(", ")}.`;
  }

  prompt +=
    " Use these memories to provide more personalized and contextually aware responses. Reference past conversations naturally when relevant, but don't force connections if they don't make sense.";

  return prompt;
}
