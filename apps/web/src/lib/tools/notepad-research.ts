/**
 * Notepad Research Tools
 *
 * Enhanced research tools that integrate with the notepad system
 * to automatically save sources and track research sessions
 */

import { tool } from "ai";
import { z } from "zod";
import { exaSearchTool } from "./exa-search";
import { xaiSearchTool } from "./xai-search";

export interface NotepadContext {
  notepadId?: string;
  userId?: string;
  prisma?: any; // Prisma client instance
}

/**
 * Enhanced XAI search that saves sources to notepad
 */
export const notepadXaiSearchTool = tool({
  description:
    "Search the web in real-time using xAI Live Search and automatically save sources to notepad",
  parameters: z.object({
    query: z.string().describe("Search query for current information"),
    maxResults: z
      .number()
      .optional()
      .default(5)
      .describe("Maximum number of results to return"),
    safeSearch: z
      .enum(["strict", "moderate", "off"])
      .optional()
      .default("moderate"),
    notepadId: z.string().optional().describe("Notepad ID to save sources to"),
  }),
  execute: async (
    { query, maxResults, safeSearch, notepadId },
    context?: NotepadContext
  ) => {
    try {
      console.log(
        "🔍 Notepad XAI Search: Starting search with notepad integration"
      );

      // Execute the original XAI search
      // @ts-ignore - We need to call the execute function directly
      const searchResult = await xaiSearchTool.execute({
        query,
        maxResults,
        safeSearch,
      });

      // If we have notepad context and the search was successful, save sources
      if (
        notepadId &&
        context?.prisma &&
        context?.userId &&
        !searchResult.error
      ) {
        console.log(
          "💾 Notepad XAI Search: Saving sources to notepad:",
          notepadId
        );

        try {
          // Verify the notepad belongs to the user
          const notepad = await context.prisma.mentionNotepad.findFirst({
            where: {
              id: notepadId,
              userId: context.userId,
            },
          });

          if (notepad) {
            // Extract sources from the search result and save them
            const sources = extractSourcesFromXaiResult(searchResult);

            for (const source of sources) {
              try {
                await context.prisma.notepadSource.create({
                  data: {
                    notepadId: notepadId,
                    title: source.title,
                    url: source.url,
                    content: source.content,
                    sourceType: "web",
                    searchTool: "xai",
                    relevanceScore: source.relevanceScore,
                    credibilityScore: source.credibilityScore,
                    publishedAt: source.publishedAt,
                  },
                });
                console.log(
                  "✅ Notepad XAI Search: Saved source:",
                  source.title
                );
              } catch (sourceError) {
                console.error(
                  "❌ Notepad XAI Search: Error saving source:",
                  sourceError
                );
                // Continue with other sources even if one fails
              }
            }

            // Update notepad research context
            await context.prisma.mentionNotepad.update({
              where: { id: notepadId },
              data: {
                researchQuery: query,
                researchContext: {
                  lastXaiSearch: {
                    query,
                    timestamp: new Date().toISOString(),
                    resultsCount: sources.length,
                  },
                },
                lastUsedAt: new Date(),
              },
            });
          }
        } catch (notepadError) {
          console.error(
            "❌ Notepad XAI Search: Error saving to notepad:",
            notepadError
          );
          // Don't fail the search if notepad saving fails
        }
      }

      return {
        ...searchResult,
        notepadIntegration: {
          notepadId,
          sourcesSaved: notepadId ? true : false,
        },
      };
    } catch (error) {
      console.error("❌ Notepad XAI Search: Error:", error);
      return {
        query,
        results: [],
        error: "Search failed",
        timestamp: new Date().toISOString(),
        source: "Notepad XAI Search",
        notepadIntegration: {
          notepadId,
          sourcesSaved: false,
          error: error instanceof Error ? error.message : "Unknown error",
        },
      };
    }
  },
});

/**
 * Enhanced Exa search that saves sources to notepad
 */
export const notepadExaSearchTool = tool({
  description:
    "Search and extract content using Exa for semantic web search and automatically save sources to notepad",
  parameters: z.object({
    query: z.string().describe("Semantic search query"),
    numResults: z
      .number()
      .optional()
      .default(5)
      .describe("Number of results to return"),
    includeContent: z
      .boolean()
      .optional()
      .default(true)
      .describe("Whether to include page content"),
    category: z
      .enum(["news", "research", "company", "general"])
      .optional()
      .default("general"),
    dateCutoff: z
      .string()
      .optional()
      .describe("ISO date string for filtering recent content"),
    notepadId: z.string().optional().describe("Notepad ID to save sources to"),
  }),
  execute: async (
    { query, numResults, includeContent, category, dateCutoff, notepadId },
    context?: NotepadContext
  ) => {
    try {
      console.log(
        "🔍 Notepad Exa Search: Starting search with notepad integration"
      );

      // Execute the original Exa search
      // @ts-ignore - We need to call the execute function directly
      const searchResult = await exaSearchTool.execute({
        query,
        numResults,
        includeContent,
        category,
        dateCutoff,
      });

      // If we have notepad context and the search was successful, save sources
      if (
        notepadId &&
        context?.prisma &&
        context?.userId &&
        !searchResult.error
      ) {
        console.log(
          "💾 Notepad Exa Search: Saving sources to notepad:",
          notepadId
        );

        try {
          // Verify the notepad belongs to the user
          const notepad = await context.prisma.mentionNotepad.findFirst({
            where: {
              id: notepadId,
              userId: context.userId,
            },
          });

          if (notepad) {
            // Extract sources from the search result and save them
            const sources = extractSourcesFromExaResult(searchResult);

            for (const source of sources) {
              try {
                await context.prisma.notepadSource.create({
                  data: {
                    notepadId: notepadId,
                    title: source.title,
                    url: source.url,
                    content: source.content,
                    sourceType: mapExaCategoryToSourceType(category),
                    searchTool: "exa",
                    relevanceScore: source.relevanceScore,
                    credibilityScore: source.credibilityScore,
                    publishedAt: source.publishedAt,
                  },
                });
                console.log(
                  "✅ Notepad Exa Search: Saved source:",
                  source.title
                );
              } catch (sourceError) {
                console.error(
                  "❌ Notepad Exa Search: Error saving source:",
                  sourceError
                );
                // Continue with other sources even if one fails
              }
            }

            // Update notepad research context
            await context.prisma.mentionNotepad.update({
              where: { id: notepadId },
              data: {
                researchQuery: query,
                researchContext: {
                  lastExaSearch: {
                    query,
                    category,
                    timestamp: new Date().toISOString(),
                    resultsCount: sources.length,
                  },
                },
                lastUsedAt: new Date(),
              },
            });
          }
        } catch (notepadError) {
          console.error(
            "❌ Notepad Exa Search: Error saving to notepad:",
            notepadError
          );
          // Don't fail the search if notepad saving fails
        }
      }

      return {
        ...searchResult,
        notepadIntegration: {
          notepadId,
          sourcesSaved: notepadId ? true : false,
        },
      };
    } catch (error) {
      console.error("❌ Notepad Exa Search: Error:", error);
      return {
        query,
        results: [],
        error: "Search failed",
        timestamp: new Date().toISOString(),
        source: "Notepad Exa Search",
        notepadIntegration: {
          notepadId,
          sourcesSaved: false,
          error: error instanceof Error ? error.message : "Unknown error",
        },
      };
    }
  },
});

/**
 * Extract sources from XAI search result
 */
function extractSourcesFromXaiResult(result: any): Array<{
  title: string;
  url: string;
  content?: string;
  relevanceScore?: number;
  credibilityScore?: number;
  publishedAt?: Date;
}> {
  const sources: Array<{
    title: string;
    url: string;
    content?: string;
    relevanceScore?: number;
    credibilityScore?: number;
    publishedAt?: Date;
  }> = [];

  try {
    // XAI results might be in different formats, try to extract what we can
    if (result.results && Array.isArray(result.results)) {
      for (const item of result.results) {
        if (item.url && item.title) {
          sources.push({
            title: item.title,
            url: item.url,
            content: item.content || item.snippet || item.description,
            relevanceScore: item.relevance || 0.5,
            credibilityScore: item.credibility || 0.5,
            publishedAt: item.publishedAt
              ? new Date(item.publishedAt)
              : undefined,
          });
        }
      }
    }
  } catch (error) {
    console.error("❌ Error extracting sources from XAI result:", error);
  }

  return sources;
}

/**
 * Extract sources from Exa search result
 */
function extractSourcesFromExaResult(result: any): Array<{
  title: string;
  url: string;
  content?: string;
  relevanceScore?: number;
  credibilityScore?: number;
  publishedAt?: Date;
}> {
  const sources: Array<{
    title: string;
    url: string;
    content?: string;
    relevanceScore?: number;
    credibilityScore?: number;
    publishedAt?: Date;
  }> = [];

  try {
    // Exa results format
    if (result.results && Array.isArray(result.results)) {
      for (const item of result.results) {
        if (item.url && item.title) {
          sources.push({
            title: item.title,
            url: item.url,
            content: item.content || item.text,
            relevanceScore: item.score || 0.5,
            credibilityScore: 0.7, // Default credibility for Exa results
            publishedAt: item.publishedDate
              ? new Date(item.publishedDate)
              : undefined,
          });
        }
      }
    }
  } catch (error) {
    console.error("❌ Error extracting sources from Exa result:", error);
  }

  return sources;
}

/**
 * Map Exa category to source type
 */
function mapExaCategoryToSourceType(
  category?: string
): "web" | "news" | "research" | "social" {
  switch (category) {
    case "news":
      return "news";
    case "research":
      return "research";
    case "company":
    case "general":
    default:
      return "web";
  }
}
