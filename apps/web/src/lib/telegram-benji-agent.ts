/**
 * Telegram <PERSON>ji Agent
 *
 * Specialized wrapper around BenjiAgent for Telegram bot interactions
 */

import { BenjiAgent, type BenjiConfig, type BenjiContext } from "./benji-agent";
import {
  createTelegramSession,
  getTelegramSession,
  updateTelegramSession,
} from "./telegram-auth";

export interface TelegramBenjiConfig extends BenjiConfig {
  telegramUserId?: string;
  telegramChatId?: string;
  maxMessageLength?: number;
  enableMarkdown?: boolean;
}

export interface TelegramBenjiContext extends BenjiContext {
  telegramUserId?: string;
  telegramChatId?: string;
  sessionId?: string;
  messageHistory?: Array<{
    role: "user" | "assistant";
    content: string;
    timestamp: Date;
  }>;
  customSystemPrompt?: string;
  twitterUrl?: string;
}

export class TelegramBenjiAgent extends BenjiAgent {
  private telegramConfig: TelegramBenjiConfig;

  constructor(config: TelegramBenjiConfig = {}) {
    super(config);
    this.telegramConfig = {
      maxMessageLength: 4000, // Telegram's message limit
      enableMarkdown: true,
      ...config,
    };
  }

  /**
   * Generate response optimized for Telegram
   */
  async generateTelegramResponse(
    message: string,
    context: TelegramBenjiContext = {}
  ) {
    console.log(
      "📱 Telegram Benji: Generating response for message:",
      message.substring(0, 100) + "..."
    );

    // Load conversation context from session
    const enhancedContext = await this.loadConversationContext(context);

    // Add Telegram-specific instructions
    const telegramContext = this.addTelegramInstructions(enhancedContext);

    // Generate response using parent class mention response method
    const result = await this.generateMentionResponse(message, telegramContext);

    // Save conversation context
    await this.saveConversationContext(enhancedContext, message, result);

    return result;
  }

  /**
   * Generate quick reply for Twitter URL (Telegram optimized)
   */
  async generateTelegramQuickReply(
    tweetContent: string,
    twitterUrl: string,
    context: TelegramBenjiContext = {}
  ) {
    console.log("🐦 Telegram Benji: Generating quick reply for tweet");

    // Add Telegram-specific context (extend with Telegram properties first)
    const telegramContext = {
      ...context,
      twitterUrl,
    } as TelegramBenjiContext;

    // Use parent class quick reply method
    const result = await this.generateQuickReply(tweetContent, telegramContext);

    return result;
  }

  /**
   * Generate enhanced response using o3 model (Telegram optimized)
   */
  async generateTelegramEnhancedResponse(
    content: string,
    context: TelegramBenjiContext = {}
  ) {
    console.log(
      "✨ Telegram Benji: Generating enhanced response with o3 model"
    );

    // Add Telegram-specific context
    const telegramContext: BenjiContext = {
      ...context,
    };

    // Use parent class enhanced response method
    const result = await this.generateEnhancedMentionResponse(
      content,
      telegramContext
    );

    return result;
  }

  /**
   * Format response for Telegram constraints
   */
  formatForTelegram(text: string): string[] {
    const maxLength = this.telegramConfig.maxMessageLength || 4000;

    if (text.length <= maxLength) {
      return [text];
    }

    return this.splitLongMessage(text, maxLength);
  }

  /**
   * Split long messages for Telegram
   */
  private splitLongMessage(text: string, maxLength: number): string[] {
    const messages: string[] = [];
    let currentMessage = "";

    // Split by paragraphs first
    const paragraphs = text.split("\n\n");

    for (const paragraph of paragraphs) {
      if ((currentMessage + paragraph + "\n\n").length > maxLength) {
        if (currentMessage.trim()) {
          messages.push(currentMessage.trim());
          currentMessage = "";
        }

        // If single paragraph is too long, split by sentences
        if (paragraph.length > maxLength) {
          const sentences = paragraph.split(". ");
          for (const sentence of sentences) {
            if ((currentMessage + sentence + ". ").length > maxLength) {
              if (currentMessage.trim()) {
                messages.push(currentMessage.trim());
                currentMessage = "";
              }

              // If single sentence is still too long, split by words
              if (sentence.length > maxLength) {
                const words = sentence.split(" ");
                for (const word of words) {
                  if ((currentMessage + word + " ").length > maxLength) {
                    if (currentMessage.trim()) {
                      messages.push(currentMessage.trim());
                      currentMessage = "";
                    }
                  }
                  currentMessage += word + " ";
                }
              } else {
                currentMessage = sentence + ". ";
              }
            } else {
              currentMessage += sentence + ". ";
            }
          }
        } else {
          currentMessage = paragraph + "\n\n";
        }
      } else {
        currentMessage += paragraph + "\n\n";
      }
    }

    if (currentMessage.trim()) {
      messages.push(currentMessage.trim());
    }

    return messages.length > 0 ? messages : [text.substring(0, maxLength)];
  }

  /**
   * Load conversation context from Telegram session
   */
  private async loadConversationContext(
    context: TelegramBenjiContext
  ): Promise<TelegramBenjiContext> {
    if (!context.telegramUserId) {
      return context;
    }

    try {
      const session = await getTelegramSession(context.telegramUserId);

      if (session?.context && typeof session.context === "object") {
        const sessionContext = session.context as any;
        return {
          ...context,
          sessionId: session.id,
          messageHistory: sessionContext.messageHistory || [],
          ...sessionContext,
        };
      }
    } catch (error) {
      console.error(
        "❌ Telegram Benji: Error loading conversation context:",
        error
      );
    }

    return context;
  }

  /**
   * Save conversation context to Telegram session
   */
  private async saveConversationContext(
    context: TelegramBenjiContext,
    userMessage: string,
    aiResponse: any
  ) {
    if (!context.telegramUserId) {
      return;
    }

    try {
      const messageHistory = context.messageHistory || [];

      // Add user message
      messageHistory.push({
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      });

      // Add AI response (convert stream to text if needed)
      let responseText = "";
      if (aiResponse.textStream) {
        for await (const chunk of aiResponse.textStream) {
          responseText += chunk;
        }
      } else if (typeof aiResponse === "string") {
        responseText = aiResponse;
      }

      if (responseText) {
        messageHistory.push({
          role: "assistant",
          content: responseText,
          timestamp: new Date(),
        });
      }

      // Keep only last 20 messages to prevent session from growing too large
      const trimmedHistory = messageHistory.slice(-20);

      const sessionContext = {
        messageHistory: trimmedHistory,
        lastInteraction: new Date(),
        telegramChatId: context.telegramChatId,
      };

      if (context.sessionId) {
        // Update existing session
        await updateTelegramSession(context.sessionId, sessionContext);
      } else {
        // Create new session
        await createTelegramSession(context.telegramUserId, sessionContext);
      }
    } catch (error) {
      console.error(
        "❌ Telegram Benji: Error saving conversation context:",
        error
      );
    }
  }

  /**
   * Add Telegram-specific instructions to context (since we can't override buildSystemPrompt)
   */
  private addTelegramInstructions(
    context: TelegramBenjiContext = {}
  ): TelegramBenjiContext {
    const telegramInstructions = `
TELEGRAM PLATFORM INSTRUCTIONS:
- You are responding via Telegram bot
- Keep responses concise but informative  
- Use Markdown formatting when appropriate (*bold*, _italic_, \`code\`)
- Break long responses into multiple messages if needed
- Be conversational and friendly
- Respond quickly and efficiently
- If generating Twitter replies, format them clearly for copy-paste

CONVERSATION CONTEXT:
${
  context.messageHistory
    ? `Previous conversation:\n${context.messageHistory
        .slice(-5)
        .map((msg) => `${msg.role}: ${msg.content.substring(0, 100)}...`)
        .join("\n")}`
    : "This is the start of a new conversation."
}

RESPONSE FORMAT:
- For Twitter replies: Provide the reply text clearly formatted
- For general questions: Give direct, helpful answers
- For image requests: Describe what you're generating
- For searches: Summarize findings concisely
`;

    return {
      ...context,
      customSystemPrompt:
        (context.customSystemPrompt || "") + telegramInstructions,
    };
  }

  /**
   * Check if message contains Twitter URL
   */
  static isTwitterUrl(message: string): boolean {
    const twitterUrlRegex =
      /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/;
    return twitterUrlRegex.test(message);
  }

  /**
   * Extract Twitter URL from message
   */
  static extractTwitterUrl(message: string): string | null {
    const twitterUrlRegex =
      /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/;
    const match = message.match(twitterUrlRegex);
    return match ? match[0] : null;
  }

  /**
   * Check if message is a command
   */
  static isCommand(message: string): boolean {
    return message.startsWith("/");
  }

  /**
   * Parse command from message
   */
  static parseCommand(
    message: string
  ): { command: string; args: string[] } | null {
    if (!TelegramBenjiAgent.isCommand(message)) {
      return null;
    }

    const parts = message.split(" ");
    const command = parts[0].substring(1); // Remove the '/'
    const args = parts.slice(1);

    return { command, args };
  }
}

/**
 * Factory function to create TelegramBenjiAgent for a user
 */
export async function getTelegramBenjiForUser(
  userId: string,
  telegramUserId?: string,
  telegramChatId?: string
): Promise<TelegramBenjiAgent> {
  console.log("🤖 Telegram Benji: Creating agent for user:", userId);

  // Get user's configuration (reuse existing logic from getBenjiForUser)
  const { prisma } = await import("./db-utils");

  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      plan: true,
      selectedPersonality: true,
      selectedModel: true,
    },
  });

  if (!user) {
    throw new Error(`User not found: ${userId}`);
  }

  // Map database model names to ModelName enum
  let modelToUse;
  if (user.selectedModel) {
    const modelNameMap: Record<string, any> = {
      Workhorse: "gemini25Flash",
      Smarty: "gemini25Pro",
      "Big Brain": "openaiO3",
    };
    modelToUse = modelNameMap[user.selectedModel.name];
  }

  return new TelegramBenjiAgent({
    userId,
    userPlan: user.plan.name,
    model: modelToUse,
    enableTools: true,
    personalityPrompt: user.selectedPersonality?.systemPrompt,
    customSystemPrompt: user.customSystemPrompt || undefined,
    useFirstPerson: user.useFirstPerson ?? true,
    telegramUserId,
    telegramChatId,
  });
}
