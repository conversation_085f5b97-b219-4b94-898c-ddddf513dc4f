/**
 * Job Manager for BuddyChip
 *
 * Handles background job creation, processing, and management
 * Replaces unreliable setImmediate with database-backed job queue
 */

import type { PrismaClient } from "../../prisma/generated/index.js";
import { MentionSyncService } from "./mention-sync-service";

export type JobType = "sync_mentions" | "analyze_mention" | "cleanup_old_jobs";

export interface JobPayload {
  accountId?: string;
  userId?: string;
  handle?: string;
  mentionId?: string;
  [key: string]: any;
}

export interface CreateJobOptions {
  type: JobType;
  payload: JobPayload;
  priority?: number; // Lower number = higher priority
  scheduledAt?: Date;
  maxAttempts?: number;
  metadata?: Record<string, any>;
}

export interface ProcessJobResult {
  success: boolean;
  error?: string;
  retry?: boolean;
  metadata?: Record<string, any>;
}

export class JobManager {
  constructor(private prisma: PrismaClient) {}

  /**
   * Create a new background job
   */
  async createJob(options: CreateJobOptions) {
    const {
      type,
      payload,
      priority = 10,
      scheduledAt = new Date(),
      maxAttempts = 3,
      metadata = {},
    } = options;

    console.log(`📝 Creating job: ${type}`, { payload, priority });

    return await this.prisma.syncJob.create({
      data: {
        type,
        payload,
        priority,
        scheduledAt,
        maxAttempts,
        metadata,
      },
    });
  }

  /**
   * Get next pending jobs to process
   */
  async getNextJobs(limit = 10) {
    const now = new Date();

    return await this.prisma.syncJob.findMany({
      where: {
        status: "pending",
        scheduledAt: { lte: now },
        attempts: { lt: this.prisma.syncJob.fields.maxAttempts },
      },
      orderBy: [
        { priority: "asc" }, // Higher priority first (lower number)
        { scheduledAt: "asc" }, // Older jobs first
      ],
      take: limit,
    });
  }

  /**
   * Mark job as processing
   */
  async startProcessing(jobId: string) {
    return await this.prisma.syncJob.update({
      where: { id: jobId },
      data: {
        status: "processing",
        startedAt: new Date(),
        attempts: { increment: 1 },
      },
    });
  }

  /**
   * Mark job as completed
   */
  async completeJob(jobId: string, metadata?: Record<string, any>) {
    return await this.prisma.syncJob.update({
      where: { id: jobId },
      data: {
        status: "completed",
        completedAt: new Date(),
        metadata: metadata || {},
      },
    });
  }

  /**
   * Mark job as failed with exponential backoff retry
   */
  async failJob(jobId: string, error: string, retry = true) {
    const job = await this.prisma.syncJob.findUnique({
      where: { id: jobId },
    });

    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    const shouldRetry = retry && job.attempts < job.maxAttempts;
    const newStatus = shouldRetry ? "pending" : "failed";

    // Exponential backoff: 2^attempts minutes
    const retryDelayMinutes = shouldRetry ? 2 ** job.attempts : 0;
    const nextScheduledAt = shouldRetry
      ? new Date(Date.now() + retryDelayMinutes * 60 * 1000)
      : undefined;

    return await this.prisma.syncJob.update({
      where: { id: jobId },
      data: {
        status: newStatus,
        error,
        lastError: job.error, // Keep previous error
        completedAt: shouldRetry ? undefined : new Date(),
        scheduledAt: nextScheduledAt || job.scheduledAt,
      },
    });
  }

  /**
   * Process a single job
   */
  async processJob(job: any): Promise<ProcessJobResult> {
    console.log(`⚡ Processing job ${job.id}: ${job.type}`);

    try {
      switch (job.type) {
        case "sync_mentions":
          return await this.processSyncMentionsJob(job);

        case "analyze_mention":
          return await this.processAnalyzeMentionJob(job);

        case "cleanup_old_jobs":
          return await this.processCleanupJob(job);

        default:
          return {
            success: false,
            error: `Unknown job type: ${job.type}`,
            retry: false,
          };
      }
    } catch (error) {
      console.error(`❌ Job ${job.id} processing error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        retry: true,
      };
    }
  }

  /**
   * Process sync_mentions job
   */
  private async processSyncMentionsJob(job: any): Promise<ProcessJobResult> {
    const { accountId, userId, handle } = job.payload;

    if (!accountId || !userId) {
      return {
        success: false,
        error: "Missing required fields: accountId, userId",
        retry: false,
      };
    }

    console.log(`🔄 Syncing mentions for account ${accountId} (${handle})`);

    const syncService = new MentionSyncService(this.prisma);
    const syncResult = await syncService.syncAccountMentions(accountId, userId);

    if (syncResult.success) {
      console.log(
        `✅ Sync completed: ${syncResult.newMentions} new mentions for ${handle}`
      );
      return {
        success: true,
        metadata: {
          newMentions: syncResult.newMentions,
          totalMentions: syncResult.totalMentions,
          handle,
        },
      };
    } else {
      console.log(`⚠️ Sync failed for ${handle}: ${syncResult.error}`);
      return {
        success: false,
        error: syncResult.error || "Sync failed",
        retry: true,
      };
    }
  }

  /**
   * Process analyze_mention job (placeholder for future AI analysis)
   */
  private async processAnalyzeMentionJob(job: any): Promise<ProcessJobResult> {
    const { mentionId } = job.payload;

    // Placeholder - could be used for AI sentiment analysis, keyword extraction, etc.
    console.log(`🤖 Analyzing mention ${mentionId}`);

    return {
      success: true,
      metadata: { analyzed: true },
    };
  }

  /**
   * Process cleanup job to remove old completed jobs
   */
  private async processCleanupJob(job: any): Promise<ProcessJobResult> {
    const daysToKeep = job.payload.daysToKeep || 7;
    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

    console.log(`🧹 Cleaning up jobs older than ${daysToKeep} days`);

    const deleteResult = await this.prisma.syncJob.deleteMany({
      where: {
        status: { in: ["completed", "failed"] },
        completedAt: { lt: cutoffDate },
      },
    });

    console.log(`🗑️ Deleted ${deleteResult.count} old jobs`);

    return {
      success: true,
      metadata: { deletedCount: deleteResult.count },
    };
  }

  /**
   * Process all pending jobs (used by cron endpoint)
   */
  async processAllPendingJobs(limit = 50) {
    console.log("🚀 Starting job processing cycle");

    const jobs = await this.getNextJobs(limit);

    if (jobs.length === 0) {
      console.log("💤 No pending jobs to process");
      return { processed: 0, successful: 0, failed: 0 };
    }

    console.log(`📋 Found ${jobs.length} pending jobs`);

    let successful = 0;
    let failed = 0;

    for (const job of jobs) {
      try {
        // Mark as processing
        await this.startProcessing(job.id);

        // Process the job
        const result = await this.processJob(job);

        if (result.success) {
          await this.completeJob(job.id, result.metadata);
          successful++;
        } else {
          await this.failJob(
            job.id,
            result.error || "Unknown error",
            result.retry !== false
          );
          failed++;
        }

        // Small delay between jobs to avoid overwhelming external APIs
        await new Promise((resolve) => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`❌ Failed to process job ${job.id}:`, error);
        await this.failJob(
          job.id,
          error instanceof Error ? error.message : "Unknown error"
        );
        failed++;
      }
    }

    console.log(
      `🏁 Job processing complete: ${successful} successful, ${failed} failed`
    );

    return {
      processed: jobs.length,
      successful,
      failed,
    };
  }

  /**
   * Get job statistics for monitoring
   */
  async getJobStats() {
    const [pending, processing, completed, failed] = await Promise.all([
      this.prisma.syncJob.count({ where: { status: "pending" } }),
      this.prisma.syncJob.count({ where: { status: "processing" } }),
      this.prisma.syncJob.count({ where: { status: "completed" } }),
      this.prisma.syncJob.count({ where: { status: "failed" } }),
    ]);

    return { pending, processing, completed, failed };
  }

  /**
   * Clean up old completed jobs (can be called manually or via job)
   */
  async cleanupOldJobs(daysToKeep = 7) {
    return await this.createJob({
      type: "cleanup_old_jobs",
      payload: { daysToKeep },
      priority: 50, // Low priority
    });
  }
}

// Convenience function to create common job types
export const JobHelpers = {
  /**
   * Create a mention sync job (replaces setImmediate usage)
   */
  createMentionSyncJob: (
    prisma: PrismaClient,
    accountId: string,
    userId: string,
    handle: string
  ) => {
    const jobManager = new JobManager(prisma);
    return jobManager.createJob({
      type: "sync_mentions",
      payload: { accountId, userId, handle },
      priority: 1, // High priority for new account syncs
    });
  },

  /**
   * Create an analyze mention job
   */
  createAnalyzeMentionJob: (prisma: PrismaClient, mentionId: string) => {
    const jobManager = new JobManager(prisma);
    return jobManager.createJob({
      type: "analyze_mention",
      payload: { mentionId },
      priority: 20, // Medium priority
    });
  },
};
