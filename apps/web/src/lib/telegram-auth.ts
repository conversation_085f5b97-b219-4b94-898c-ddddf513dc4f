/**
 * Telegram Authentication Utilities
 *
 * Handles linking Telegram users with BuddyChip accounts
 */

import crypto from "crypto";
import { prisma } from "./db-utils";

export interface TelegramLinkCode {
  code: string;
  telegramChatId: string;
  expiresAt: Date;
  isUsed: boolean;
}

export interface TelegramAuthResult {
  success: boolean;
  message: string;
  telegramUser?: any;
}

/**
 * Generate a secure link code for Telegram account linking
 */
export function generateTelegramLinkCode(
  telegramChatId: string
): TelegramLinkCode {
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(8).toString("hex");
  const code = `TG_${telegramChatId}_${timestamp}_${randomBytes}`;

  return {
    code,
    telegramChatId,
    expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    isUsed: false,
  };
}

/**
 * Validate and parse a Telegram link code
 */
export function parseTelegramLinkCode(
  code: string
): { telegramChatId: string; timestamp: number } | null {
  try {
    const parts = code.split("_");
    if (parts.length !== 4 || parts[0] !== "TG") {
      return null;
    }

    const telegramChatId = parts[1];
    const timestamp = parseInt(parts[2]);

    if (isNaN(timestamp)) {
      return null;
    }

    return { telegramChatId, timestamp };
  } catch (error) {
    console.error("❌ Telegram Auth: Error parsing link code:", error);
    return null;
  }
}

/**
 * Link a Telegram user to a BuddyChip user account
 */
export async function linkTelegramAccount(
  buddyChipUserId: string,
  linkCode: string
): Promise<TelegramAuthResult> {
  try {
    console.log(
      "🔗 Telegram Auth: Attempting to link account with code:",
      linkCode
    );

    // Parse the link code
    const parsed = parseTelegramLinkCode(linkCode);
    if (!parsed) {
      return {
        success: false,
        message: "Invalid link code format",
      };
    }

    const { telegramChatId, timestamp } = parsed;

    // Check if code is expired (10 minutes)
    const codeAge = Date.now() - timestamp;
    const maxAge = 10 * 60 * 1000; // 10 minutes

    if (codeAge > maxAge) {
      return {
        success: false,
        message:
          "Link code has expired. Please generate a new one from Telegram.",
      };
    }

    // Check if BuddyChip user exists
    const buddyChipUser = await prisma.user.findUnique({
      where: { id: buddyChipUserId },
    });

    if (!buddyChipUser) {
      return {
        success: false,
        message: "BuddyChip user not found",
      };
    }

    // Find the Telegram user by chat ID
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramChatId },
    });

    if (!telegramUser) {
      return {
        success: false,
        message:
          "Telegram user not found. Please start a conversation with the bot first.",
      };
    }

    // Check if this Telegram user is already linked to another account
    if (telegramUser.userId && telegramUser.userId !== buddyChipUserId) {
      return {
        success: false,
        message:
          "This Telegram account is already linked to another BuddyChip account.",
      };
    }

    // Check if this BuddyChip user already has a linked Telegram account
    const existingLink = await prisma.telegramUser.findFirst({
      where: {
        userId: buddyChipUserId,
        id: { not: telegramUser.id },
      },
    });

    if (existingLink) {
      return {
        success: false,
        message:
          "Your BuddyChip account is already linked to another Telegram account.",
      };
    }

    // Link the accounts
    const updatedTelegramUser = await prisma.telegramUser.update({
      where: { id: telegramUser.id },
      data: {
        userId: buddyChipUserId,
        isActive: true,
      },
    });

    console.log("✅ Telegram Auth: Successfully linked accounts:", {
      buddyChipUserId,
      telegramUserId: telegramUser.id,
      telegramChatId,
    });

    return {
      success: true,
      message:
        "Telegram account successfully linked! You can now use all AI features.",
      telegramUser: updatedTelegramUser,
    };
  } catch (error) {
    console.error("❌ Telegram Auth: Error linking account:", error);
    return {
      success: false,
      message:
        "An error occurred while linking your account. Please try again.",
    };
  }
}

/**
 * Unlink a Telegram account from a BuddyChip user
 */
export async function unlinkTelegramAccount(
  buddyChipUserId: string
): Promise<TelegramAuthResult> {
  try {
    console.log(
      "🔓 Telegram Auth: Unlinking account for user:",
      buddyChipUserId
    );

    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId: buddyChipUserId },
    });

    if (!telegramUser) {
      return {
        success: false,
        message: "No linked Telegram account found",
      };
    }

    // Unlink the account
    await prisma.telegramUser.update({
      where: { id: telegramUser.id },
      data: { userId: null },
    });

    console.log("✅ Telegram Auth: Successfully unlinked account");

    return {
      success: true,
      message: "Telegram account successfully unlinked",
    };
  } catch (error) {
    console.error("❌ Telegram Auth: Error unlinking account:", error);
    return {
      success: false,
      message:
        "An error occurred while unlinking your account. Please try again.",
    };
  }
}

/**
 * Get linked Telegram account for a BuddyChip user
 */
export async function getLinkedTelegramAccount(buddyChipUserId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findFirst({
      where: { userId: buddyChipUserId },
    });

    return telegramUser;
  } catch (error) {
    console.error("❌ Telegram Auth: Error getting linked account:", error);
    return null;
  }
}

/**
 * Get BuddyChip user for a Telegram user
 */
export async function getBuddyChipUserForTelegram(telegramChatId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramChatId },
      include: { user: true },
    });

    return telegramUser?.user || null;
  } catch (error) {
    console.error("❌ Telegram Auth: Error getting BuddyChip user:", error);
    return null;
  }
}

/**
 * Validate Telegram webhook signature (for security)
 */
export function validateTelegramWebhook(
  body: string,
  signature: string,
  botToken: string
): boolean {
  try {
    const secretKey = crypto.createHash("sha256").update(botToken).digest();
    const hmac = crypto.createHmac("sha256", secretKey);
    hmac.update(body);
    const calculatedSignature = hmac.digest("hex");

    return crypto.timingSafeEqual(
      Buffer.from(signature, "hex"),
      Buffer.from(calculatedSignature, "hex")
    );
  } catch (error) {
    console.error(
      "❌ Telegram Auth: Error validating webhook signature:",
      error
    );
    return false;
  }
}

/**
 * Check if a Telegram user has access to premium features
 */
export async function checkTelegramUserAccess(telegramChatId: string): Promise<{
  hasAccess: boolean;
  plan?: string;
  userId?: string;
}> {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramChatId },
      include: {
        user: {
          include: { plan: true },
        },
      },
    });

    if (!telegramUser?.user) {
      return { hasAccess: false };
    }

    return {
      hasAccess: telegramUser.user.plan.isActive,
      plan: telegramUser.user.plan.name,
      userId: telegramUser.user.id,
    };
  } catch (error) {
    console.error("❌ Telegram Auth: Error checking user access:", error);
    return { hasAccess: false };
  }
}

/**
 * Create a session for conversation context
 */
export async function createTelegramSession(
  telegramUserId: string,
  context?: any
) {
  try {
    const session = await prisma.telegramSession.create({
      data: {
        telegramUserId,
        context: context || {},
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      },
    });

    return session;
  } catch (error) {
    console.error("❌ Telegram Auth: Error creating session:", error);
    return null;
  }
}

/**
 * Get active session for a Telegram user
 */
export async function getTelegramSession(telegramUserId: string) {
  try {
    const session = await prisma.telegramSession.findFirst({
      where: {
        telegramUserId,
        isActive: true,
        expiresAt: { gt: new Date() },
      },
      orderBy: { createdAt: "desc" },
    });

    return session;
  } catch (error) {
    console.error("❌ Telegram Auth: Error getting session:", error);
    return null;
  }
}

/**
 * Update session context
 */
export async function updateTelegramSession(sessionId: string, context: any) {
  try {
    const session = await prisma.telegramSession.update({
      where: { id: sessionId },
      data: {
        context,
        updatedAt: new Date(),
      },
    });

    return session;
  } catch (error) {
    console.error("❌ Telegram Auth: Error updating session:", error);
    return null;
  }
}
