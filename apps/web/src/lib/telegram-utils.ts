/**
 * Telegram Utilities
 *
 * Helper functions for Telegram bot operations
 */

import { TelegramBotService } from "./telegram-bot";

/**
 * Initialize Telegram bot service
 */
export function initializeTelegramBot(): TelegramBotService | null {
  try {
    const token = process.env.TELEGRAM_BOT_TOKEN;

    if (!token) {
      console.warn("⚠️ Telegram: TELEGRAM_BOT_TOKEN not configured");
      return null;
    }

    console.log("🤖 Telegram: Initializing bot service");

    return new TelegramBotService({
      token,
      enablePolling: process.env.NODE_ENV === "development",
      webhookUrl:
        process.env.NODE_ENV === "production"
          ? `${process.env.NEXT_PUBLIC_APP_URL}/api/telegram/webhook`
          : undefined,
    });
  } catch (error) {
    console.error("❌ Telegram: Error initializing bot service:", error);
    return null;
  }
}

/**
 * Validate Telegram bot configuration
 */
export function validateTelegramConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required environment variables
  if (!process.env.TELEGRAM_BOT_TOKEN) {
    errors.push("TELEGRAM_BOT_TOKEN is required");
  }

  if (
    !process.env.NEXT_PUBLIC_APP_URL &&
    process.env.NODE_ENV === "production"
  ) {
    errors.push(
      "NEXT_PUBLIC_APP_URL is required for webhook setup in production"
    );
  }

  // Check optional environment variables
  if (!process.env.TELEGRAM_WEBHOOK_SECRET) {
    warnings.push(
      "TELEGRAM_WEBHOOK_SECRET is recommended for webhook security"
    );
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Format text for Telegram Markdown
 */
export function formatTelegramMarkdown(text: string): string {
  // Escape special characters for Telegram MarkdownV2
  return text
    .replace(/[_*[\]()~`>#+=|{}.!-]/g, "\\$&")
    .replace(/\\\*/g, "*") // Allow intentional bold
    .replace(/\\_/g, "_") // Allow intentional italic
    .replace(/\\`/g, "`"); // Allow intentional code
}

/**
 * Create inline keyboard for Telegram
 */
export function createInlineKeyboard(
  buttons: Array<
    Array<{
      text: string;
      callback_data?: string;
      url?: string;
    }>
  >
) {
  return {
    inline_keyboard: buttons,
  };
}

/**
 * Extract Twitter URL from text
 */
export function extractTwitterUrl(text: string): string | null {
  const twitterUrlRegex = /https?:\/\/(twitter\.com|x\.com)\/\w+\/status\/\d+/;
  const match = text.match(twitterUrlRegex);
  return match ? match[0] : null;
}

/**
 * Check if text contains Twitter URL
 */
export function containsTwitterUrl(text: string): boolean {
  return extractTwitterUrl(text) !== null;
}

/**
 * Parse Telegram command
 */
export function parseCommand(text: string): {
  command: string;
  args: string[];
} | null {
  if (!text.startsWith("/")) {
    return null;
  }

  const parts = text.split(" ");
  const command = parts[0].substring(1); // Remove the '/'
  const args = parts.slice(1);

  return { command, args };
}

/**
 * Generate help text for Telegram bot
 */
export function generateHelpText(): string {
  return `
📚 *BuddyChip AI Help*

*Available Commands:*
/start - Welcome message and setup
/help - Show this help message
/settings - Account settings and preferences
/status - Check usage limits and account status

*Features:*
🐦 *Twitter Integration*
• Send any Twitter/X link
• Get instant AI-generated replies
• Perfect for social media engagement

🔍 *Web Search*
• Ask questions about current events
• Get real-time information
• Powered by advanced search tools

🎨 *Image Generation*
• Request AI-generated images
• Use detailed prompts for best results
• High-quality DALL-E 3 powered

💬 *Conversations*
• Natural language interactions
• Context-aware responses
• Multi-turn conversations

*Tips:*
• Be specific in your requests
• Use detailed prompts for images
• Check /status for usage limits
• Link your account for full access

Need more help? Contact support through the BuddyChip dashboard.
  `.trim();
}

/**
 * Generate status text for user
 */
export function generateStatusText(user: any): string {
  return `
📊 *Account Status*

*Plan:* ${user.plan?.name || "Unknown"}
*Status:* ${user.isActive ? "✅ Active" : "❌ Inactive"}

*Usage Limits:*
• AI Calls: ${user.plan?.aiCallsLimit === -1 ? "Unlimited" : (user.plan?.aiCallsLimit || 0) + "/month"}
• Image Generation: ${user.plan?.imageGenerationLimit === -1 ? "Unlimited" : (user.plan?.imageGenerationLimit || 0) + "/month"}

*Account Info:*
• Member since: ${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : "Unknown"}
• Last active: ${user.lastActiveAt ? new Date(user.lastActiveAt).toLocaleDateString() : "Never"}

Visit buddychip.app for detailed usage analytics.
  `.trim();
}

/**
 * Generate welcome text for new users
 */
export function generateWelcomeText(isLinked: boolean): string {
  return `
🤖 *Welcome to BuddyChip AI!*

I'm Benji, your AI assistant with access to powerful tools and capabilities.

*What I can do:*
• 🐦 Analyze Twitter/X links and generate smart replies
• 🔍 Search the web for real-time information
• 🎨 Generate images with AI
• 💬 Have intelligent conversations
• 📊 Provide crypto market insights

*Getting Started:*
${
  isLinked
    ? "✅ Your account is already linked! You can start using all features."
    : "🔗 Link your BuddyChip account with /settings to unlock all features."
}

*Commands:*
/help - Show all available commands
/settings - Manage your account and preferences
/status - Check your usage and limits

*Try me:*
• Send me a Twitter/X link for instant AI replies
• Ask me anything - I'll search and provide answers
• Request image generation with detailed prompts

Ready to get started? 🚀
  `.trim();
}

/**
 * Generate settings text for user
 */
export function generateSettingsText(telegramUser: any): string {
  return `
⚙️ *Account Settings*

*Account Status:*
${
  telegramUser.userId
    ? "✅ Linked to BuddyChip account"
    : "❌ Not linked - limited functionality"
}

*Current Settings:*
• Username: @${telegramUser.username || "Not set"}
• Active: ${telegramUser.isActive ? "✅" : "❌"}
• Last active: ${telegramUser.lastActiveAt ? new Date(telegramUser.lastActiveAt).toLocaleDateString() : "Never"}

${
  !telegramUser.userId
    ? "*To unlock all features:*\n1. Visit buddychip.app\n2. Create an account\n3. Use the link below to connect"
    : "*Manage your subscription and preferences at buddychip.app*"
}
  `.trim();
}

/**
 * Validate environment variables for Telegram integration
 */
export function checkTelegramEnvironment(): {
  ready: boolean;
  missing: string[];
  optional: string[];
} {
  const required = ["TELEGRAM_BOT_TOKEN"];
  const optional = ["TELEGRAM_WEBHOOK_SECRET", "NEXT_PUBLIC_APP_URL"];

  const missing = required.filter((key) => !process.env[key]);
  const missingOptional = optional.filter((key) => !process.env[key]);

  return {
    ready: missing.length === 0,
    missing,
    optional: missingOptional,
  };
}

/**
 * Log Telegram bot activity
 */
export function logTelegramActivity(
  action: string,
  userId?: string,
  chatId?: string,
  details?: any
) {
  console.log(`📱 Telegram Activity: ${action}`, {
    userId,
    chatId,
    timestamp: new Date().toISOString(),
    ...details,
  });
}

/**
 * Handle Telegram errors gracefully
 */
export function handleTelegramError(error: any, context: string): string {
  console.error(`❌ Telegram Error (${context}):`, error);

  // Return user-friendly error messages
  if (error.code === "ETELEGRAM") {
    switch (error.response?.body?.error_code) {
      case 400:
        return "Invalid request. Please try again.";
      case 401:
        return "Bot authentication failed. Please contact support.";
      case 403:
        return "Bot was blocked by user or lacks permissions.";
      case 429:
        return "Too many requests. Please wait a moment and try again.";
      default:
        return "Telegram service error. Please try again later.";
    }
  }

  return "An unexpected error occurred. Please try again.";
}
