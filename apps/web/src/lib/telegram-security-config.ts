/**
 * Telegram Security Configuration System
 * 
 * Centralized, environment-aware security configuration for Telegram webhook
 */

import { telegramLogger } from './telegram-logger';

export type SecurityLevel = 'DEVELOPMENT' | 'STAGING' | 'PRODUCTION';

export interface TelegramSecurityConfig {
  // Environment
  environment: SecurityLevel;
  
  // Security settings
  enableWebhookSecretValidation: boolean;
  enableIPValidation: boolean;
  enableRateLimit: boolean;
  enableContentValidation: boolean;
  
  // Telegram settings
  botToken?: string;
  webhookSecret?: string;
  allowedIPs: string[];
  
  // Limits
  maxPayloadSize: number;
  maxRequestsPerMinute: number;
  webhookTimeout: number;
  
  // Behavior
  allowBypassInDevelopment: boolean;
  logSecurityEvents: boolean;
  blockOnSecurityFailure: boolean;
}

export class TelegramSecurityManager {
  private static instance: TelegramSecurityManager;
  private config: TelegramSecurityConfig;
  private isConfigValid: boolean = false;
  private configErrors: string[] = [];

  private constructor() {
    this.config = this.loadConfiguration();
    this.validateConfiguration();
  }

  public static getInstance(): TelegramSecurityManager {
    if (!TelegramSecurityManager.instance) {
      TelegramSecurityManager.instance = new TelegramSecurityManager();
    }
    return TelegramSecurityManager.instance;
  }

  private detectEnvironment(): SecurityLevel {
    // Check explicit environment variable
    const envVar = process.env.NODE_ENV?.toUpperCase();
    if (envVar === 'PRODUCTION') return 'PRODUCTION';
    if (envVar === 'STAGING') return 'STAGING';
    if (envVar === 'DEVELOPMENT') return 'DEVELOPMENT';

    // Check deployment platform indicators
    if (process.env.VERCEL_ENV === 'production') return 'PRODUCTION';
    if (process.env.VERCEL_ENV === 'preview') return 'STAGING';
    if (process.env.VERCEL_ENV === 'development') return 'DEVELOPMENT';

    // Check for development indicators
    if (process.env.NODE_ENV === 'development' || !process.env.NODE_ENV) {
      return 'DEVELOPMENT';
    }

    // Default to production for safety
    return 'PRODUCTION';
  }

  private loadConfiguration(): TelegramSecurityConfig {
    const environment = this.detectEnvironment();
    
    telegramLogger.info('Loading Telegram security configuration', {
      metadata: { environment }
    });

    // Get environment-specific settings
    const envConfig = this.getEnvironmentSpecificConfig(environment);

    // Base configuration
    const baseConfig: TelegramSecurityConfig = {
      environment,
      botToken: process.env.TELEGRAM_BOT_TOKEN,
      webhookSecret: process.env.TELEGRAM_WEBHOOK_SECRET,
      allowedIPs: this.getTelegramIPRanges(),
      maxPayloadSize: 10 * 1024, // 10KB
      maxRequestsPerMinute: 60,
      webhookTimeout: 30000, // 30 seconds
      logSecurityEvents: true,

      // Environment-specific settings
      enableWebhookSecretValidation: envConfig.enableWebhookSecretValidation!,
      enableIPValidation: envConfig.enableIPValidation!,
      enableRateLimit: envConfig.enableRateLimit!,
      enableContentValidation: envConfig.enableContentValidation!,
      allowBypassInDevelopment: envConfig.allowBypassInDevelopment!,
      blockOnSecurityFailure: envConfig.blockOnSecurityFailure!,
    };

    return baseConfig;
  }

  private getEnvironmentSpecificConfig(environment: SecurityLevel): Partial<TelegramSecurityConfig> {
    switch (environment) {
      case 'DEVELOPMENT':
        return {
          enableWebhookSecretValidation: false, // Optional in dev
          enableIPValidation: false, // Disabled in dev
          enableRateLimit: false, // Disabled in dev
          enableContentValidation: true, // Basic validation
          allowBypassInDevelopment: true,
          blockOnSecurityFailure: false, // Log but don't block
        };

      case 'STAGING':
        return {
          enableWebhookSecretValidation: true,
          enableIPValidation: false, // May use different IPs
          enableRateLimit: true,
          enableContentValidation: true,
          allowBypassInDevelopment: false,
          blockOnSecurityFailure: true,
        };

      case 'PRODUCTION':
        return {
          enableWebhookSecretValidation: true,
          enableIPValidation: true,
          enableRateLimit: true,
          enableContentValidation: true,
          allowBypassInDevelopment: false,
          blockOnSecurityFailure: true,
        };

      default:
        throw new Error(`Unknown environment: ${environment}`);
    }
  }

  private getTelegramIPRanges(): string[] {
    // Official Telegram webhook IP ranges
    return [
      '*************/20',
      '**********/22',
      '***********/22',
      '***********/23',
      '*************/21',
      '*************/22',
      '*************/22',
      '*************/22',
    ];
  }

  private validateConfiguration(): void {
    this.configErrors = [];

    // Validate bot token
    if (!this.config.botToken) {
      this.configErrors.push('TELEGRAM_BOT_TOKEN is required');
    }

    // Validate webhook secret for production
    if (this.config.enableWebhookSecretValidation && !this.config.webhookSecret) {
      if (this.config.environment === 'PRODUCTION') {
        this.configErrors.push('TELEGRAM_WEBHOOK_SECRET is required in production');
      } else {
        telegramLogger.warn('TELEGRAM_WEBHOOK_SECRET not configured', {
          metadata: { environment: this.config.environment }
        });
      }
    }

    // Validate IP ranges
    if (this.config.enableIPValidation && this.config.allowedIPs.length === 0) {
      this.configErrors.push('No allowed IP ranges configured for IP validation');
    }

    this.isConfigValid = this.configErrors.length === 0;

    if (!this.isConfigValid) {
      telegramLogger.error('Telegram security configuration validation failed', {
        metadata: { errors: this.configErrors }
      });
    } else {
      telegramLogger.info('Telegram security configuration validated successfully', {
        metadata: {
          environment: this.config.environment,
          webhookSecretValidation: this.config.enableWebhookSecretValidation,
          ipValidation: this.config.enableIPValidation,
          rateLimit: this.config.enableRateLimit
        }
      });
    }
  }

  // Public API
  public getConfig(): TelegramSecurityConfig {
    return { ...this.config };
  }

  public isValid(): boolean {
    return this.isConfigValid;
  }

  public getErrors(): string[] {
    return [...this.configErrors];
  }

  public getEnvironment(): SecurityLevel {
    return this.config.environment;
  }

  public shouldEnforceWebhookSecret(): boolean {
    return this.config.enableWebhookSecretValidation && !!this.config.webhookSecret;
  }

  public shouldValidateIP(): boolean {
    return this.config.enableIPValidation;
  }

  public shouldRateLimit(): boolean {
    return this.config.enableRateLimit;
  }

  public canBypassSecurity(): boolean {
    return this.config.allowBypassInDevelopment && this.config.environment === 'DEVELOPMENT';
  }

  public shouldBlockOnFailure(): boolean {
    return this.config.blockOnSecurityFailure;
  }

  // Utility methods
  public logConfigurationStatus(): void {
    const config = this.getConfig();
    
    telegramLogger.info('Telegram Security Configuration Status', {
      metadata: {
        environment: config.environment,
        isValid: this.isConfigValid,
        errors: this.configErrors,
        settings: {
          webhookSecretValidation: config.enableWebhookSecretValidation,
          ipValidation: config.enableIPValidation,
          rateLimit: config.enableRateLimit,
          contentValidation: config.enableContentValidation,
          allowBypass: config.allowBypassInDevelopment,
          blockOnFailure: config.blockOnSecurityFailure
        }
      }
    });
  }
}

// Export singleton instance
export const telegramSecurity = TelegramSecurityManager.getInstance();
