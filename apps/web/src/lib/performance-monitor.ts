/**
 * Performance monitoring utilities for BuddyChip
 * Tracks query performance and provides optimization insights
 */

interface QueryMetrics {
  query: string;
  duration: number;
  timestamp: Date;
  params?: any;
  userId?: string;
  resultCount?: number;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: QueryMetrics[] = [];
  private readonly MAX_METRICS = 1000; // Keep last 1000 queries

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Track a database query with timing
   */
  async trackQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>,
    params?: any,
    userId?: string
  ): Promise<T> {
    const startTime = performance.now();

    try {
      const result = await queryFn();
      const duration = performance.now() - startTime;

      // Determine result count if it's an array
      let resultCount: number | undefined;
      if (Array.isArray(result)) {
        resultCount = result.length;
      } else if (result && typeof result === "object" && "length" in result) {
        resultCount = (result as any).length;
      }

      this.recordMetric({
        query: queryName,
        duration,
        timestamp: new Date(),
        params,
        userId,
        resultCount,
      });

      // Log slow queries
      if (duration > 1000) {
        console.warn(
          `🐌 Slow query detected: ${queryName} took ${duration.toFixed(2)}ms`,
          {
            params,
            userId,
            resultCount,
          }
        );
      } else if (duration > 500) {
        console.info(
          `⚠️ Moderate query: ${queryName} took ${duration.toFixed(2)}ms`,
          {
            resultCount,
          }
        );
      } else {
        console.log(
          `⚡ Fast query: ${queryName} took ${duration.toFixed(2)}ms`,
          {
            resultCount,
          }
        );
      }

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(
        `❌ Query failed: ${queryName} failed after ${duration.toFixed(2)}ms`,
        {
          error: error instanceof Error ? error.message : String(error),
          params,
          userId,
        }
      );
      throw error;
    }
  }

  private recordMetric(metric: QueryMetrics): void {
    this.metrics.push(metric);

    // Keep only the last MAX_METRICS entries
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }
  }

  /**
   * Get performance statistics
   */
  getStats(minutes: number = 60): {
    averageDuration: number;
    slowQueries: QueryMetrics[];
    queryCount: number;
    topSlowQueries: Array<{
      query: string;
      avgDuration: number;
      count: number;
    }>;
  } {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    const recentMetrics = this.metrics.filter((m) => m.timestamp > cutoff);

    if (recentMetrics.length === 0) {
      return {
        averageDuration: 0,
        slowQueries: [],
        queryCount: 0,
        topSlowQueries: [],
      };
    }

    const averageDuration =
      recentMetrics.reduce((sum, m) => sum + m.duration, 0) /
      recentMetrics.length;
    const slowQueries = recentMetrics.filter((m) => m.duration > 1000);

    // Group by query name and calculate averages
    const queryGroups = recentMetrics.reduce(
      (acc, metric) => {
        if (!acc[metric.query]) {
          acc[metric.query] = { durations: [], count: 0 };
        }
        acc[metric.query].durations.push(metric.duration);
        acc[metric.query].count++;
        return acc;
      },
      {} as Record<string, { durations: number[]; count: number }>
    );

    const topSlowQueries = Object.entries(queryGroups)
      .map(([query, data]) => ({
        query,
        avgDuration:
          data.durations.reduce((sum, d) => sum + d, 0) / data.durations.length,
        count: data.count,
      }))
      .sort((a, b) => b.avgDuration - a.avgDuration)
      .slice(0, 10);

    return {
      averageDuration,
      slowQueries,
      queryCount: recentMetrics.length,
      topSlowQueries,
    };
  }

  /**
   * Log performance summary
   */
  logSummary(minutes: number = 60): void {
    const stats = this.getStats(minutes);

    console.log(`📊 Performance Summary (last ${minutes} minutes):`);
    console.log(`   Total queries: ${stats.queryCount}`);
    console.log(`   Average duration: ${stats.averageDuration.toFixed(2)}ms`);
    console.log(`   Slow queries (>1s): ${stats.slowQueries.length}`);

    if (stats.topSlowQueries.length > 0) {
      console.log(`   Top slow queries:`);
      stats.topSlowQueries.slice(0, 5).forEach((query, index) => {
        console.log(
          `     ${index + 1}. ${query.query}: ${query.avgDuration.toFixed(2)}ms avg (${query.count} calls)`
        );
      });
    }
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * Helper decorator for tracking Prisma queries
 */
export function trackPrismaQuery<T>(
  queryName: string,
  params?: any,
  userId?: string
) {
  return (queryFn: () => Promise<T>): Promise<T> => {
    return performanceMonitor.trackQuery(queryName, queryFn, params, userId);
  };
}
