/**
 * Mem0 Memory Service for BuddyChip
 *
 * Provides centralized memory management for the Benji AI agent using self-hosted mem0
 * with Supabase as the vector database. Includes user-specific memory isolation,
 * conversation context management, and integration with the existing rate limiting system.
 */

// Type definitions for mem0ai (since the package may not have proper TypeScript definitions)
interface Memory {
  search(params: any): Promise<any[]>;
  add(messages: any[], userId: string, metadata?: any): Promise<void>;
  delete(memoryId: string): Promise<void>;
  get_all(params: any): Promise<any[]>;
}

interface MemoryClass {
  from_config(config: any): Memory;
}

import { safeDbOperation } from "./db-utils";
import { canUserUseClerkFeature, logUsage } from "./user-service";

// Types for better TypeScript support
export interface MemoryItem {
  id: string;
  content: string;
  metadata: Record<string, any>;
  similarity?: number;
  memoryType?: string;
  createdAt?: Date;
}

export interface ConversationContext {
  userId: string;
  sessionId?: string;
  mentionId?: string;
  authorInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
  monitoredAccountInfo?: {
    name: string;
    handle: string;
    avatarUrl?: string;
  };
}

export interface MemorySearchOptions {
  query?: string;
  limit?: number;
  similarityThreshold?: number;
  memoryType?: string;
  metadata?: Record<string, any>;
}

export interface MemoryAddOptions {
  memoryType?: string;
  metadata?: Record<string, any>;
  relevanceScore?: number;
}

// Singleton pattern for mem0 instance management
class Mem0Service {
  private memoryInstance: Memory | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  /**
   * Initialize mem0 with Supabase configuration
   */
  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    if (this.initializationPromise) {
      await this.initializationPromise;
      return;
    }

    this.initializationPromise = this._doInitialize();
    await this.initializationPromise;
  }

  private async _doInitialize(): Promise<void> {
    try {
      console.log("🧠 Mem0Service: Initializing mem0 with Supabase...");

      // Validate required environment variables
      if (!process.env.DATABASE_URL) {
        throw new Error(
          "DATABASE_URL is required for mem0 Supabase integration"
        );
      }
      if (!process.env.OPENAI_API_KEY && !process.env.OPENROUTER_API_KEY) {
        throw new Error(
          "At least one of OPENAI_API_KEY or OPENROUTER_API_KEY is required for mem0"
        );
      }

      // Extract PostgreSQL connection string from DATABASE_URL
      const connectionString =
        process.env.DIRECT_URL || process.env.DATABASE_URL;

      // Configure mem0 with self-hosted Supabase
      const config = {
        vector_store: {
          provider: "supabase",
          config: {
            connection_string: connectionString,
            collection_name: "memories",
            index_method: "hnsw",
            index_measure: "cosine_distance",
          },
        },
        llm: {
          provider: "openai",
          config: {
            model: "gpt-4o-mini", // Use efficient model for memory processing
            api_key: process.env.OPENAI_API_KEY,
            temperature: 0.1, // Low temperature for consistent memory extraction
          },
        },
        // Optional: Use OpenRouter as fallback
        ...(process.env.OPENROUTER_API_KEY &&
          !process.env.OPENAI_API_KEY && {
            llm: {
              provider: "openrouter",
              config: {
                model: "google/gemini-2.0-flash-exp",
                api_key: process.env.OPENROUTER_API_KEY,
                base_url: "https://openrouter.ai/api/v1",
                temperature: 0.1,
              },
            },
          }),
      };

      // Initialize mem0 instance (dynamic import to handle potential module issues)
      try {
        const mem0Module = await import("mem0ai");
        // Handle different possible export structures
        const MemoryClass =
          (mem0Module as any).Memory ||
          (mem0Module as any).default?.Memory ||
          (mem0Module as any).default;

        if (!MemoryClass || typeof MemoryClass.from_config !== "function") {
          throw new Error(
            "mem0ai Memory class not found or invalid. The package structure may have changed."
          );
        }

        this.memoryInstance = MemoryClass.from_config(config) as Memory;
      } catch (importError) {
        console.error("❌ Mem0Service: Failed to import mem0ai:", importError);
        throw new Error(
          `Failed to import mem0ai: ${importError instanceof Error ? importError.message : "Unknown error"}`
        );
      }
      this.isInitialized = true;

      console.log(
        "✅ Mem0Service: Successfully initialized with Supabase vector store"
      );
    } catch (error) {
      console.error("❌ Mem0Service: Failed to initialize:", error);
      this.isInitialized = false;
      this.initializationPromise = null;
      throw new Error(
        `Failed to initialize mem0: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  /**
   * Get initialized mem0 instance
   */
  private async getMemoryInstance(): Promise<Memory> {
    if (!this.memoryInstance || !this.isInitialized) {
      await this.initialize();
    }

    if (!this.memoryInstance) {
      throw new Error("Mem0 instance not initialized");
    }

    return this.memoryInstance;
  }

  /**
   * Search for relevant memories for a user
   */
  async searchMemories(
    userId: string,
    options: MemorySearchOptions = {}
  ): Promise<MemoryItem[]> {
    try {
      console.log(
        `🔍 Mem0Service: Searching memories for user ${userId}`,
        options
      );

      // Check rate limits first
      const usage = await canUserUseClerkFeature(userId, "AI_CALLS");
      if (!usage.allowed) {
        console.log(
          `❌ Mem0Service: Memory search rate limited for user ${userId}`
        );
        throw new Error(
          "Memory search rate limit exceeded. Please upgrade your plan or wait for the next billing period."
        );
      }

      const memory = await this.getMemoryInstance();

      const searchParams = {
        query: options.query || "",
        user_id: userId,
        limit: Math.min(options.limit || 5, 10), // Cap at 10 memories
        ...(options.metadata && { filter: options.metadata }),
      };

      const results = await memory.search(searchParams);

      // Log usage for rate limiting
      await logUsage(userId, "AI_CALLS", 1, {
        operation: "memory_search",
        query: options.query,
        resultsCount: results.length,
      });

      console.log(
        `✅ Mem0Service: Found ${results.length} memories for user ${userId}`
      );

      return results.map(this.formatMemoryItem);
    } catch (error) {
      console.error("❌ Mem0Service: Memory search failed:", error);

      // Return empty array on error to allow graceful degradation
      if (error instanceof Error && error.message.includes("rate limit")) {
        throw error; // Re-throw rate limit errors
      }

      console.log(
        "🔄 Mem0Service: Returning empty memories due to error - AI will work without memory"
      );
      return [];
    }
  }

  /**
   * Add memories from conversation context
   */
  async addMemories(
    userId: string,
    messages: Array<{ role: string; content: string }>,
    context: ConversationContext,
    options: MemoryAddOptions = {}
  ): Promise<void> {
    try {
      console.log(`💾 Mem0Service: Adding memories for user ${userId}`, {
        messagesCount: messages.length,
        mentionId: context.mentionId,
        memoryType: options.memoryType,
      });

      // Check rate limits
      const usage = await canUserUseClerkFeature(userId, "AI_CALLS");
      if (!usage.allowed) {
        console.log(
          `❌ Mem0Service: Memory addition rate limited for user ${userId}`
        );
        // Don't throw error - just skip memory storage to allow response generation
        console.log(
          "🔄 Mem0Service: Skipping memory storage due to rate limits"
        );
        return;
      }

      const memory = await this.getMemoryInstance();

      // Prepare metadata with context information
      const metadata = {
        ...options.metadata,
        memoryType: options.memoryType || "conversation",
        mentionId: context.mentionId,
        sessionId: context.sessionId,
        authorHandle: context.authorInfo?.handle,
        monitoredAccount: context.monitoredAccountInfo?.handle,
        timestamp: new Date().toISOString(),
        relevanceScore: options.relevanceScore || 1.0,
      };

      // Add memories with user isolation
      await memory.add(messages, userId, metadata);

      // Log usage for rate limiting
      await logUsage(userId, "AI_CALLS", 1, {
        operation: "memory_add",
        messagesCount: messages.length,
        memoryType: options.memoryType,
        mentionId: context.mentionId,
      });

      console.log(
        `✅ Mem0Service: Successfully added memories for user ${userId}`
      );
    } catch (error) {
      console.error("❌ Mem0Service: Failed to add memories:", error);
      // Don't throw - allow the conversation to continue without memory storage
      console.log("🔄 Mem0Service: Continuing without memory storage");
    }
  }

  /**
   * Get user's memory context for conversation enhancement
   */
  async getUserMemoryContext(
    userId: string,
    query: string,
    limit = 3
  ): Promise<string> {
    try {
      const memories = await this.searchMemories(userId, {
        query,
        limit,
        similarityThreshold: 0.7, // Higher threshold for more relevant memories
      });

      if (memories.length === 0) {
        return "";
      }

      // Format memories into a context string
      const memoryContext = memories
        .map((memory, index) => `Memory ${index + 1}: ${memory.content}`)
        .join("\n");

      console.log(
        `📝 Mem0Service: Generated memory context for user ${userId}:`,
        {
          memoriesCount: memories.length,
          contextLength: memoryContext.length,
        }
      );

      return `\n\nRelevant memories from previous conversations:\n${memoryContext}\n`;
    } catch (error) {
      console.error("❌ Mem0Service: Failed to get memory context:", error);
      return ""; // Return empty string on error
    }
  }

  /**
   * Delete specific memories (for user privacy/GDPR)
   */
  async deleteMemories(userId: string, memoryIds: string[]): Promise<void> {
    try {
      console.log(
        `🗑️ Mem0Service: Deleting memories for user ${userId}`,
        memoryIds
      );

      const memory = await this.getMemoryInstance();

      for (const memoryId of memoryIds) {
        await memory.delete(memoryId);
      }

      console.log(
        `✅ Mem0Service: Successfully deleted ${memoryIds.length} memories for user ${userId}`
      );
    } catch (error) {
      console.error("❌ Mem0Service: Failed to delete memories:", error);
      throw error; // Re-throw for proper error handling in API
    }
  }

  /**
   * Get all memories for a user (for user dashboard/management)
   */
  async getUserMemories(userId: string, limit = 50): Promise<MemoryItem[]> {
    try {
      console.log(`📋 Mem0Service: Getting all memories for user ${userId}`);

      const memory = await this.getMemoryInstance();

      const results = await memory.get_all({
        user_id: userId,
        limit: Math.min(limit, 100), // Cap at 100
      });

      console.log(
        `✅ Mem0Service: Retrieved ${results.length} memories for user ${userId}`
      );

      return results.map(this.formatMemoryItem);
    } catch (error) {
      console.error("❌ Mem0Service: Failed to get user memories:", error);
      return []; // Return empty array on error
    }
  }

  /**
   * Format memory item from mem0 response
   */
  private formatMemoryItem(memory: any): MemoryItem {
    return {
      id: memory.id,
      content: memory.content || memory.text || "",
      metadata: memory.metadata || {},
      similarity: memory.similarity,
      memoryType: memory.metadata?.memoryType || "conversation",
      createdAt: memory.created_at ? new Date(memory.created_at) : undefined,
    };
  }

  /**
   * Health check for mem0 service
   */
  async healthCheck(): Promise<{
    status: "healthy" | "unhealthy";
    error?: string;
  }> {
    try {
      await this.getMemoryInstance();
      return { status: "healthy" };
    } catch (error) {
      console.error("❌ Mem0Service: Health check failed:", error);
      return {
        status: "unhealthy",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
}

// Export singleton instance
export const mem0Service = new Mem0Service();

// Export types and service
export default mem0Service;
