/**
 * AI Provider Configuration for BuddyChip
 *
 * Configures OpenRouter and OpenAI providers for the Benji AI agent
 */

import { openai } from "@ai-sdk/openai";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { prisma } from "./db-utils";
import { getSubscriptionPlan } from "./subscriptions";

// Model configurations
export const modelConfig = {
  gemini25Flash: {
    provider: "openrouter",
    modelId: "google/gemini-2.5-flash",
  },
  gemini25Pro: {
    provider: "openrouter",
    modelId: "google/gemini-2.5-pro",
  },
  openaiO3: {
    provider: "openrouter",
    modelId: "openai/o3",
  },
} as const;

export type ModelName = keyof typeof modelConfig;

// Get model instance by name
export function getModel(modelName: ModelName): any {
  const config = modelConfig[modelName];

  if (config.provider === "openrouter") {
    const provider = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY!,
    });
    return provider(config.modelId);
  } else if (config.provider === "openai") {
    return openai(config.modelId);
  } else {
    throw new Error(`Unknown provider: ${(config as any).provider}`);
  }
}

// Model metadata for UI display
export const modelMetadata = {
  gemini25Flash: {
    name: "Workhorse",
    technicalName: "Gemini 2.5 Flash",
    description: "Fast and efficient for everyday tasks",
    provider: "Google (OpenRouter)",
    costTier: "low",
    speed: "fast",
  },

  gemini25Pro: {
    name: "Smarty",
    technicalName: "Gemini 2.5 Pro",
    description: "Balanced reasoning and performance",
    provider: "Google (OpenRouter)",
    costTier: "medium",
    speed: "medium",
  },

  openaiO3: {
    name: "Big Brain",
    technicalName: "OpenAI o3",
    description: "Maximum reasoning power for complex tasks",
    provider: "OpenAI (OpenRouter)",
    costTier: "high",
    speed: "slow",
  },
} as const;

// Get model for subscription plan
export function getModelByPlan(planName: string): ModelName {
  const plan = getSubscriptionPlan(planName);
  return plan ? plan.defaultModel : "gemini25Flash";
}

// Validate environment variables
export function validateAIEnvironment() {
  const required = ["OPENROUTER_API_KEY", "OPENAI_API_KEY"];

  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
  }

  return true;
}

// Database model functions
export async function getAvailableModels() {
  return await prisma.aIModel.findMany({
    where: { isActive: true },
    orderBy: [{ costTier: "asc" }, { name: "asc" }],
  });
}

export async function getModelByDatabaseId(modelId: string) {
  return await prisma.aIModel.findUnique({
    where: { id: modelId },
  });
}

export async function getModelByName(name: string) {
  return await prisma.aIModel.findUnique({
    where: { name },
  });
}

// Get model instance from database model
export function getModelFromDatabase(dbModel: {
  provider: string;
  modelId: string;
}): any {
  if (dbModel.provider === "openrouter") {
    const provider = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY!,
    });
    return provider(dbModel.modelId);
  } else if (dbModel.provider === "openai") {
    return openai(dbModel.modelId);
  } else {
    throw new Error(`Unknown provider: ${dbModel.provider}`);
  }
}
