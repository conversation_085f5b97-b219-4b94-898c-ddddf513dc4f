/**
 * Seed subscription plans and their features
 * Run with: tsx apps/web/src/scripts/seed-subscription-plans.ts
 */

import { runScript } from "../lib/script-utils.js";

async function seedSubscriptionPlans(prisma: any) {
  console.log("🌱 Seeding subscription plans...");

  // Create subscription plans
  const replyGuyPlan = await prisma.subscriptionPlan.upsert({
    where: { name: "reply-guy" },
    update: {},
    create: {
      name: "reply-guy",
      displayName: "Reply Guy",
      description:
        "Perfect for individuals getting started with social media automation and crypto intelligence",
      price: 29.0,
      baseUsers: 1,
      isActive: true,
    },
  });

  const replyGodPlan = await prisma.subscriptionPlan.upsert({
    where: { name: "reply-god" },
    update: {},
    create: {
      name: "reply-god",
      displayName: "Reply God",
      description:
        "Advanced features for power users and small teams with enhanced crypto market intelligence",
      price: 99.0,
      baseUsers: 1,
      isActive: true,
    },
  });

  const teamPlan = await prisma.subscriptionPlan.upsert({
    where: { name: "team" },
    update: {},
    create: {
      name: "team",
      displayName: "Team",
      description:
        "Enterprise solution for teams and organizations with unlimited crypto market intelligence",
      price: 299.0,
      baseUsers: 5,
      additionalUserPrice: 50.0,
      isActive: true,
    },
  });

  console.log("✅ Created subscription plans");

  // Create plan features for Reply Guy
  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: replyGuyPlan.id, feature: "MENTIONS_PER_SYNC" },
    },
    update: { limit: 25 },
    create: {
      planId: replyGuyPlan.id,
      feature: "MENTIONS_PER_SYNC",
      limit: 25,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: {
        planId: replyGuyPlan.id,
        feature: "MAX_TOTAL_MENTIONS",
      },
    },
    update: { limit: 100 },
    create: {
      planId: replyGuyPlan.id,
      feature: "MAX_TOTAL_MENTIONS",
      limit: 100,
    },
  });

  await prisma.planFeature.upsert({
    where: { planId_feature: { planId: replyGuyPlan.id, feature: "AI_CALLS" } },
    update: { limit: 1000 },
    create: {
      planId: replyGuyPlan.id,
      feature: "AI_CALLS",
      limit: 1000,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: {
        planId: replyGuyPlan.id,
        feature: "MONITORED_ACCOUNTS",
      },
    },
    update: { limit: 3 },
    create: {
      planId: replyGuyPlan.id,
      feature: "MONITORED_ACCOUNTS",
      limit: 3,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: replyGuyPlan.id, feature: "COOKIE_API_CALLS" },
    },
    update: { limit: 50 },
    create: {
      planId: replyGuyPlan.id,
      feature: "COOKIE_API_CALLS",
      limit: 50,
    },
  });

  // Create plan features for Reply God
  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: replyGodPlan.id, feature: "MENTIONS_PER_SYNC" },
    },
    update: { limit: 100 },
    create: {
      planId: replyGodPlan.id,
      feature: "MENTIONS_PER_SYNC",
      limit: 100,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: {
        planId: replyGodPlan.id,
        feature: "MAX_TOTAL_MENTIONS",
      },
    },
    update: { limit: 500 },
    create: {
      planId: replyGodPlan.id,
      feature: "MAX_TOTAL_MENTIONS",
      limit: 500,
    },
  });

  await prisma.planFeature.upsert({
    where: { planId_feature: { planId: replyGodPlan.id, feature: "AI_CALLS" } },
    update: { limit: 5000 },
    create: {
      planId: replyGodPlan.id,
      feature: "AI_CALLS",
      limit: 5000,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: {
        planId: replyGodPlan.id,
        feature: "MONITORED_ACCOUNTS",
      },
    },
    update: { limit: 10 },
    create: {
      planId: replyGodPlan.id,
      feature: "MONITORED_ACCOUNTS",
      limit: 10,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: replyGodPlan.id, feature: "COOKIE_API_CALLS" },
    },
    update: { limit: 200 },
    create: {
      planId: replyGodPlan.id,
      feature: "COOKIE_API_CALLS",
      limit: 200,
    },
  });

  // Create plan features for Team
  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: teamPlan.id, feature: "MENTIONS_PER_SYNC" },
    },
    update: { limit: 200 },
    create: {
      planId: teamPlan.id,
      feature: "MENTIONS_PER_SYNC",
      limit: 200,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: teamPlan.id, feature: "MAX_TOTAL_MENTIONS" },
    },
    update: { limit: 2000 },
    create: {
      planId: teamPlan.id,
      feature: "MAX_TOTAL_MENTIONS",
      limit: 2000,
    },
  });

  await prisma.planFeature.upsert({
    where: { planId_feature: { planId: teamPlan.id, feature: "AI_CALLS" } },
    update: { limit: -1 }, // Unlimited
    create: {
      planId: teamPlan.id,
      feature: "AI_CALLS",
      limit: -1,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: teamPlan.id, feature: "MONITORED_ACCOUNTS" },
    },
    update: { limit: 50 },
    create: {
      planId: teamPlan.id,
      feature: "MONITORED_ACCOUNTS",
      limit: 50,
    },
  });

  await prisma.planFeature.upsert({
    where: {
      planId_feature: { planId: teamPlan.id, feature: "COOKIE_API_CALLS" },
    },
    update: { limit: -1 },
    create: {
      planId: teamPlan.id,
      feature: "COOKIE_API_CALLS",
      limit: -1, // Unlimited
    },
  });

  console.log("✅ Created plan features");

  console.log("\n📊 Subscription Plans Summary:");
  console.log(
    `- Reply Guy: 25 mentions/sync, 100 max total, 3 accounts, 1K AI calls, 50 Cookie API calls`
  );
  console.log(
    `- Reply God: 100 mentions/sync, 500 max total, 10 accounts, 5K AI calls, 200 Cookie API calls`
  );
  console.log(
    `- Team: 200 mentions/sync, 2000 max total, 50 accounts, unlimited AI calls, unlimited Cookie API calls`
  );

  console.log("\n🎉 Subscription plans seeded successfully!");
}

async function main() {
  await runScript("seed-subscription-plans", seedSubscriptionPlans, {
    verbose: true,
  });
}

main().catch((error) => {
  console.error("❌ Script failed:", error);
  process.exit(1);
});
