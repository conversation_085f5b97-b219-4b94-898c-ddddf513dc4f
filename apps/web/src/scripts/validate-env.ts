#!/usr/bin/env tsx

/**
 * Environment Validation Script
 *
 * Validates that all required environment variables are set
 * and provides helpful error messages for missing variables
 */

import {
  env,
  getMissingEnvVars,
  getSafeEnvInfo,
  validateFeatureEnv,
} from "../lib/env-validation";

async function validateEnvironment() {
  console.log("🔧 BuddyChip Environment Validation");
  console.log("=====================================");

  try {
    // Basic environment info
    const envInfo = getSafeEnvInfo();
    console.log("📊 Environment:", envInfo.NODE_ENV);
    console.log("🔗 Features:");
    console.log(
      `  ✅ Database: ${envInfo.hasDatabase ? "Configured" : "❌ Missing"}`
    );
    console.log(
      `  ✅ Authentication: ${envInfo.hasAuth ? "Configured" : "❌ Missing"}`
    );
    console.log(
      `  ✅ AI Providers: ${envInfo.hasAI ? "Configured" : "❌ Missing"}`
    );
    console.log(
      `  ✅ Twitter API: ${envInfo.hasTwitter ? "Configured" : "❌ Missing"}`
    );
    console.log(
      `  ✅ Monitoring: ${envInfo.hasMonitoring ? "Configured" : "⚠️ Optional"}`
    );

    console.log("\n🔍 Detailed Feature Validation:");

    // Validate each feature
    const features = [
      "database",
      "auth",
      "ai",
      "twitter",
      "monitoring",
    ] as const;
    let hasErrors = false;

    for (const feature of features) {
      const isValid = validateFeatureEnv(feature);
      const missing = getMissingEnvVars(feature);

      if (!isValid && missing.length > 0) {
        console.log(
          `❌ ${feature.toUpperCase()}: Missing variables: ${missing.join(", ")}`
        );
        hasErrors = true;
      } else if (isValid) {
        console.log(`✅ ${feature.toUpperCase()}: All variables configured`);
      } else {
        console.log(
          `⚠️ ${feature.toUpperCase()}: Optional or partially configured`
        );
      }
    }

    // Development flags
    console.log("\n🐛 Development Flags:");
    console.log(
      `  Verbose Logging: ${envInfo.verboseLogging ? "Enabled" : "Disabled"}`
    );
    console.log(
      `  Prisma Query Logs: ${envInfo.features.prismaLogs ? "Enabled" : "Disabled"}`
    );
    console.log(
      `  Context Logs: ${envInfo.features.contextLogs ? "Enabled" : "Disabled"}`
    );
    console.log(
      `  tRPC Request Logs: ${envInfo.features.trpcLogs ? "Enabled" : "Disabled"}`
    );

    // Security warnings
    console.log("\n🛡️ Security Checks:");

    if (env.NODE_ENV === "production") {
      if (env.VERBOSE_LOGGING) {
        console.log(
          "⚠️ Verbose logging is enabled in production - consider disabling"
        );
      }

      if (env.ENABLE_PRISMA_QUERY_LOGS) {
        console.log(
          "⚠️ Prisma query logs are enabled in production - consider disabling"
        );
      }

      if (!env.SENTRY_AUTH_TOKEN) {
        console.log(
          "⚠️ No Sentry token found - error monitoring will be limited"
        );
      }
    }

    // Database URL checks
    if (
      env.NODE_ENV !== "production" &&
      !env.DATABASE_URL.includes("localhost") &&
      !env.DATABASE_URL.includes("supabase")
    ) {
      console.log("⚠️ Using remote database in non-production environment");
    }

    if (env.NODE_ENV === "test" && !env.DATABASE_URL.includes("test")) {
      console.log("⚠️ Test environment using non-test database");
    }

    console.log("\n📋 Summary:");
    if (hasErrors) {
      console.log(
        "❌ Environment validation failed - fix missing variables before starting"
      );
      console.log(
        "\n💡 Tip: Check your .env file and ensure all required variables are set"
      );
      process.exit(1);
    } else {
      console.log(
        "✅ Environment validation passed - all required variables are configured"
      );
      console.log("🚀 Ready to start BuddyChip!");
    }
  } catch (error) {
    console.error("❌ Environment validation failed:", error);
    process.exit(1);
  }
}

// Run validation
if (require.main === module) {
  validateEnvironment();
}
