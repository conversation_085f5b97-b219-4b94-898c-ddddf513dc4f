/**
 * <PERSON><PERSON><PERSON> to fix monitored accounts data consistency issues
 *
 * This script:
 * 1. Identifies orphaned or inconsistent monitored account records
 * 2. Fixes data inconsistencies between count checks and UI queries
 * 3. Ensures proper cleanup of invalid records
 */

import { prisma } from "../lib/db-utils";

async function fixMonitoredAccountsConsistency() {
  console.log("🔍 Starting monitored accounts consistency check...");

  try {
    // 1. Find all users with monitored accounts
    const usersWithAccounts = await prisma.user.findMany({
      include: {
        monitoredAccounts: true,
        plan: {
          include: {
            features: {
              where: {
                feature: "MONITORED_ACCOUNTS",
              },
            },
          },
        },
      },
    });

    console.log(
      `📊 Found ${usersWithAccounts.length} users with potential monitored accounts`
    );

    for (const user of usersWithAccounts) {
      const userId = user.id;
      const userEmail = user.email || "No email";

      console.log(`\n👤 Checking user: ${userEmail} (${userId})`);

      // Get all accounts for this user
      const allAccounts = user.monitoredAccounts;
      const activeAccounts = allAccounts.filter((acc) => acc.isActive);
      const inactiveAccounts = allAccounts.filter((acc) => !acc.isActive);

      console.log(`  📈 Total accounts: ${allAccounts.length}`);
      console.log(`  ✅ Active accounts: ${activeAccounts.length}`);
      console.log(`  ❌ Inactive accounts: ${inactiveAccounts.length}`);

      // Get user's plan limit
      const planLimit = user.plan.features[0]?.limit || 1;
      console.log(
        `  📋 Plan limit: ${planLimit === -1 ? "Unlimited" : planLimit}`
      );

      // Check for issues
      let hasIssues = false;

      // Issue 1: Check for duplicate handles
      const handleCounts = new Map<string, number>();
      allAccounts.forEach((acc) => {
        const handle = acc.twitterHandle.toLowerCase();
        handleCounts.set(handle, (handleCounts.get(handle) || 0) + 1);
      });

      const duplicates = Array.from(handleCounts.entries()).filter(
        ([handle, count]) => count > 1
      );
      if (duplicates.length > 0) {
        hasIssues = true;
        console.log(
          `  ⚠️  Found duplicate handles: ${duplicates.map(([handle, count]) => `@${handle} (${count}x)`).join(", ")}`
        );

        // Keep only the most recent account for each duplicate handle
        for (const [handle] of duplicates) {
          const accountsForHandle = allAccounts
            .filter((acc) => acc.twitterHandle.toLowerCase() === handle)
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

          const toKeep = accountsForHandle[0];
          const toDelete = accountsForHandle.slice(1);

          console.log(
            `    🗑️  Deleting ${toDelete.length} duplicate accounts for @${handle}, keeping ${toKeep.id}`
          );

          for (const account of toDelete) {
            await prisma.monitoredAccount.delete({
              where: { id: account.id },
            });
          }
        }
      }

      // Issue 2: Check for accounts with invalid Twitter handles
      const invalidAccounts = allAccounts.filter(
        (acc) =>
          !acc.twitterHandle ||
          acc.twitterHandle.trim() === "" ||
          acc.twitterHandle.includes("@") ||
          !/^[a-zA-Z0-9_]{1,15}$/.test(acc.twitterHandle)
      );

      if (invalidAccounts.length > 0) {
        hasIssues = true;
        console.log(
          `  ⚠️  Found ${invalidAccounts.length} accounts with invalid handles`
        );

        for (const account of invalidAccounts) {
          console.log(
            `    🗑️  Deleting account with invalid handle: "${account.twitterHandle}" (ID: ${account.id})`
          );
          await prisma.monitoredAccount.delete({
            where: { id: account.id },
          });
        }
      }

      // Issue 3: Check for orphaned accounts (user doesn't exist)
      // This is already handled by foreign key constraints, but let's verify

      // Issue 4: Check if active accounts exceed plan limit
      const currentActiveCount = await prisma.monitoredAccount.count({
        where: {
          userId: userId,
          isActive: true,
        },
      });

      if (planLimit !== -1 && currentActiveCount > planLimit) {
        hasIssues = true;
        console.log(
          `  ⚠️  User has ${currentActiveCount} active accounts but plan limit is ${planLimit}`
        );

        // Deactivate the oldest accounts that exceed the limit
        const excessAccounts = await prisma.monitoredAccount.findMany({
          where: {
            userId: userId,
            isActive: true,
          },
          orderBy: {
            createdAt: "asc",
          },
          take: currentActiveCount - planLimit,
        });

        for (const account of excessAccounts) {
          console.log(
            `    ⏸️  Deactivating excess account: @${account.twitterHandle} (ID: ${account.id})`
          );
          await prisma.monitoredAccount.update({
            where: { id: account.id },
            data: { isActive: false },
          });
        }
      }

      if (!hasIssues) {
        console.log(`  ✅ No issues found for user ${userEmail}`);
      }
    }

    // 5. Final consistency check
    console.log("\n🔍 Running final consistency check...");

    const consistencyIssues = (await prisma.$queryRaw`
      SELECT 
        u.id as user_id,
        u.email,
        COUNT(ma.id) as ui_query_count,
        COUNT(CASE WHEN ma.is_active = true THEN 1 END) as count_check_count,
        pf.limit as plan_limit
      FROM users u
      LEFT JOIN monitored_accounts ma ON u.id = ma.user_id
      LEFT JOIN subscription_plans sp ON u.plan_id = sp.id
      LEFT JOIN plan_features pf ON sp.id = pf.plan_id AND pf.feature = 'MONITORED_ACCOUNTS'
      GROUP BY u.id, u.email, pf.limit
      HAVING COUNT(ma.id) != COUNT(CASE WHEN ma.is_active = true THEN 1 END)
         OR (pf.limit != -1 AND COUNT(CASE WHEN ma.is_active = true THEN 1 END) > pf.limit)
    `) as Array<{
      user_id: string;
      email: string;
      ui_query_count: bigint;
      count_check_count: bigint;
      plan_limit: number;
    }>;

    if (consistencyIssues.length > 0) {
      console.log("⚠️ Remaining consistency issues:");
      consistencyIssues.forEach((issue) => {
        console.log(
          `  ${issue.email}: UI=${Number(issue.ui_query_count)}, Count=${Number(issue.count_check_count)}, Limit=${issue.plan_limit}`
        );
      });
    } else {
      console.log("✅ All consistency issues resolved!");
    }

    console.log("\n🎉 Monitored accounts consistency check completed!");
  } catch (error) {
    console.error("❌ Error during consistency fix:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  fixMonitoredAccountsConsistency().catch(console.error);
}

export { fixMonitoredAccountsConsistency };
