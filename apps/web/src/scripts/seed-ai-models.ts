/**
 * Seed AI Models
 *
 * Seeds the database with the three AI models: Workhorse, Smarty, and Big Brain
 */

import { runScript } from "../lib/script-utils.js";

const AI_MODELS = [
  {
    name: "Workhorse",
    displayName: "Workhorse",
    description:
      "Fast and efficient for everyday tasks. Great for quick responses and high-volume usage.",
    provider: "openrouter",
    modelId: "google/gemini-2.5-flash",
    costTier: "low",
    speed: "fast",
    isActive: true,
  },
  {
    name: "Smarty",
    displayName: "Smarty",
    description:
      "Balanced reasoning and performance. Perfect for complex conversations and nuanced responses.",
    provider: "openrouter",
    modelId: "google/gemini-2.5-pro",
    costTier: "medium",
    speed: "medium",
    isActive: true,
  },
  {
    name: "Big Brain",
    displayName: "Big Brain",
    description:
      "Maximum reasoning power for the most complex tasks. Slowest but most capable model.",
    provider: "openai",
    modelId: "o3",
    costTier: "high",
    speed: "slow",
    isActive: true,
  },
];

async function seedAIModels(prisma: any) {
  console.log("🧠 Seeding AI models...");

  // Create or update each model
  for (const modelData of AI_MODELS) {
    const model = await prisma.aIModel.upsert({
      where: { name: modelData.name },
      update: {
        displayName: modelData.displayName,
        description: modelData.description,
        provider: modelData.provider,
        modelId: modelData.modelId,
        costTier: modelData.costTier,
        speed: modelData.speed,
        isActive: modelData.isActive,
      },
      create: modelData,
    });

    console.log(
      `✅ Created/Updated AI model: ${model.name} (${model.modelId})`
    );
  }

  console.log("🎉 AI models seeded successfully!");
}

async function main() {
  await runScript("seed-ai-models", seedAIModels, {
    verbose: true,
  });
}

// Only run if this file is executed directly
if (require.main === module) {
  main().catch((error) => {
    console.error("❌ Script failed:", error);
    process.exit(1);
  });
}

export { seedAIModels };
