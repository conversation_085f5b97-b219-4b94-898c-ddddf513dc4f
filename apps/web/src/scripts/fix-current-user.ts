import { createClerkClient } from "@clerk/backend";
import { prisma } from "../lib/db-utils";

async function fixCurrentUser() {
  console.log("🔄 Fixing current user...");

  const secretKey = process.env.CLERK_SECRET_KEY;
  if (!secretKey) {
    throw new Error("CLERK_SECRET_KEY is not set");
  }

  const clerkClient = createClerkClient({ secretKey });
  const userId = "user_2y2xJLvi2ncFa8bEKWJmb94DPTZ";

  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (existingUser) {
      console.log("✅ User already exists in database");
      return;
    }

    // Get user from Clerk
    const clerkUser = await clerkClient.users.getUser(userId);
    console.log("📊 Found user in Clerk:", clerkUser.id);

    // Get default plan
    const defaultPlan = await prisma.subscriptionPlan.findFirst({
      where: { name: "reply-guy" },
    });

    if (!defaultPlan) {
      throw new Error("Default plan not found");
    }

    // Get primary email
    const primaryEmail = clerkUser.emailAddresses.find(
      (email) => email.id === clerkUser.primaryEmailAddressId
    );

    // Create user with modified email to avoid conflict
    const emailToUse = primaryEmail?.emailAddress
      ? `${primaryEmail.emailAddress.split("@")[0]}_${userId.slice(-6)}@${primaryEmail.emailAddress.split("@")[1]}`
      : null;

    await prisma.user.create({
      data: {
        id: userId,
        email: emailToUse,
        name:
          clerkUser.firstName && clerkUser.lastName
            ? `${clerkUser.firstName} ${clerkUser.lastName}`
            : clerkUser.firstName || clerkUser.username || "User",
        avatar: clerkUser.imageUrl || null,
        planId: defaultPlan.id,
        lastActiveAt: new Date(),
      },
    });

    console.log(`✅ Created user: ${userId} with email: ${emailToUse}`);
  } catch (error) {
    console.error("❌ Error:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

fixCurrentUser().catch(console.error);
