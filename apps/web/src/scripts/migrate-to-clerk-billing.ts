/**
 * Migration Script: Legacy Plans to Clerk Billing
 *
 * This script migrates existing users from the legacy subscription system
 * to Clerk billing system.
 *
 * Run with: tsx apps/web/src/scripts/migrate-to-clerk-billing.ts
 */

import { runScript } from "../lib/script-utils.js";

interface MigrationStats {
  totalUsers: number;
  migratedUsers: number;
  skippedUsers: number;
  errors: number;
}

async function migrateToClerkBilling(prisma: any) {
  console.log("🚀 Starting migration to Clerk billing...");

  const stats: MigrationStats = {
    totalUsers: 0,
    migratedUsers: 0,
    skippedUsers: 0,
    errors: 0,
  };

  try {
    // Get all users with their current plans
    const users = await prisma.user.findMany({
      include: {
        plan: true,
      },
    });

    stats.totalUsers = users.length;
    console.log(`📊 Found ${stats.totalUsers} users to migrate`);

    for (const user of users) {
      try {
        // Skip users who already have Clerk billing
        if (user.clerkPlanName && user.subscriptionStatus === "active") {
          console.log(`⏭️ Skipping user ${user.id} - already has Clerk billing`);
          stats.skippedUsers++;
          continue;
        }

        // Map legacy plan names to Clerk plan names
        const clerkPlanName = mapLegacyPlanToClerk(user.plan.name);

        if (!clerkPlanName) {
          console.log(
            `⚠️ Unknown legacy plan for user ${user.id}: ${user.plan.name}`
          );
          stats.skippedUsers++;
          continue;
        }

        // Update user with Clerk billing information
        await prisma.user.update({
          where: { id: user.id },
          data: {
            clerkPlanName: clerkPlanName,
            subscriptionStatus: "migrated", // Special status for migrated users
            subscriptionUpdatedAt: new Date(),
          },
        });

        console.log(
          `✅ Migrated user ${user.id} from ${user.plan.name} to ${clerkPlanName}`
        );
        stats.migratedUsers++;
      } catch (error) {
        console.error(`❌ Error migrating user ${user.id}:`, error);
        stats.errors++;
      }
    }

    // Print migration summary
    console.log("\n📈 Migration Summary:");
    console.log(`Total users: ${stats.totalUsers}`);
    console.log(`Successfully migrated: ${stats.migratedUsers}`);
    console.log(`Skipped (already migrated): ${stats.skippedUsers}`);
    console.log(`Errors: ${stats.errors}`);

    if (stats.errors === 0) {
      console.log("🎉 Migration completed successfully!");
    } else {
      console.log(
        "⚠️ Migration completed with some errors. Please review the logs."
      );
    }
  } catch (error) {
    console.error("💥 Migration failed:", error);
    process.exit(1);
  }
}

/**
 * Map legacy plan names to Clerk plan names
 */
function mapLegacyPlanToClerk(legacyPlanName: string): string | null {
  const planMapping: Record<string, string> = {
    "reply-guy": "reply-guy",
    "reply-god": "reply-god",
    team: "team-plan",
    "team-plan": "team-plan",
  };

  return planMapping[legacyPlanName] || null;
}

/**
 * Rollback migration (if needed)
 */
async function rollbackMigration(prisma: any) {
  console.log("🔄 Rolling back Clerk billing migration...");

  const result = await prisma.user.updateMany({
    where: {
      subscriptionStatus: "migrated",
    },
    data: {
      clerkPlanName: null,
      subscriptionStatus: null,
      subscriptionUpdatedAt: null,
    },
  });

  console.log(`✅ Rolled back ${result.count} users`);
  console.log("🎉 Rollback completed successfully!");
}

/**
 * Verify migration results
 */
async function verifyMigration(prisma: any) {
  console.log("🔍 Verifying migration results...");

  try {
    const totalUsers = await prisma.user.count();
    const clerkBillingUsers = await prisma.user.count({
      where: {
        clerkPlanName: { not: null },
      },
    });
    const migratedUsers = await prisma.user.count({
      where: {
        subscriptionStatus: "migrated",
      },
    });

    console.log(`📊 Verification Results:`);
    console.log(`Total users: ${totalUsers}`);
    console.log(`Users with Clerk billing: ${clerkBillingUsers}`);
    console.log(`Users with 'migrated' status: ${migratedUsers}`);

    // Show plan distribution
    const planDistribution = await prisma.user.groupBy({
      by: ["clerkPlanName"],
      _count: {
        clerkPlanName: true,
      },
      where: {
        clerkPlanName: { not: null },
      },
    });

    console.log("\n📈 Plan Distribution:");
    planDistribution.forEach((group: any) => {
      console.log(
        `${group.clerkPlanName}: ${group._count.clerkPlanName} users`
      );
    });
  } catch (error) {
    console.error("💥 Verification failed:", error);
    process.exit(1);
  }
}

async function main() {
  const command = process.argv[2];

  switch (command) {
    case "migrate":
      await runScript("migrate-to-clerk-billing", migrateToClerkBilling, { verbose: true });
      break;
    case "rollback":
      await runScript("rollback-clerk-billing", rollbackMigration, { verbose: true });
      break;
    case "verify":
      await runScript("verify-clerk-billing", verifyMigration, { verbose: true });
      break;
    default:
      console.log("Usage:");
      console.log(
        "  tsx migrate-to-clerk-billing.ts migrate   - Migrate users to Clerk billing"
      );
      console.log(
        "  tsx migrate-to-clerk-billing.ts rollback  - Rollback migration"
      );
      console.log(
        "  tsx migrate-to-clerk-billing.ts verify    - Verify migration results"
      );
      process.exit(1);
  }
}

main().catch((error) => {
  console.error("💥 Script failed:", error);
  process.exit(1);
});
