"use client";

import {
  ArrowLeft,
  Clock,
  Code,
  Coffee,
  Heart,
  Rocket,
  Sparkles,
  Star,
  Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import Logo from "@/components/logo";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export default function ComingSoonPage() {
  const [clickCount, setClickCount] = useState(0);
  const [showHearts, setShowHearts] = useState(false);
  const [logoAnimation, setLogoAnimation] = useState("bounce");

  const animations = [
    "bounce",
    "pulse",
    "spin",
    "wiggle",
    "shake",
    "heartbeat",
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      const randomAnimation =
        animations[Math.floor(Math.random() * animations.length)];
      setLogoAnimation(randomAnimation);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const handleLogoClick = () => {
    setClickCount((prev) => prev + 1);
    setLogoAnimation("heartbeat");

    if (clickCount >= 4) {
      setShowHearts(true);
      toast.success("🥰 You found the easter egg! The logo loves you too!");
      setTimeout(() => setShowHearts(false), 3000);
      setClickCount(0);
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      handleLogoClick();
    }
  };

  const getAnimationClass = (animation: string) => {
    switch (animation) {
      case "bounce":
        return "animate-bounce";
      case "pulse":
        return "animate-pulse";
      case "spin":
        return "animate-spin";
      case "wiggle":
        return "animate-wiggle";
      case "shake":
        return "animate-shake";
      case "heartbeat":
        return "animate-heartbeat";
      default:
        return "animate-bounce";
    }
  };

  return (
    <div className="min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline">
      {/* Flying Hearts when logo is clicked multiple times */}
      {showHearts && (
        <div className="absolute inset-0 pointer-events-none z-50">
          {[...Array(8)].map((_, i) => (
            <Heart
              key={i}
              className={`absolute w-6 h-6 text-pink-500 animate-heart-float-${i % 4}`}
              style={{
                left: `${20 + i * 10}%`,
                top: `${30 + i * 5}%`,
                animationDelay: `${i * 0.2}s`,
              }}
            />
          ))}
        </div>
      )}

      <div className="max-w-4xl mx-auto">
        {/* Main Content Card */}
        <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
          <CardContent className="p-6 md:p-12 text-center">
            {/* Animated Logo Section */}
            <div className="mb-8">
              <div
                role="button"
                tabIndex={0}
                aria-label="Click to activate logo animation easter egg"
                className={`inline-block cursor-pointer transition-all duration-300 hover:scale-110 ${getAnimationClass(logoAnimation)}`}
                onClick={handleLogoClick}
                onKeyDown={handleKeyDown}
                style={{
                  animationDuration: logoAnimation === "spin" ? "2s" : "1s",
                }}
              >
                <Logo size={120} href="" showText={false} />
              </div>
              <div className="mt-4">
                <Badge className="bg-app-main/10 text-app-main border-app-main/20 px-4 py-2">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Click the logo for a surprise!
                </Badge>
              </div>
            </div>

            {/* Title Section */}
            <div className="mb-8">
              <h1 className="text-4xl md:text-6xl font-bold text-app-headline mb-4 tracking-wide">
                <span className="bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 bg-clip-text text-transparent">
                  COMING SOON
                </span>
              </h1>
              <p className="text-xl md:text-2xl text-app-headline/80 max-w-2xl mx-auto leading-relaxed">
                We're cooking up something{" "}
                <span className="font-bold text-app-main">amazing</span> for
                you! This feature is in our secret lab right now.
              </p>
            </div>

            {/* Status Indicators */}
            <div className="mb-8 flex flex-wrap justify-center gap-4">
              <div className="flex items-center space-x-2 px-4 py-2 bg-orange-50 rounded-full border border-orange-200">
                <Code className="w-4 h-4 text-orange-600" />
                <span className="text-orange-800 text-sm font-medium">
                  Coding in Progress
                </span>
              </div>
              <div className="flex items-center space-x-2 px-4 py-2 bg-blue-50 rounded-full border border-blue-200">
                <Clock className="w-4 h-4 text-blue-600" />
                <span className="text-blue-800 text-sm font-medium">
                  Almost Ready
                </span>
              </div>
              <div className="flex items-center space-x-2 px-4 py-2 bg-green-50 rounded-full border border-green-200">
                <Coffee className="w-4 h-4 text-green-600" />
                <span className="text-green-800 text-sm font-medium">
                  Fueled by Coffee
                </span>
              </div>
            </div>

            {/* What to Expect */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-app-headline mb-6">
                What to Expect
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200 group hover:scale-105 transition-transform">
                  <Rocket className="w-8 h-8 text-purple-600 mx-auto mb-3 group-hover:animate-bounce" />
                  <h4 className="font-semibold text-purple-800 mb-2">
                    Powerful Features
                  </h4>
                  <p className="text-purple-700 text-sm">
                    Revolutionary tools that will change how you work
                  </p>
                </div>
                <div className="p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200 group hover:scale-105 transition-transform">
                  <Zap className="w-8 h-8 text-blue-600 mx-auto mb-3 group-hover:animate-pulse" />
                  <h4 className="font-semibold text-blue-800 mb-2">
                    Lightning Fast
                  </h4>
                  <p className="text-blue-700 text-sm">
                    Optimized for speed and performance
                  </p>
                </div>
                <div className="p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200 group hover:scale-105 transition-transform">
                  <Star className="w-8 h-8 text-green-600 mx-auto mb-3 group-hover:animate-spin" />
                  <h4 className="font-semibold text-green-800 mb-2">
                    Delightful UX
                  </h4>
                  <p className="text-green-700 text-sm">
                    Beautiful and intuitive user experience
                  </p>
                </div>
              </div>
            </div>

            {/* Back to Dashboard */}
            <div className="text-center pt-6 border-t border-app-stroke">
              <p className="text-app-headline/60 text-sm mb-4">
                Ready to explore what's already available?
              </p>
              <div className="flex flex-wrap justify-center gap-3">
                <Button
                  variant="outline"
                  className="border-app-stroke text-app-headline hover:bg-app-background"
                  onClick={() => window.location.assign("/dashboard")}
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Dashboard
                </Button>
                <Button
                  variant="outline"
                  className="border-app-stroke text-app-headline hover:bg-app-background"
                  onClick={() => window.location.assign("/reply-guy")}
                >
                  Reply Guy
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <style jsx>{`
        @keyframes wiggle {
          0%, 100% { transform: rotate(0deg); }
          25% { transform: rotate(-10deg); }
          75% { transform: rotate(10deg); }
        }
        @keyframes shake {
          0%, 100% { transform: translateX(0); }
          25% { transform: translateX(-5px); }
          75% { transform: translateX(5px); }
        }
        @keyframes heartbeat {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.1); }
        }
        @keyframes heart-float-0 {
          0% { transform: translateY(0) rotate(0deg); opacity: 1; }
          100% { transform: translateY(-100px) rotate(180deg); opacity: 0; }
        }
        @keyframes heart-float-1 {
          0% { transform: translateY(0) rotate(0deg); opacity: 1; }
          100% { transform: translateY(-120px) rotate(-180deg); opacity: 0; }
        }
        @keyframes heart-float-2 {
          0% { transform: translateY(0) rotate(0deg); opacity: 1; }
          100% { transform: translateY(-90px) rotate(90deg); opacity: 0; }
        }
        @keyframes heart-float-3 {
          0% { transform: translateY(0) rotate(0deg); opacity: 1; }
          100% { transform: translateY(-110px) rotate(-90deg); opacity: 0; }
        }
        .animate-wiggle { animation: wiggle 1s ease-in-out; }
        .animate-shake { animation: shake 0.5s ease-in-out; }
        .animate-heartbeat { animation: heartbeat 1s ease-in-out; }
        .animate-heart-float-0 { animation: heart-float-0 2s ease-out forwards; }
        .animate-heart-float-1 { animation: heart-float-1 2s ease-out forwards; }
        .animate-heart-float-2 { animation: heart-float-2 2s ease-out forwards; }
        .animate-heart-float-3 { animation: heart-float-3 2s ease-out forwards; }
      `}</style>
    </div>
  );
}
