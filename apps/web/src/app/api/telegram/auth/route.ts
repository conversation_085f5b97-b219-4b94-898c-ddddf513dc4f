/**
 * Telegram Authentication API
 *
 * Handles linking and unlinking Telegram accounts with BuddyChip users
 */

import { auth } from "@clerk/nextjs/server";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import {
  getLinkedTelegramAccount,
  linkTelegramAccount,
  unlinkTelegramAccount,
} from "@/lib/telegram-auth";

// Validation schemas
const linkAccountSchema = z.object({
  linkCode: z.string().min(1, "Link code is required"),
});

const unlinkAccountSchema = z.object({
  confirm: z.boolean().refine((val) => val === true, "Confirmation required"),
});

/**
 * Link Telegram account to BuddyChip user
 */
export async function POST(req: NextRequest) {
  try {
    console.log("🔗 Telegram Auth API: Link account request received");

    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = linkAccountSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { linkCode } = validation.data;

    console.log(
      "🔗 Telegram Auth API: Attempting to link account for user:",
      userId
    );

    // Attempt to link the account
    const result = await linkTelegramAccount(userId, linkCode);

    if (result.success) {
      console.log("✅ Telegram Auth API: Account linked successfully");
      return NextResponse.json({
        success: true,
        message: result.message,
        telegramUser: result.telegramUser,
      });
    } else {
      console.log("❌ Telegram Auth API: Link failed:", result.message);
      return NextResponse.json(
        {
          success: false,
          error: result.message,
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("❌ Telegram Auth API: Error linking account:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "An unexpected error occurred while linking your account",
      },
      { status: 500 }
    );
  }
}

/**
 * Unlink Telegram account from BuddyChip user
 */
export async function DELETE(req: NextRequest) {
  try {
    console.log("🔓 Telegram Auth API: Unlink account request received");

    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = unlinkAccountSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validation.error.errors,
        },
        { status: 400 }
      );
    }

    console.log(
      "🔓 Telegram Auth API: Attempting to unlink account for user:",
      userId
    );

    // Attempt to unlink the account
    const result = await unlinkTelegramAccount(userId);

    if (result.success) {
      console.log("✅ Telegram Auth API: Account unlinked successfully");
      return NextResponse.json({
        success: true,
        message: result.message,
      });
    } else {
      console.log("❌ Telegram Auth API: Unlink failed:", result.message);
      return NextResponse.json(
        {
          success: false,
          error: result.message,
        },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("❌ Telegram Auth API: Error unlinking account:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "An unexpected error occurred while unlinking your account",
      },
      { status: 500 }
    );
  }
}

/**
 * Get linked Telegram account information
 */
export async function GET(req: NextRequest) {
  try {
    console.log("📋 Telegram Auth API: Get linked account request received");

    // Check authentication
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log(
      "📋 Telegram Auth API: Getting linked account for user:",
      userId
    );

    // Get linked account information
    const telegramUser = await getLinkedTelegramAccount(userId);

    if (telegramUser) {
      console.log("✅ Telegram Auth API: Found linked account");
      return NextResponse.json({
        linked: true,
        telegramUser: {
          id: telegramUser.id,
          username: telegramUser.username,
          firstName: telegramUser.firstName,
          lastName: telegramUser.lastName,
          isActive: telegramUser.isActive,
          lastActiveAt: telegramUser.lastActiveAt,
          createdAt: telegramUser.createdAt,
        },
      });
    } else {
      console.log("📋 Telegram Auth API: No linked account found");
      return NextResponse.json({
        linked: false,
        telegramUser: null,
      });
    }
  } catch (error) {
    console.error("❌ Telegram Auth API: Error getting linked account:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        message:
          "An unexpected error occurred while retrieving account information",
      },
      { status: 500 }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin":
        process.env.NEXT_PUBLIC_APP_URL || "https://buddychip.app",
      "Access-Control-Allow-Methods": "GET, POST, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
