import { auth } from "@clerk/nextjs/server";
import { type NextRequest, NextResponse } from "next/server";

// Force dynamic rendering for auth compatibility
export const dynamic = "force-dynamic";

export async function GET(req: NextRequest) {
  console.log("🧪 Test Auth: Request received");
  console.log("🍪 Test Auth: Cookies:", req.headers.get("cookie"));
  console.log("🌐 Test Auth: Origin:", req.headers.get("origin"));

  try {
    const authResult = await auth();
    console.log("👤 Test Auth: Auth result:", authResult);

    return NextResponse.json({
      success: true,
      userId: authResult.userId,
      sessionId: authResult.sessionId,
      cookies: req.headers.get("cookie"),
      origin: req.headers.get("origin"),
    });
  } catch (error) {
    console.error("❌ Test Auth: Error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      cookies: req.headers.get("cookie"),
      origin: req.headers.get("origin"),
    });
  }
}
