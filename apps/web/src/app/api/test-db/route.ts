import { NextResponse } from "next/server";
import { prisma, testDatabaseConnection } from "../../../lib/db-utils";

/**
 * Test database connectivity endpoint
 * GET /api/test-db
 */
export async function GET() {
  try {
    console.log("🔍 Testing database connection...");

    // Test basic connection
    const isConnected = await testDatabaseConnection(3);

    if (!isConnected) {
      return NextResponse.json(
        {
          success: false,
          error: "Database connection failed",
          message:
            "Could not establish connection to the database. This may be due to a paused Supabase instance.",
          timestamp: new Date().toISOString(),
        },
        { status: 503 }
      );
    }

    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 
      version() as db_version,
      current_database() as db_name,
      current_user as db_user,
      inet_server_addr() as server_ip,
      inet_server_port() as server_port`;

    // Test subscription plans table (should exist)
    const planCount = await prisma.subscriptionPlan.count();

    return NextResponse.json({
      success: true,
      message: "Database connection successful",
      details: {
        connected: true,
        dbInfo: result,
        planCount,
        ssl: process.env.DATABASE_URL?.includes("sslmode=require") || false,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error: unknown) {
    console.error("❌ Database test failed:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.name : "DatabaseError",
        message:
          error instanceof Error ? error.message : "Unknown database error",
        code:
          error instanceof Error && "code" in error
            ? (error as Error & { code?: string }).code
            : undefined,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
