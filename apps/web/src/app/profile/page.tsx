"use client";

import { User<PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useUser } from "@clerk/nextjs";
import {
  <PERSON><PERSON>,
  CheckCircle,
  ChevronLeft,
  CreditCard,
  Globe,
  Link2,
  Mail,
  Shield,
  Sparkles,
  Twitter,
  UploadCloud,
  User,
} from "lucide-react";
import Link from "next/link";
import { redirect, useRouter } from "next/navigation";
import { useState } from "react";
import AuthenticatedNavbar from "@/components/authenticated-navbar";

// Type definition for billing usage item
interface BillingUsageItem {
  feature: string;
  currentUsage: number;
  limit: number;
}

import PricingSection from "@/components/pricing-section";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import ModelSelector from "@/components/ui/model-selector";
import PersonalitySelector from "@/components/ui/personality-selector";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation";
import TelegramIntegration from "@/components/ui/telegram-integration";
import { trpc } from "@/utils/trpc";

type ProfileSection = "profile" | "security" | "billing";

export default function UserProfilePage() {
  const { user, isLoaded } = useUser();
  const { signOut } = useClerk();
  const {
    data: userProfile,
    isLoading: profileLoading,
    refetch,
  } = trpc.user.getProfile.useQuery();
  const { data: usage } = trpc.user.getUsage.useQuery();
  const { data: subscription } = trpc.billing.getSubscription.useQuery();
  const { data: billingUsage } = trpc.billing.getUsage.useQuery();
  const updateProfile = trpc.user.updateProfile.useMutation();
  const router = useRouter();

  const [activeSection, setActiveSection] = useState<ProfileSection>("profile");
  const [username, setUsername] = useState("");
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [personalityDialogOpen, setPersonalityDialogOpen] = useState(false);
  const [modelDialogOpen, setModelDialogOpen] = useState(false);

  if (!isLoaded || profileLoading) {
    return (
      <div className="min-h-screen bg-app-background flex items-center justify-center">
        <ShardLoadingAnimation size={80} />
      </div>
    );
  }

  if (!user) {
    redirect("/sign-in");
  }

  const handleUpdateUsername = async () => {
    try {
      await updateProfile.mutateAsync({ name: username });
      await refetch();
      setIsEditingUsername(false);
    } catch (error) {
      console.error("Failed to update username:", error);
    }
  };

  const renderSectionContent = () => {
    switch (activeSection) {
      case "profile":
        return <ProfileSectionContent />;
      case "security":
        return <SecuritySectionContent />;
      case "billing":
        return <PricingSection />;
      default:
        return null;
    }
  };

  const ProfileSectionContent = () => (
    <div className="space-y-6">
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">Profile</CardTitle>
          <CardDescription className="text-app-headline/70">
            This is how others will see you on the site.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Avatar className="h-20 w-20 border-2 border-app-main">
              <AvatarImage
                src={user.imageUrl}
                alt={user.firstName || "Profile"}
              />
              <AvatarFallback className="bg-app-main text-app-secondary text-2xl">
                {user.firstName?.charAt(0) ||
                  user.emailAddresses[0]?.emailAddress.charAt(0) ||
                  "U"}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <h3 className="text-xl font-semibold text-app-headline">
                {userProfile?.user.name ||
                  `${user.firstName} ${user.lastName || ""}` ||
                  "User"}
              </h3>
              <Button
                variant="outline"
                size="sm"
                className="border-app-stroke text-app-headline hover:bg-app-main hover:text-white"
              >
                <UploadCloud className="w-4 h-4 mr-2" /> Change photo
              </Button>
            </div>
          </div>
          <Separator className="bg-app-stroke/20" />
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium text-app-headline mb-1"
            >
              Display Name
            </label>
            {isEditingUsername ? (
              <div className="flex items-center space-x-2">
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline/60"
                />
                <Button
                  onClick={handleUpdateUsername}
                  disabled={updateProfile.isPending}
                  className="bg-app-main text-app-secondary hover:bg-app-highlight"
                >
                  {updateProfile.isPending ? "Saving..." : "Save"}
                </Button>
                <Button
                  variant="ghost"
                  onClick={() => setIsEditingUsername(false)}
                  className="text-app-headline hover:bg-app-main/20"
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <div className="flex items-center justify-between p-2 rounded-md hover:bg-app-background">
                <span className="text-app-headline">
                  {userProfile?.user.name ||
                    `${user.firstName} ${user.lastName || ""}` ||
                    "Not set"}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setUsername(
                      userProfile?.user.name ||
                        `${user.firstName} ${user.lastName || ""}` ||
                        ""
                    );
                    setIsEditingUsername(true);
                  }}
                  className="text-app-main hover:text-app-highlight"
                >
                  Edit
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">
            Contact Information
          </CardTitle>
          <CardDescription className="text-app-headline/70">
            Manage your email addresses and connected accounts.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Email Addresses */}
          <div>
            <h4 className="font-medium text-app-headline mb-2">
              Email addresses
            </h4>
            <div className="space-y-2">
              {user.emailAddresses.map((emailItem, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 rounded-md hover:bg-app-background"
                >
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-app-main" />
                    <div>
                      <span className="text-app-headline">
                        {emailItem.emailAddress}
                      </span>
                      <div className="flex items-center space-x-2">
                        {user.primaryEmailAddressId === emailItem.id && (
                          <Badge className="bg-app-main text-app-secondary">
                            Primary
                          </Badge>
                        )}
                        <span className="flex items-center text-xs text-green-600">
                          <CheckCircle className="w-3 h-3 mr-1" /> Verified
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <Separator className="bg-app-stroke/20" />

          {/* Connected Accounts */}
          <div>
            <h4 className="font-medium text-app-headline mb-2">
              Connected accounts
            </h4>
            <div className="space-y-2">
              {user.externalAccounts.map((account, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 rounded-md hover:bg-app-background"
                >
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-blue-500" />
                    <div>
                      <span className="font-medium text-app-headline capitalize">
                        {account.provider}
                      </span>
                      <p className="text-xs text-app-headline/70">
                        {account.emailAddress}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-app-highlight hover:text-app-main"
                  >
                    Disconnect
                  </Button>
                </div>
              ))}
            </div>
            <Button
              variant="outline"
              className="w-full mt-4 border-app-stroke text-app-headline hover:bg-app-main hover:text-white"
            >
              <Link2 className="w-4 h-4 mr-2" /> Connect another account
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Telegram Integration */}
      <TelegramIntegration />

      {/* AI Settings */}
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader className="pb-4 px-3 sm:px-4 lg:px-6">
          <CardTitle className="text-app-headline text-lg sm:text-xl lg:text-2xl font-semibold">
            AI Settings
          </CardTitle>
          <CardDescription className="text-app-headline/70 text-sm sm:text-base lg:text-lg leading-relaxed max-w-2xl">
            Configure your AI personality and model preferences for generating
            replies.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4 px-3 sm:px-4 lg:px-6">
          <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6 auto-rows-fr">
            {/* AI Personality Card */}
            <div
              onClick={() => setPersonalityDialogOpen(true)}
              className="group relative bg-gradient-to-br from-app-card to-app-background border border-app-stroke rounded-xl p-4 sm:p-5 lg:p-6 cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:border-app-main/50 hover:bg-gradient-to-br hover:from-app-main/5 hover:to-app-background min-h-[120px] sm:min-h-[140px] lg:min-h-[160px] flex flex-col justify-between"
            >
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 sm:p-2.5 bg-app-main/10 rounded-lg group-hover:bg-app-main/20 transition-colors duration-300">
                    <User className="w-4 h-4 sm:w-5 sm:h-5 text-app-main" />
                  </div>
                  <h3 className="font-semibold text-sm sm:text-base lg:text-lg text-app-headline group-hover:text-app-main transition-colors duration-300">
                    AI Personality
                  </h3>
                </div>
                <p className="text-xs sm:text-sm lg:text-base text-app-headline/70 leading-relaxed group-hover:text-app-headline/80 transition-colors duration-300">
                  Choose your AI's response style and personality.
                </p>
              </div>
              <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-2 h-2 bg-app-main rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* AI Model Card */}
            <div
              onClick={() => setModelDialogOpen(true)}
              className="group relative bg-gradient-to-br from-app-card to-app-background border border-app-stroke rounded-xl p-4 sm:p-5 lg:p-6 cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:border-app-main/50 hover:bg-gradient-to-br hover:from-app-main/5 hover:to-app-background min-h-[120px] sm:min-h-[140px] lg:min-h-[160px] flex flex-col justify-between"
            >
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 sm:p-2.5 bg-app-main/10 rounded-lg group-hover:bg-app-main/20 transition-colors duration-300">
                    <Bot className="w-4 h-4 sm:w-5 sm:h-5 text-app-main" />
                  </div>
                  <h3 className="font-semibold text-sm sm:text-base lg:text-lg text-app-headline group-hover:text-app-main transition-colors duration-300">
                    AI Model
                  </h3>
                </div>
                <p className="text-xs sm:text-sm lg:text-base text-app-headline/70 leading-relaxed group-hover:text-app-headline/80 transition-colors duration-300">
                  Select the AI model for responses and analysis.
                </p>
              </div>
              <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-2 h-2 bg-app-main rounded-full animate-pulse"></div>
              </div>
            </div>

            {/* Generate Persona Card */}
            <div
              onClick={() => router.push('/persona-generator')}
              className="group relative bg-gradient-to-br from-purple-500/5 via-app-card to-pink-500/5 border border-purple-500/20 rounded-xl p-4 sm:p-5 lg:p-6 cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg hover:shadow-purple-500/10 hover:border-purple-500/40 hover:bg-gradient-to-br hover:from-purple-500/10 hover:to-pink-500/10 min-h-[120px] sm:min-h-[140px] lg:min-h-[160px] flex flex-col justify-between"
            >
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 sm:p-2.5 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg group-hover:shadow-lg group-hover:shadow-purple-500/20 transition-all duration-300">
                    <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                  </div>
                  <h3 className="font-semibold text-sm sm:text-base lg:text-lg text-app-headline group-hover:text-purple-600 transition-colors duration-300">
                    Generate Persona
                  </h3>
                </div>
                <p className="text-xs sm:text-sm lg:text-base text-app-headline/70 leading-relaxed group-hover:text-app-headline/80 transition-colors duration-300">
                  Create AI personas from Twitter accounts.
                </p>
              </div>
              <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
              </div>
              {/* Sparkle animation overlay */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none">
                <div className="absolute top-4 right-8 w-1 h-1 bg-purple-400 rounded-full animate-ping"></div>
                <div className="absolute bottom-6 left-8 w-1 h-1 bg-pink-400 rounded-full animate-ping animation-delay-300"></div>
                <div className="absolute top-1/2 right-4 w-0.5 h-0.5 bg-purple-300 rounded-full animate-pulse animation-delay-500"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subscription Details */}
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">Subscription Plan</CardTitle>
          <CardDescription className="text-app-headline/70">
            Your current plan and usage details.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 border border-blue-500/30 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-app-headline text-xl">
                  {subscription?.isClerkBilling
                    ? subscription.clerkPlan.name
                    : subscription?.legacyPlan?.displayName ||
                      userProfile?.plan?.displayName ||
                      "Reply Guy"}
                </h3>
                <p className="text-app-headline/70 text-lg">
                  $
                  {subscription?.isClerkBilling
                    ? "9" // Default to $9 for now, can be made dynamic
                    : subscription?.legacyPlan?.price ||
                      userProfile?.plan?.price ||
                      "9"}
                  /month
                </p>
                <p className="text-sm text-app-headline/70 mt-1">
                  {subscription?.isClerkBilling
                    ? "Basic AI replies with Gemini 2.5 Flash"
                    : subscription?.legacyPlan?.description ||
                      userProfile?.plan?.description ||
                      "Basic AI replies with Gemini 2.5 Flash"}
                </p>
              </div>
              <Button
                className="bg-app-main text-app-secondary hover:bg-app-highlight"
                onClick={() => setActiveSection("billing")}
              >
                Upgrade Plan
              </Button>
            </div>
          </div>

          {/* Usage Stats */}
          <div className="space-y-4">
            <h4 className="font-medium text-app-headline">Current Usage</h4>
            {billingUsage?.map((item: BillingUsageItem) => {
              const percentage =
                item.limit === -1
                  ? 0
                  : Math.min((item.currentUsage / item.limit) * 100, 100);
              const displayName = item.feature
                .replace("_", " ")
                .toLowerCase()
                .replace(/\b\w/g, (l: string) => l.toUpperCase());

              return (
                <div
                  key={item.feature}
                  className="p-4 bg-app-background/30 rounded-lg"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-app-headline font-medium">
                      {displayName}
                    </span>
                    <span className="text-sm text-app-headline/70">
                      {item.currentUsage} /{" "}
                      {item.limit === -1 ? "∞" : item.limit}
                    </span>
                  </div>
                  {item.limit !== -1 ? (
                    <div className="relative">
                      <div className="h-3 bg-app-stroke/30 rounded-full overflow-hidden">
                        <div
                          className={`h-full rounded-full transition-all duration-500 ${
                            percentage >= 90
                              ? "bg-red-500"
                              : percentage >= 70
                                ? "bg-yellow-500"
                                : "bg-gradient-to-r from-blue-500 to-purple-500"
                          }`}
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-60" />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const SecuritySectionContent = () => (
    <div className="space-y-6">
      <Card className="bg-app-card border-app-stroke shadow-sm">
        <CardHeader>
          <CardTitle className="text-app-headline">Account Security</CardTitle>
          <CardDescription className="text-app-headline/70">
            Manage your password, two-factor authentication, and security
            settings.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="clerk-profile-container">
            <UserProfile
              appearance={{
                elements: {
                  card: "bg-transparent shadow-none border-0",
                  navbar: "hidden",
                  pageScrollBox: "p-0",
                  page: "bg-transparent",
                  rootBox: "w-full",
                  profileSection:
                    "bg-app-background border border-app-stroke rounded-lg p-4 mb-4",
                  profileSectionTitle: "text-app-headline",
                  profileSectionContent: "text-app-headline",
                  formButtonPrimary:
                    "bg-app-main hover:bg-app-highlight text-app-secondary",
                  formFieldLabel: "text-app-headline",
                  formFieldInput:
                    "border-app-stroke focus:border-app-main bg-app-background",
                  identityPreview: "bg-app-background border border-app-stroke",
                  accordionTriggerButton:
                    "text-app-headline hover:bg-app-background",
                },
              }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline">
      <AuthenticatedNavbar currentPage="profile" />

      <header className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-app-headline">Settings</h1>
          <p className="text-app-headline opacity-70">
            Manage your account and preferences
          </p>
        </div>
        <Button
          variant="outline"
          asChild
          className="border-app-stroke bg-app-card hover:bg-app-main hover:text-white text-app-headline"
        >
          <Link href="/dashboard">
            <ChevronLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Left Sidebar Navigation */}
        <aside className="lg:col-span-1">
          <nav className="sticky top-8 bg-app-card p-4 rounded-lg border border-app-stroke shadow-md space-y-1">
            <Button
              variant={activeSection === "profile" ? "secondary" : "ghost"}
              onClick={() => setActiveSection("profile")}
              className={`w-full justify-start text-base p-3 ${activeSection === "profile" ? "bg-app-main text-app-secondary hover:bg-app-highlight" : "text-app-headline hover:bg-app-main/20"}`}
            >
              <User className="w-5 h-5 mr-3" /> Profile
            </Button>
            <Button
              variant={activeSection === "security" ? "secondary" : "ghost"}
              onClick={() => setActiveSection("security")}
              className={`w-full justify-start text-base p-3 ${activeSection === "security" ? "bg-app-main text-app-secondary hover:bg-app-highlight" : "text-app-headline hover:bg-app-main/20"}`}
            >
              <Shield className="w-5 h-5 mr-3" /> Security
            </Button>
            <Button
              variant={activeSection === "billing" ? "secondary" : "ghost"}
              onClick={() => setActiveSection("billing")}
              className={`w-full justify-start text-base p-3 ${activeSection === "billing" ? "bg-app-main text-app-secondary hover:bg-app-highlight" : "text-app-headline hover:bg-app-main/20"}`}
            >
              <CreditCard className="w-5 h-5 mr-3" /> Billing
            </Button>
          </nav>
        </aside>

        {/* Main Content Area */}
        <main className="lg:col-span-3">{renderSectionContent()}</main>
      </div>

      {/* AI Settings Dialogs */}
      <PersonalitySelector
        isOpen={personalityDialogOpen}
        onClose={() => setPersonalityDialogOpen(false)}
      />

      <ModelSelector
        isOpen={modelDialogOpen}
        onClose={() => setModelDialogOpen(false)}
      />
    </div>
  );
}
