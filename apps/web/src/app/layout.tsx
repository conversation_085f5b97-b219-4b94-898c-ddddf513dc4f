import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "../index.css";
import Providers from "@/components/providers";

// Auto-start Telegram bot in development
import "@/lib/telegram-startup";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "BuddyChip",
  description:
    "AI-powered social media monitoring and response tool for Twitter mentions",
  icons: {
    icon: [
      {
        url: "/Logo-L-Text.svg",
        type: "image/svg+xml",
        sizes: "any",
      },
      {
        url: "/Logo-L-Text.svg",
        type: "image/svg+xml",
        sizes: "1024x1024",
      },
    ],
    shortcut: "/Logo-L-Text.svg",
    apple: {
      url: "/Logo-L-Text.svg",
      type: "image/svg+xml",
      sizes: "512x512",
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

  return (
    <ClerkProvider
      publishableKey={publishableKey}
      dynamic
      telemetry={{
        disabled: process.env.NODE_ENV !== "production",
      }}
      appearance={{
        baseTheme: undefined,
      }}
    >
      <html lang="en" suppressHydrationWarning>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
          suppressHydrationWarning
        >
          <Providers>{children}</Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}
