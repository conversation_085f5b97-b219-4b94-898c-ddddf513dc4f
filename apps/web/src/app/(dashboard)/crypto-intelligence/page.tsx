"use client";

import { useUser } from "@clerk/nextjs";
import { redirect } from "next/navigation";
import AuthenticatedNavbar from "@/components/authenticated-navbar";
import SmartFollowersPanel from "@/components/crypto/SmartFollowersPanel";
import { TrendingProjectsPanel } from "@/components/crypto/TrendingProjectsPanel";
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation";

export default function CryptoIntelligencePage() {
  const { user, isLoaded } = useUser();

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-app-background flex items-center justify-center">
        <ShardLoadingAnimation size={80} />
      </div>
    );
  }

  if (!user) {
    redirect("/sign-in");
  }

  return (
    <div className="min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-6 sm:mb-8">
        <h1 className="text-[clamp(1.75rem,5vw,3rem)] font-bold tracking-wider text-app-headline mb-4">
          CRYPTO INTELLIGENCE
        </h1>
        <p className="text-sm sm:text-base text-app-headline/70 max-w-3xl mx-auto">
          Real-time crypto market intelligence, competitive analysis, and smart
          account discovery powered by Cookie.fun
        </p>
      </header>

      <AuthenticatedNavbar currentPage="crypto-intelligence" />

      <main className="space-y-6 sm:space-y-8">
        {/* Main Intelligence Dashboard - Unified Component */}
        <TrendingProjectsPanel className="w-full" />

        {/* Smart Followers Discovery - Full Width */}
        <SmartFollowersPanel />
      </main>
    </div>
  );
}
