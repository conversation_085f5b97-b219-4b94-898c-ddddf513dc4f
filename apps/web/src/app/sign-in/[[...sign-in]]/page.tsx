import { SignIn } from "@clerk/nextjs";

export default function SignInPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-app-background">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-app-headline mb-2">
            Welcome Back
          </h1>
          <p className="text-app-sub-headline">
            Sign in to your BuddyChip account
          </p>
        </div>

        <div className="bg-app-card rounded-lg border border-app-stroke shadow-lg p-6">
          <SignIn
            appearance={{
              elements: {
                formButtonPrimary:
                  "bg-app-main hover:bg-app-highlight text-app-secondary",
                card: "bg-transparent shadow-none",
                headerTitle: "hidden",
                headerSubtitle: "hidden",
                socialButtonsBlockButton:
                  "border-app-stroke hover:bg-app-background",
                dividerText: "text-app-headline",
                formFieldLabel: "text-app-headline",
                formFieldInput: "border-app-stroke focus:border-app-main",
                footerActionLink: "text-app-main hover:text-app-highlight",
              },
            }}
            redirectUrl="/dashboard"
            signUpUrl="/sign-up"
          />
        </div>

        <div className="text-center mt-6">
          <p className="text-sm text-app-headline opacity-70">
            Don't have an account?{" "}
            <a
              href="/sign-up"
              className="text-app-main hover:text-app-highlight font-medium"
            >
              Sign up here
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
