"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Check,
  CreditCard,
  Crown,
  Sparkles,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { trpc } from "@/utils/trpc";

interface PricingSectionProps {
  className?: string;
}

export default function PricingSection({
  className = "",
}: PricingSectionProps) {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const { data: subscription, isLoading: subscriptionLoading } =
    trpc.billing.getSubscription.useQuery();
  const { data: usage } = trpc.billing.getUsage.useQuery();
  const { data: availablePlans } = trpc.billing.getAvailablePlans.useQuery();
  const changePlan = trpc.billing.changePlan.useMutation();
  const createBillingPortal =
    trpc.billing.createBillingPortalSession.useMutation();

  console.log("🔍 PricingSection: Rendering with subscription:", subscription);

  const handleUpgradePlan = async (planId: string) => {
    try {
      setIsUpgrading(true);
      const result = await changePlan.mutateAsync({ planId });

      if (result.success && result.redirectUrl) {
        window.location.href = result.redirectUrl;
      }
    } catch (error) {
      console.error("Failed to upgrade plan:", error);
      // In a real app, you'd show a toast notification here
    } finally {
      setIsUpgrading(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      setIsUpgrading(true);
      const result = await createBillingPortal.mutateAsync();

      if (result.url) {
        window.location.href = result.url;
      }
    } catch (error) {
      console.error("Failed to open billing portal:", error);
      // Fallback to pricing page
      window.location.href = "/pricing";
    } finally {
      setIsUpgrading(false);
    }
  };

  if (subscriptionLoading || isUpgrading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-app-stroke rounded w-1/3 mb-4"></div>
          <div className="h-32 bg-app-stroke rounded"></div>
        </div>
      </div>
    );
  }

  const getUsagePercentage = (current: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((current / limit) * 100, 100);
  };

  const planIcons = {
    free: Bot,
    "reply-guy": Bot,
    "reply-god": Crown,
    team: Users,
  };

  const planColors = {
    free: "from-green-500/20 to-emerald-500/20 border-green-500/30",
    "reply-guy": "from-blue-500/20 to-cyan-500/20 border-blue-500/30",
    "reply-god": "from-purple-500/20 to-pink-500/20 border-purple-500/30",
    team: "from-orange-500/20 to-red-500/20 border-orange-500/30",
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Current Plan Status */}
      <div className="animate-in slide-in-from-bottom-4 duration-500">
        <Card className="bg-gradient-to-br from-app-card to-app-background border-app-stroke shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-app-main/10">
                  <CreditCard className="w-5 h-5 text-app-main" />
                </div>
                <div>
                  <CardTitle className="text-app-headline">
                    Current Plan
                  </CardTitle>
                  <CardDescription className="text-app-sub-headline">
                    Your subscription details and usage
                  </CardDescription>
                </div>
              </div>
              {subscription?.isClerkBilling && (
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                >
                  <Sparkles className="w-3 h-3 mr-1" />
                  Active
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 rounded-lg bg-app-background/50">
              <div>
                <h3 className="font-semibold text-app-headline text-lg">
                  {subscription?.isClerkBilling
                    ? subscription.clerkPlan.name
                    : subscription?.legacyPlan.displayName || "Loading..."}
                </h3>
                <p className="text-app-sub-headline text-sm">
                  {subscription?.isClerkBilling
                    ? `Managed by Clerk • Status: ${subscription.clerkPlan.status}`
                    : `$${subscription?.legacyPlan.price || "0"}/month • Legacy plan`}
                </p>
              </div>
              <Button
                className="bg-app-main text-app-secondary hover:bg-app-highlight"
                onClick={handleManageBilling}
                disabled={isUpgrading}
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                {isUpgrading ? "Loading..." : "Manage Plan"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Usage Statistics */}
      {usage && (
        <div className="animate-in slide-in-from-bottom-4 duration-500 delay-100">
          <Card className="bg-app-card border-app-stroke shadow-lg">
            <CardHeader>
              <CardTitle className="text-app-headline flex items-center gap-2">
                <Zap className="w-5 h-5 text-app-main" />
                Usage Overview
              </CardTitle>
              <CardDescription className="text-app-sub-headline">
                Current billing period usage
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {usage.map((item: any, index: number) => {
                  const percentage = getUsagePercentage(
                    item.currentUsage,
                    item.limit
                  );
                  const isUnlimited = item.limit === -1;

                  return (
                    <div
                      key={item.feature}
                      className="p-4 rounded-lg bg-app-background/30 hover:bg-app-background/50 transition-all duration-300 hover:scale-105"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-app-headline font-medium text-sm">
                          {item.feature
                            .replace("_", " ")
                            .toLowerCase()
                            .replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        </span>
                        <span className="text-xs text-app-sub-headline">
                          {item.currentUsage} / {isUnlimited ? "∞" : item.limit}
                        </span>
                      </div>
                      {!isUnlimited && (
                        <div className="relative">
                          <div className="h-3 bg-app-stroke/30 rounded-full overflow-hidden">
                            <div
                              className={`h-full rounded-full transition-all duration-500 ${
                                percentage >= 90
                                  ? "bg-red-500"
                                  : percentage >= 70
                                    ? "bg-yellow-500"
                                    : "bg-gradient-to-r from-blue-500 to-purple-500"
                              }`}
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                      {isUnlimited && (
                        <div className="h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full opacity-60" />
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Available Plans */}
      {availablePlans && !subscription?.isClerkBilling && (
        <div className="animate-in slide-in-from-bottom-4 duration-500 delay-200">
          <Card className="bg-app-card border-app-stroke shadow-lg">
            <CardHeader>
              <CardTitle className="text-app-headline flex items-center gap-2">
                <Crown className="w-5 h-5 text-app-main" />
                Upgrade Your Plan
              </CardTitle>
              <CardDescription className="text-app-sub-headline">
                Choose the perfect plan for your needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                {availablePlans.map((plan: any, index: number) => {
                  const Icon =
                    planIcons[plan.id as keyof typeof planIcons] || Bot;
                  const isSelected = selectedPlan === plan.id;

                  return (
                    <div
                      key={plan.id}
                      className={`relative overflow-hidden rounded-lg border-2 transition-all duration-300 cursor-pointer hover:scale-102 active:scale-98 ${
                        isSelected
                          ? `bg-gradient-to-br ${planColors[plan.id as keyof typeof planColors]} scale-105`
                          : "border-app-stroke hover:border-app-main/50 bg-app-background/30"
                      }`}
                      onClick={() =>
                        setSelectedPlan(isSelected ? null : plan.id)
                      }
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      <div className="p-6">
                        <div className="flex items-center gap-3 mb-4">
                          <div
                            className={`p-2 rounded-lg ${isSelected ? "bg-white/20" : "bg-app-main/10"}`}
                          >
                            <Icon
                              className={`w-5 h-5 ${isSelected ? "text-white" : "text-app-main"}`}
                            />
                          </div>
                          <div>
                            <h3
                              className={`font-semibold ${isSelected ? "text-white" : "text-app-headline"}`}
                            >
                              {plan.name}
                            </h3>
                            <p
                              className={`text-2xl font-bold ${isSelected ? "text-white" : "text-app-main"}`}
                            >
                              ${plan.price}
                              <span
                                className={`text-sm font-normal ${isSelected ? "text-white/70" : "text-app-sub-headline"}`}
                              >
                                /{plan.interval}
                              </span>
                            </p>
                          </div>
                        </div>

                        {isSelected && (
                          <div className="space-y-2 mb-4 animate-in slide-in-from-top-2 duration-300">
                            {plan.features.map(
                              (feature: string, idx: number) => (
                                <div
                                  key={idx}
                                  className="flex items-center gap-2 text-white/90 text-sm"
                                >
                                  <Check className="w-3 h-3" />
                                  {feature}
                                </div>
                              )
                            )}
                          </div>
                        )}

                        {!isSelected && (
                          <div className="space-y-1 mb-4">
                            {plan.features
                              .slice(0, 3)
                              .map((feature: string, idx: number) => (
                                <div
                                  key={idx}
                                  className="flex items-center gap-2 text-app-sub-headline text-sm"
                                >
                                  <Check className="w-3 h-3 text-app-main" />
                                  {feature}
                                </div>
                              ))}
                            {plan.features.length > 3 && (
                              <p className="text-xs text-app-sub-headline">
                                +{plan.features.length - 3} more features
                              </p>
                            )}
                          </div>
                        )}

                        <Button
                          className={`w-full ${
                            isSelected
                              ? "bg-white text-gray-900 hover:bg-gray-100"
                              : "bg-app-main text-app-secondary hover:bg-app-highlight"
                          } transition-all duration-200`}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!isSelected) {
                              handleUpgradePlan(plan.id);
                            }
                          }}
                          disabled={isSelected || isUpgrading}
                        >
                          {isSelected
                            ? "Current Plan"
                            : isUpgrading
                              ? "Processing..."
                              : "Upgrade"}
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
