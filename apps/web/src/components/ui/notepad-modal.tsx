"use client";

import {
  <PERSON>mark,
  <PERSON><PERSON>pen,
  ExternalLink,
  FileText,
  Lightbulb,
  Save,
  Search,
  Star,
  Trash2,
  X,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { trpc } from "@/utils/trpc";
import { Badge } from "./badge";
import { Button } from "./button";
import { Card, CardContent, CardHeader, CardTitle } from "./card";
import { Input } from "./input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "./tabs";
import { Textarea } from "./textarea";

// Input validation utilities
const validateSearchQuery = (query: string): boolean => {
  if (!query.trim()) {
    toast.error("Please enter a search query");
    return false;
  }
  if (query.length > 500) {
    toast.error("Search query too long (max 500 characters)");
    return false;
  }
  return true;
};

const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

interface NotepadModalProps {
  isOpen: boolean;
  onClose: () => void;
  mentionId: string;
  mentionContent: string;
  authorHandle: string;
  authorName: string;
}

interface Source {
  id: string;
  title: string;
  url: string;
  content: string | null;
  sourceType: string;
  searchTool: string;
  relevanceScore: number | null;
  credibilityScore: number | null;
  publishedAt: string | null;
  extractedAt: string;
  isBookmarked: boolean;
  userRating: number | null;
  userNotes: string | null;
  createdAt: string;
  updatedAt: string;
}

interface Draft {
  id: string;
  content: string;
  version: number;
  title: string | null;
  generatedBy: string | null;
  model: string | null;
  prompt: string | null;
  tokensUsed: number | null;
  isActive: boolean;
  isFavorite: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Notepad {
  id: string;
  mentionId: string;
  title: string | null;
  notes: string | null;
  draftResponse: string | null;
  finalResponse: string | null;
  researchQuery: string | null;
  researchContext: any;
  isActive: boolean;
  lastUsedAt: string;
  createdAt: string;
  updatedAt: string;
  sources: Source[];
  drafts: Draft[];
}

export default function NotepadModal({
  isOpen,
  onClose,
  mentionId,
  mentionContent,
  authorHandle,
  authorName,
}: NotepadModalProps) {
  const [activeTab, setActiveTab] = useState("overview");
  const [notes, setNotes] = useState("");
  const [draftResponse, setDraftResponse] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // tRPC queries and mutations
  const {
    data: notepadData,
    isLoading,
    refetch,
  } = trpc.notepad.getOrCreate.useQuery({ mentionId }, { enabled: isOpen });

  const updateNotepadMutation = trpc.notepad.update.useMutation();

  const addSourceMutation = trpc.notepad.addSource.useMutation();
  const updateSourceMutation = trpc.notepad.updateSource.useMutation();
  const deleteSourceMutation = trpc.notepad.deleteSource.useMutation();
  const createDraftMutation = trpc.notepad.createDraft.useMutation();

  const notepad = notepadData?.notepad;

  // Update local state when notepad data changes
  useEffect(() => {
    if (notepad) {
      setNotes(notepad.notes || "");
      setDraftResponse(notepad.draftResponse || "");
    }
  }, [notepad]);

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleSaveNotes = async () => {
    if (!notepad) return;

    try {
      await updateNotepadMutation.mutateAsync({
        notepadId: notepad.id,
        notes,
      });
      toast.success("Notes saved successfully");
      refetch();
    } catch (error: any) {
      console.error("Error saving notes:", error);
      toast.error(error?.message || "Failed to save notes");
    }
  };

  const handleSaveDraft = async () => {
    if (!notepad) return;

    try {
      await updateNotepadMutation.mutateAsync({
        notepadId: notepad.id,
        draftResponse,
      });
      toast.success("Draft saved successfully");
      refetch();
    } catch (error: any) {
      console.error("Error saving draft:", error);
      toast.error(error?.message || "Failed to save draft");
    }
  };

  const handleCreateDraft = async () => {
    if (!notepad || !draftResponse.trim()) return;

    try {
      await createDraftMutation.mutateAsync({
        notepadId: notepad.id,
        content: draftResponse,
        generatedBy: "user",
      });
      toast.success("Draft version created successfully");
      refetch();
    } catch (error: any) {
      console.error("Error creating draft:", error);
      toast.error(error?.message || "Failed to create draft");
    }
  };

  const handleBookmarkSource = async (
    sourceId: string,
    isBookmarked: boolean
  ) => {
    try {
      await updateSourceMutation.mutateAsync({
        sourceId,
        isBookmarked: !isBookmarked,
      });
      refetch();
    } catch (error: any) {
      console.error("Error updating bookmark:", error);
      toast.error(error?.message || "Failed to update bookmark");
    }
  };

  const handleRateSource = async (sourceId: string, rating: number) => {
    try {
      await updateSourceMutation.mutateAsync({
        sourceId,
        userRating: rating,
      });
      refetch();
    } catch (error: any) {
      console.error("Error rating source:", error);
      toast.error(error?.message || "Failed to rate source");
    }
  };

  const handleDeleteSource = async (sourceId: string) => {
    try {
      await deleteSourceMutation.mutateAsync({ sourceId });
      toast.success("Source deleted successfully");
      refetch();
    } catch (error: any) {
      console.error("Error deleting source:", error);
      toast.error(error?.message || "Failed to delete source");
    }
  };

  const handleSearch = async () => {
    if (!validateSearchQuery(searchQuery) || !notepad) return;

    setIsSearching(true);
    try {
      // This would trigger the enhanced search with notepad integration
      // For now, we'll just update the research query
      await updateNotepadMutation.mutateAsync({
        notepadId: notepad.id,
        researchQuery: searchQuery,
      });

      toast.success("Search initiated - sources will be added automatically");
    } catch (error) {
      console.error("Error initiating search:", error);
      toast.error("Failed to initiate search");
    } finally {
      setIsSearching(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative w-full max-w-6xl h-[90vh] mx-4 bg-app-background border border-app-stroke rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-app-stroke bg-app-card">
          <div className="flex items-center gap-3">
            <BookOpen className="w-5 h-5 text-app-main" />
            <div>
              <h2 className="text-lg font-semibold text-app-headline">
                Response Notepad
              </h2>
              <p className="text-sm text-app-sub-headline">
                Crafting response for @{authorHandle}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-app-sub-headline hover:text-app-headline"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex h-[calc(100%-4rem)]">
          {/* Original Tweet */}
          <div className="w-80 border-r border-app-stroke bg-app-card p-4 overflow-y-auto">
            <h3 className="font-medium text-app-headline mb-3">
              Original Tweet
            </h3>
            <Card className="bg-app-background border-app-stroke">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-app-main/10 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-app-main">
                      {authorName.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-app-headline text-sm">
                      {authorName}
                    </p>
                    <p className="text-xs text-app-sub-headline">
                      @{authorHandle}
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-app-headline leading-relaxed">
                  {mentionContent}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Notepad Area */}
          <div className="flex-1 flex flex-col">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col"
            >
              <TabsList className="grid w-full grid-cols-4 bg-app-card border-b border-app-stroke rounded-none">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="research">Research</TabsTrigger>
                <TabsTrigger value="drafts">Drafts</TabsTrigger>
                <TabsTrigger value="notes">Notes</TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-hidden">
                <TabsContent
                  value="overview"
                  className="h-full p-4 overflow-y-auto"
                >
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-app-headline mb-2">
                        Quick Draft
                      </h3>
                      <Textarea
                        value={draftResponse}
                        onChange={(e) => setDraftResponse(e.target.value)}
                        placeholder="Start crafting your response..."
                        className="min-h-32 bg-app-background border-app-stroke"
                      />
                      <div className="flex gap-2 mt-2">
                        <Button
                          size="sm"
                          onClick={handleSaveDraft}
                          disabled={updateNotepadMutation.isPending}
                          className="bg-app-main text-app-secondary hover:bg-app-highlight"
                        >
                          <Save className="w-4 h-4 mr-1" />
                          Save Draft
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleCreateDraft}
                          disabled={
                            createDraftMutation.isPending ||
                            !draftResponse.trim()
                          }
                          className="border-app-stroke text-app-headline hover:bg-app-main hover:text-white"
                        >
                          Create Version
                        </Button>
                      </div>
                    </div>

                    {notepad?.sources && notepad.sources.length > 0 && (
                      <div>
                        <h3 className="font-medium text-app-headline mb-2">
                          Recent Sources
                        </h3>
                        <div className="space-y-2">
                          {notepad.sources.slice(0, 3).map((source: Source) => (
                            <Card
                              key={source.id}
                              className="bg-app-background border-app-stroke"
                            >
                              <CardContent className="p-3">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <h4 className="font-medium text-app-headline text-sm mb-1">
                                      {source.title}
                                    </h4>
                                    <p className="text-xs text-app-sub-headline mb-2">
                                      {source.url}
                                    </p>
                                    <div className="flex items-center gap-2">
                                      <Badge
                                        variant="secondary"
                                        className="text-xs"
                                      >
                                        {source.sourceType}
                                      </Badge>
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {source.searchTool}
                                      </Badge>
                                    </div>
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() =>
                                      window.open(source.url, "_blank")
                                    }
                                    className="text-app-sub-headline hover:text-app-headline"
                                  >
                                    <ExternalLink className="w-3 h-3" />
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent
                  value="research"
                  className="h-full p-4 overflow-y-auto"
                >
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-medium text-app-headline mb-2">
                        Search & Research
                      </h3>
                      <div className="flex gap-2">
                        <Input
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          placeholder="Search for relevant information..."
                          className="bg-app-background border-app-stroke"
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              handleSearch();
                            }
                          }}
                        />
                        <Button
                          onClick={handleSearch}
                          disabled={isSearching || !searchQuery.trim()}
                          className="bg-app-main text-app-secondary hover:bg-app-highlight"
                        >
                          <Search className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium text-app-headline mb-2">
                        Sources
                      </h3>
                      {isLoading ? (
                        <p className="text-app-sub-headline">
                          Loading sources...
                        </p>
                      ) : notepad?.sources && notepad.sources.length > 0 ? (
                        <div className="space-y-3">
                          {notepad.sources.map((source: Source) => (
                            <Card
                              key={source.id}
                              className="bg-app-background border-app-stroke"
                            >
                              <CardContent className="p-4">
                                <div className="flex items-start justify-between mb-2">
                                  <h4 className="font-medium text-app-headline text-sm">
                                    {source.title}
                                  </h4>
                                  <div className="flex items-center gap-1">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() =>
                                        handleBookmarkSource(
                                          source.id,
                                          source.isBookmarked
                                        )
                                      }
                                      className={`text-xs ${source.isBookmarked ? "text-yellow-500" : "text-app-sub-headline"}`}
                                    >
                                      <Bookmark className="w-3 h-3" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() =>
                                        window.open(source.url, "_blank")
                                      }
                                      className="text-app-sub-headline hover:text-app-headline"
                                    >
                                      <ExternalLink className="w-3 h-3" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() =>
                                        handleDeleteSource(source.id)
                                      }
                                      className="text-red-500 hover:text-red-600"
                                    >
                                      <Trash2 className="w-3 h-3" />
                                    </Button>
                                  </div>
                                </div>
                                <p className="text-xs text-app-sub-headline mb-2">
                                  {source.url}
                                </p>
                                {source.content && (
                                  <p className="text-xs text-app-headline mb-2 line-clamp-3">
                                    {source.content}
                                  </p>
                                )}
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {source.sourceType}
                                    </Badge>
                                    <Badge
                                      variant="outline"
                                      className="text-xs"
                                    >
                                      {source.searchTool}
                                    </Badge>
                                    {source.relevanceScore && (
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {Math.round(
                                          source.relevanceScore * 100
                                        )}
                                        % relevant
                                      </Badge>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-1">
                                    {[1, 2, 3, 4, 5].map((star) => (
                                      <button
                                        key={star}
                                        onClick={() =>
                                          handleRateSource(source.id, star)
                                        }
                                        className={`text-xs ${
                                          source.userRating &&
                                          star <= source.userRating
                                            ? "text-yellow-500"
                                            : "text-app-stroke"
                                        }`}
                                      >
                                        <Star className="w-3 h-3" />
                                      </button>
                                    ))}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      ) : (
                        <p className="text-app-sub-headline">
                          No sources yet. Start by searching for relevant
                          information.
                        </p>
                      )}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent
                  value="drafts"
                  className="h-full p-4 overflow-y-auto"
                >
                  <div className="space-y-4">
                    <h3 className="font-medium text-app-headline">
                      Draft Versions
                    </h3>
                    {notepad?.drafts && notepad.drafts.length > 0 ? (
                      <div className="space-y-3">
                        {notepad.drafts.map((draft: Draft) => (
                          <Card
                            key={draft.id}
                            className="bg-app-background border-app-stroke"
                          >
                            <CardHeader className="pb-2">
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-sm">
                                  {draft.title || `Draft v${draft.version}`}
                                </CardTitle>
                                <div className="flex items-center gap-2">
                                  <Badge variant="outline" className="text-xs">
                                    v{draft.version}
                                  </Badge>
                                  {draft.generatedBy && (
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {draft.generatedBy}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm text-app-headline mb-2">
                                {draft.content}
                              </p>
                              <div className="flex items-center justify-between text-xs text-app-sub-headline">
                                <span>
                                  Created{" "}
                                  {new Date(
                                    draft.createdAt
                                  ).toLocaleDateString()}
                                </span>
                                {draft.tokensUsed && (
                                  <span>{draft.tokensUsed} tokens</span>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <p className="text-app-sub-headline">
                        No drafts yet. Create your first draft in the Overview
                        tab.
                      </p>
                    )}
                  </div>
                </TabsContent>

                <TabsContent
                  value="notes"
                  className="h-full p-4 overflow-y-auto"
                >
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-app-headline">
                        Personal Notes
                      </h3>
                      <Button
                        size="sm"
                        onClick={handleSaveNotes}
                        disabled={updateNotepadMutation.isPending}
                        className="bg-app-main text-app-secondary hover:bg-app-highlight"
                      >
                        <Save className="w-4 h-4 mr-1" />
                        Save Notes
                      </Button>
                    </div>
                    <Textarea
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Add your thoughts, ideas, and notes here..."
                      className="min-h-96 bg-app-background border-app-stroke"
                    />
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}
