import type React from "react";

const ShardLoadingAnimation = ({ size = 64 }: { size?: number }) => {
  const numShards = 6;
  const shardWidth = size / 16; // e.g., 4px for size 64
  const shardHeight = size / 4; // e.g., 16px for size 64
  const radius = size / 3.2; // e.g., 20px for size 64

  return (
    <div
      className="flex justify-center items-center"
      style={{ width: size, height: size }}
    >
      <div className="relative" style={{ width: size, height: size }}>
        {Array.from({ length: numShards }).map((_, i) => {
          const angle = (i / numShards) * 360;
          return (
            <div
              key={i}
              className={`absolute top-1/2 left-1/2 rounded-full ${i % 2 === 0 ? "bg-app-main" : "bg-app-highlight"}`}
              style={
                {
                  width: `${shardWidth}px`,
                  height: `${shardHeight}px`,
                  transformOrigin: "center",
                  animation: `shard-loading-animation 1.5s ease-in-out infinite`,
                  animationDelay: `${i * 0.15}s`,
                  // CSS variables for the animation
                  "--angle": `${angle}deg`,
                  "--radius": `${radius}px`,
                  "--base-height": `${shardHeight}px`,
                } as React.CSSProperties
              }
            />
          );
        })}
      </div>
    </div>
  );
};

export default ShardLoadingAnimation;
