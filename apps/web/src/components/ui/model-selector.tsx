"use client";

import { <PERSON>, <PERSON>Sign, Gauge, X, Zap } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { trpc } from "@/utils/trpc";
import { Button } from "./button";

interface ModelSelectorProps {
  isOpen: boolean;
  onClose: () => void;
}

interface AIModel {
  id: string;
  name: string;
  displayName: string;
  description: string;
  provider: string;
  costTier: string;
  speed: string;
}

// Icon mapping for cost tiers
const COST_TIER_ICONS: Record<string, any> = {
  low: Zap,
  medium: Gauge,
  high: Brain,
};

// Color mapping for cost tiers
const COST_TIER_COLORS: Record<string, string> = {
  low: "text-green-500",
  medium: "text-yellow-500",
  high: "text-red-500",
};

// Badge colors for speed
const SPEED_COLORS: Record<string, string> = {
  fast: "bg-green-100 text-green-800",
  medium: "bg-yellow-100 text-yellow-800",
  slow: "bg-red-100 text-red-800",
};

export default function ModelSelector({ isOpen, onClose }: ModelSelectorProps) {
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);

  // Get current user's selected model
  const { data: currentModel, refetch } = trpc.user.getSelectedModel.useQuery(
    undefined,
    {
      enabled: isOpen,
    }
  );

  // Get available AI models
  const { data: models } = trpc.user.getAIModels.useQuery();

  // Update model mutation
  const updateModelMutation = trpc.user.updateSelectedModel.useMutation();

  // Load current settings when dialog opens
  useEffect(() => {
    if (isOpen && currentModel) {
      setSelectedModelId(currentModel.id);
    } else if (isOpen && !currentModel) {
      setSelectedModelId(null);
    }
  }, [isOpen, currentModel]);

  // Handle ESC key to close dialog
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleSave = async () => {
    try {
      await updateModelMutation.mutateAsync({
        modelId: selectedModelId || undefined,
      });

      await refetch();
      toast.success("AI model updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating model:", error);
      toast.error("Failed to update AI model");
    }
  };

  const handleCancel = () => {
    // Reset to current saved values
    if (currentModel) {
      setSelectedModelId(currentModel.id);
    } else {
      setSelectedModelId(null);
    }
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleCancel}
      />

      {/* Dialog */}
      <div className="relative bg-app-card border border-app-stroke rounded-lg shadow-xl max-w-4xl w-full mx-4 p-6 max-h-[90vh] overflow-y-auto">
        {/* Close button */}
        <button
          onClick={handleCancel}
          className="absolute top-4 right-4 text-app-headline hover:text-app-main transition-colors"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Header */}
        <div className="pr-8 mb-6">
          <h2 className="text-xl font-semibold text-app-headline mb-2 flex items-center">
            <Brain className="w-5 h-5 mr-2" />
            AI Model Selection
          </h2>
          <p className="text-app-headline opacity-70 text-sm">
            Choose the AI model that best fits your needs. Each model has
            different capabilities, speed, and cost characteristics.
          </p>
        </div>

        {/* Model Options */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-app-headline mb-4">
            Available Models
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {models?.map((model: AIModel) => {
              const IconComponent = COST_TIER_ICONS[model.costTier] || Brain;
              const costColor =
                COST_TIER_COLORS[model.costTier] || "text-gray-500";
              const speedColor =
                SPEED_COLORS[model.speed] || "bg-gray-100 text-gray-800";
              const isSelected = selectedModelId === model.id;

              return (
                <div
                  key={model.id}
                  onClick={() => setSelectedModelId(model.id)}
                  className={`
                    p-4 border rounded-lg cursor-pointer transition-all
                    ${
                      isSelected
                        ? "border-app-main bg-app-main/10 shadow-md"
                        : "border-app-stroke hover:border-app-main/50 hover:bg-app-background/50"
                    }
                  `}
                >
                  <div className="space-y-3">
                    {/* Model header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-2">
                        <IconComponent
                          className={`w-5 h-5 ${isSelected ? "text-app-main" : costColor}`}
                        />
                        <h4
                          className={`font-medium ${isSelected ? "text-app-main" : "text-app-headline"}`}
                        >
                          {model.name}
                        </h4>
                      </div>
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${speedColor}`}
                      >
                        {model.speed}
                      </span>
                    </div>

                    {/* Description */}
                    <p className="text-sm text-app-headline opacity-70">
                      {model.description}
                    </p>

                    {/* Cost tier indicator */}
                    <div className="flex items-center space-x-2">
                      <DollarSign className={`w-4 h-4 ${costColor}`} />
                      <span className={`text-sm capitalize ${costColor}`}>
                        {model.costTier} cost
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}

            {/* Plan Default option */}
            <div
              onClick={() => setSelectedModelId(null)}
              className={`
                p-4 border rounded-lg cursor-pointer transition-all
                ${
                  selectedModelId === null
                    ? "border-app-main bg-app-main/10 shadow-md"
                    : "border-app-stroke hover:border-app-main/50 hover:bg-app-background/50"
                }
              `}
            >
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Gauge
                    className={`w-5 h-5 ${selectedModelId === null ? "text-app-main" : "text-app-headline opacity-60"}`}
                  />
                  <h4
                    className={`font-medium ${selectedModelId === null ? "text-app-main" : "text-app-headline"}`}
                  >
                    Plan Default
                  </h4>
                </div>
                <p className="text-sm text-app-headline opacity-70">
                  Use the default model for your subscription plan
                </p>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                    automatic
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Current Selection Info */}
        {selectedModelId && (
          <div className="mb-6 p-4 bg-app-background rounded-lg border border-app-stroke">
            <h4 className="font-medium text-app-headline mb-2">
              Selected Model
            </h4>
            {(() => {
              const selectedModel = models?.find(
                (m) => m.id === selectedModelId
              );
              return selectedModel ? (
                <div className="text-sm text-app-headline opacity-80">
                  <p>
                    <strong>{selectedModel.name}</strong>:{" "}
                    {selectedModel.description}
                  </p>
                  <p className="mt-1">
                    Speed: {selectedModel.speed} • Cost:{" "}
                    {selectedModel.costTier}
                  </p>
                </div>
              ) : null;
            })()}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="border-app-stroke text-app-headline hover:bg-app-background"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={updateModelMutation.isPending}
            className="bg-app-main text-app-secondary hover:bg-app-highlight"
          >
            {updateModelMutation.isPending ? "Saving..." : "Save Model"}
          </Button>
        </div>
      </div>
    </div>
  );
}
