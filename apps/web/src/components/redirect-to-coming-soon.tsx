"use client";

import { useUser } from "@clerk/nextjs";
import { redirect } from "next/navigation";
import { useEffect } from "react";
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation";

interface RedirectToComingSoonProps {
  requireAuth?: boolean;
}

export default function RedirectToComingSoon({
  requireAuth = true,
}: RedirectToComingSoonProps) {
  const { user, isLoaded } = useUser();

  useEffect(() => {
    if (requireAuth) {
      if (isLoaded && user) {
        redirect("/coming-soon");
      }
    } else {
      redirect("/coming-soon");
    }
  }, [isLoaded, user, requireAuth]);

  if (requireAuth && !isLoaded) {
    return (
      <div className="min-h-screen bg-app-background flex items-center justify-center">
        <ShardLoadingAnimation size={80} />
      </div>
    );
  }

  if (requireAuth && !user) {
    redirect("/sign-in");
  }

  return (
    <div className="min-h-screen bg-app-background flex items-center justify-center">
      <ShardLoadingAnimation size={80} />
    </div>
  );
}
