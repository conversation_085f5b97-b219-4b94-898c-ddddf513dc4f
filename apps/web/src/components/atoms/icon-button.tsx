"use client";
import type { IconType } from "react-icons";

interface IconButtonProps {
  icon?: IconType | null;
  onClick?: () => void;
  className?: string;
  variant?: "primary" | "secondary" | "tertiary";
  iconClassName?: string;
  "aria-label"?: string;
  asChild?: boolean;
}

const IconButton = ({
  icon = null,
  onClick,
  className,
  variant = "primary",
  iconClassName,
  "aria-label": ariaLabel,
  asChild = false,
}: IconButtonProps) => {
  const variantClasses = {
    primary: "bg-app-secondary text-app-headline",
    secondary: "bg-app-main text-app-secondary",
    tertiary: "bg-app-stroke text-app-secondary",
  };
  const Icon = icon;

  const baseClassName = `w-10 h-10 rounded-full flex items-center justify-center ${variantClasses[variant]} ${className} transition-all duration-300`;

  if (asChild) {
    return (
      <span className={`${baseClassName} cursor-pointer`}>
        {Icon && <Icon className={`w-[28px] h-[28px] ${iconClassName}`} />}
      </span>
    );
  }

  return (
    <button
      type="button"
      className={`${baseClassName} cursor-pointer`}
      onClick={onClick}
      aria-label={ariaLabel}
    >
      {Icon && <Icon className={`w-[28px] h-[28px] ${iconClassName}`} />}
    </button>
  );
};

export default IconButton;
