"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON>ink,
  Loader2,
  Search,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { trpc } from "@/utils/trpc";
import SectorRadarChart from "./charts/SectorRadarChart";

interface CompetitiveAnalysis {
  project: {
    name: string;
    slug: string;
    symbol?: string;
    description?: string;
    sector?: string;
    mindshare?: number;
    twitterUrl?: string;
    websiteUrl?: string;
  } | null;
  competitors: Array<{
    name: string;
    slug: string;
    symbol?: string;
    sector?: string;
    mindshare?: number;
    smartEngagementPoints?: number;
    twitterUrl?: string;
  }>;
  sectorTrends: Array<{
    name: string;
    slug: string;
    symbol?: string;
    mindshare?: number;
    trending?: boolean;
  }>;
}

export default function CompetitiveAnalysisWidget() {
  const [projectQuery, setProjectQuery] = useState("");
  const [analysis, setAnalysis] = useState<CompetitiveAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // tRPC mutations/queries
  const searchProjectsMutation = trpc.crypto.searchProjects.useMutation();
  const utils = trpc.useUtils();

  const handleAnalyzeProject = async () => {
    if (!projectQuery.trim()) {
      toast.error("Please enter a project name or symbol");
      return;
    }

    setIsAnalyzing(true);
    try {
      // First try to find the project
      const searchResult = await searchProjectsMutation.mutateAsync({
        searchQuery: projectQuery.trim(),
        limit: 1,
      });

      if (!searchResult.success || searchResult.data.length === 0) {
        toast.error("Project not found. Try a different name or symbol.");
        setIsAnalyzing(false);
        return;
      }

      const project = searchResult.data[0];

      // Get competitive analysis
      const result = await utils.crypto.getCompetitiveAnalysis.fetch({
        projectSlug: project.slug,
      });

      if (result.success) {
        setAnalysis(result);
        toast.success(`Analysis complete for ${project.name}`);
      } else {
        toast.error("Failed to get competitive analysis");
      }
    } catch (error) {
      console.error("Analysis error:", error);
      toast.error("Failed to analyze project. Please try again.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getTrendIndicator = (current?: number, competitor?: number) => {
    if (!current || !competitor) return null;

    const diff = current - competitor;
    const percentage = Math.abs((diff / competitor) * 100);

    if (percentage < 5) return null;

    return {
      isPositive: diff > 0,
      percentage: percentage.toFixed(0),
    };
  };

  const getMindshareColor = (mindshare?: number) => {
    if (!mindshare) return "text-gray-500";
    if (mindshare > 80) return "text-green-600";
    if (mindshare > 60) return "text-blue-600";
    if (mindshare > 40) return "text-yellow-600";
    return "text-red-500";
  };

  return (
    <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-app-headline flex items-center gap-2">
          <BarChart3 className="w-5 h-5 text-app-main" />
          Competitive Analysis
        </CardTitle>
        <p className="text-sm text-app-headline/60">
          Compare projects against their competitors and analyze sector
          positioning
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Search Controls */}
        <div className="flex gap-2">
          <Input
            placeholder="Enter project name or symbol (e.g., Bitcoin, $BTC)"
            value={projectQuery}
            onChange={(e) => setProjectQuery(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleAnalyzeProject()}
            className="flex-1 bg-app-background border-app-stroke text-app-headline placeholder:text-app-headline/50"
            disabled={isAnalyzing}
          />
          <Button
            onClick={handleAnalyzeProject}
            disabled={isAnalyzing || !projectQuery.trim()}
            className="bg-app-main text-app-secondary hover:bg-app-highlight px-4"
          >
            {isAnalyzing ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Search className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* Analysis Results */}
        {analysis && (
          <div className="space-y-6">
            {/* Project Overview */}
            {analysis.project && (
              <div className="p-4 bg-app-background rounded-lg border border-app-stroke/30">
                <div className="flex items-start justify-between gap-4 mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="text-lg font-semibold text-app-headline">
                        {analysis.project.name}
                      </h4>
                      {analysis.project.symbol && (
                        <Badge
                          variant="outline"
                          className="text-app-headline/60 border-app-stroke"
                        >
                          {analysis.project.symbol}
                        </Badge>
                      )}
                    </div>

                    {analysis.project.description && (
                      <p className="text-sm text-app-headline/70 mb-3 line-clamp-2">
                        {analysis.project.description}
                      </p>
                    )}

                    <div className="flex items-center gap-4">
                      {analysis.project.sector && (
                        <Badge className="bg-app-main/10 text-app-main border-app-main/30">
                          {analysis.project.sector}
                        </Badge>
                      )}

                      {analysis.project.mindshare && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-app-headline/60">
                            Mindshare:
                          </span>
                          <span
                            className={`text-sm font-medium ${getMindshareColor(analysis.project.mindshare)}`}
                          >
                            {analysis.project.mindshare}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    {analysis.project.twitterUrl && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() =>
                          window.open(analysis.project?.twitterUrl, "_blank")
                        }
                        className="text-app-headline/50 hover:text-app-main p-2"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Competitive Positioning */}
            {analysis.competitors.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-app-headline mb-3">
                  Top Competitors ({analysis.competitors.length})
                </h5>

                <div className="space-y-3">
                  {analysis.competitors.slice(0, 5).map((competitor, index) => {
                    const mindshareCompare = getTrendIndicator(
                      analysis.project?.mindshare,
                      competitor.mindshare
                    );

                    return (
                      <div
                        key={competitor.slug}
                        className="flex items-center justify-between p-3 bg-app-background rounded border border-app-stroke/20 hover:border-app-stroke/40 transition-colors"
                      >
                        <div className="flex items-center gap-3 flex-1">
                          <div className="flex-shrink-0 w-6 h-6 bg-app-stroke/20 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-app-headline/60">
                              {index + 1}
                            </span>
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h6 className="text-sm font-medium text-app-headline truncate">
                                {competitor.name}
                              </h6>
                              {competitor.symbol && (
                                <Badge
                                  variant="outline"
                                  className="text-xs px-1 py-0 text-app-headline/50"
                                >
                                  {competitor.symbol}
                                </Badge>
                              )}
                            </div>

                            {competitor.sector && (
                              <p className="text-xs text-app-headline/50">
                                {competitor.sector}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-3 flex-shrink-0">
                          {competitor.mindshare && (
                            <div className="text-right">
                              <div className="flex items-center gap-1">
                                <span
                                  className={`text-sm font-medium ${getMindshareColor(competitor.mindshare)}`}
                                >
                                  {competitor.mindshare}
                                </span>
                                {mindshareCompare && (
                                  <span
                                    className={`flex items-center gap-0.5 text-xs ${
                                      mindshareCompare.isPositive
                                        ? "text-green-600"
                                        : "text-red-600"
                                    }`}
                                  >
                                    {mindshareCompare.isPositive ? (
                                      <TrendingUp className="w-3 h-3" />
                                    ) : (
                                      <TrendingDown className="w-3 h-3" />
                                    )}
                                    {mindshareCompare.percentage}%
                                  </span>
                                )}
                              </div>
                              <p className="text-xs text-app-headline/50">
                                mindshare
                              </p>
                            </div>
                          )}

                          {competitor.twitterUrl && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() =>
                                window.open(competitor.twitterUrl, "_blank")
                              }
                              className="text-app-headline/50 hover:text-app-main p-1 min-h-[32px] min-w-[32px]"
                            >
                              <ExternalLink className="w-3 h-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Sector Trends */}
            {analysis.sectorTrends.length > 0 && (
              <div>
                <h5 className="text-sm font-medium text-app-headline mb-3">
                  Sector Trends
                </h5>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                  {analysis.sectorTrends.slice(0, 6).map((project) => (
                    <div
                      key={project.slug}
                      className="p-3 bg-app-background rounded border border-app-stroke/20"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h6 className="text-sm font-medium text-app-headline truncate">
                          {project.name}
                        </h6>
                        {project.trending && (
                          <TrendingUp className="w-3 h-3 text-green-500 flex-shrink-0" />
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        {project.symbol && (
                          <Badge
                            variant="outline"
                            className="text-xs px-1 py-0"
                          >
                            {project.symbol}
                          </Badge>
                        )}

                        {project.mindshare && (
                          <span
                            className={`text-xs font-medium ${getMindshareColor(project.mindshare)}`}
                          >
                            {project.mindshare} mindshare
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Optional: Radar Chart for Visual Comparison */}
            {analysis.project && analysis.competitors.length > 0 && (
              <div className="pt-4 border-t border-app-stroke/30">
                <SectorRadarChart
                  targetProject={analysis.project}
                  competitors={analysis.competitors.slice(0, 3)}
                />
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {!analysis && !isAnalyzing && (
          <div className="text-center py-8">
            <BarChart3 className="w-12 h-12 text-app-main/30 mx-auto mb-3" />
            <p className="text-sm text-app-headline/60 mb-2">
              Ready to analyze your competition?
            </p>
            <p className="text-xs text-app-headline/40 max-w-md mx-auto">
              Enter a crypto project name or symbol to see how it compares
              against competitors and sector trends.
            </p>
          </div>
        )}

        {/* Analysis Tips */}
        <div className="bg-app-main/5 border border-app-main/20 rounded-lg p-4">
          <h5 className="text-sm font-medium text-app-headline mb-2">
            🎯 Analysis Tips
          </h5>
          <ul className="text-xs text-app-headline/70 space-y-1">
            <li>• Compare mindshare scores to gauge market attention</li>
            <li>• Look for competitors with higher engagement rates</li>
            <li>• Monitor sector trends to identify rising competitors</li>
            <li>• Use insights to improve your own social strategy</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
