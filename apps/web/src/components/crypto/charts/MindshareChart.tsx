"use client";

import { TrendingDown, TrendingUp } from "lucide-react";
import { useMemo } from "react";

interface Project {
  name: string;
  symbol?: string;
  mindshare?: number;
  sector?: string;
}

interface MindshareChartProps {
  projects: Project[];
  timeframe: "_7Days" | "_30Days";
  compact?: boolean;
}

export default function MindshareChart({
  projects,
  timeframe,
  compact = false,
}: MindshareChartProps) {
  // Generate mock time-series data for demonstration
  const chartData = useMemo(() => {
    const days = timeframe === "_7Days" ? 7 : 30;
    const dataPoints = compact ? 7 : days;

    return projects.slice(0, 3).map((project, projectIndex) => {
      const baseValue = project.mindshare || 50;
      const color =
        [
          "#6366f1", // Indigo
          "#10b981", // Emerald
          "#f59e0b", // Amber
        ][projectIndex] || "#6366f1";

      const points = Array.from({ length: dataPoints }, (_, i) => {
        // Generate realistic fluctuation around the base mindshare value
        const variance = 15 + Math.random() * 10;
        const trend = Math.sin((i / dataPoints) * Math.PI * 2) * variance;
        const noise = (Math.random() - 0.5) * 10;
        return Math.max(0, Math.min(100, baseValue + trend + noise));
      });

      return {
        name: project.name,
        symbol: project.symbol,
        color,
        points,
        currentValue: points[points.length - 1],
        change: points[points.length - 1] - points[0],
      };
    });
  }, [projects, timeframe, compact]);

  const maxValue = Math.max(...chartData.flatMap((d) => d.points), 100);
  const chartHeight = compact ? 60 : 120;
  const chartWidth = 100;

  if (chartData.length === 0) return null;

  return (
    <div className="space-y-3">
      {!compact && (
        <div className="flex items-center justify-between">
          <h5 className="text-sm font-medium text-app-headline">
            Mindshare Trends ({timeframe === "_7Days" ? "7 days" : "30 days"})
          </h5>
        </div>
      )}

      {/* Chart */}
      <div className="relative bg-app-background rounded-lg border border-app-stroke/30 p-4">
        <svg
          width="100%"
          height={chartHeight}
          viewBox={`0 0 ${chartWidth} ${chartHeight}`}
          className="overflow-visible"
        >
          {/* Grid lines */}
          {!compact &&
            [0, 25, 50, 75, 100].map((value) => {
              const y = chartHeight - (value / maxValue) * chartHeight;
              return (
                <g key={value}>
                  <line
                    x1="0"
                    y1={y}
                    x2={chartWidth}
                    y2={y}
                    stroke="currentColor"
                    strokeWidth="0.5"
                    className="text-app-stroke/30"
                    strokeDasharray="2,2"
                  />
                  <text
                    x="-5"
                    y={y + 2}
                    className="text-app-headline/40 text-[8px]"
                    textAnchor="end"
                  >
                    {value}
                  </text>
                </g>
              );
            })}

          {/* Chart lines */}
          {chartData.map((series, index) => {
            const pathData = series.points
              .map((value, i) => {
                const x = (i / (series.points.length - 1)) * chartWidth;
                const y = chartHeight - (value / maxValue) * chartHeight;
                return `${i === 0 ? "M" : "L"} ${x} ${y}`;
              })
              .join(" ");

            return (
              <g key={series.name}>
                {/* Line */}
                <path
                  d={pathData}
                  fill="none"
                  stroke={series.color}
                  strokeWidth={compact ? "1.5" : "2"}
                  className="drop-shadow-sm"
                />

                {/* Data points */}
                {!compact &&
                  series.points.map((value, i) => {
                    const x = (i / (series.points.length - 1)) * chartWidth;
                    const y = chartHeight - (value / maxValue) * chartHeight;
                    return (
                      <circle
                        key={i}
                        cx={x}
                        cy={y}
                        r="1.5"
                        fill={series.color}
                        className="drop-shadow-sm"
                      />
                    );
                  })}
              </g>
            );
          })}
        </svg>
      </div>

      {/* Legend */}
      <div
        className={`grid gap-2 ${compact ? "grid-cols-1" : "grid-cols-1 sm:grid-cols-3"}`}
      >
        {chartData.map((series) => (
          <div
            key={series.name}
            className="flex items-center justify-between p-2 bg-app-background rounded border border-app-stroke/20"
          >
            <div className="flex items-center gap-2 min-w-0">
              <div
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: series.color }}
              />
              <div className="min-w-0">
                <p className="text-xs font-medium text-app-headline truncate">
                  {series.name}
                </p>
                {series.symbol && !compact && (
                  <p className="text-[10px] text-app-headline/50">
                    {series.symbol}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-1 flex-shrink-0">
              <span className="text-xs font-medium text-app-headline">
                {series.currentValue.toFixed(0)}
              </span>
              {Math.abs(series.change) > 1 && (
                <span
                  className={`flex items-center gap-0.5 text-[10px] ${
                    series.change > 0 ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {series.change > 0 ? (
                    <TrendingUp className="w-2.5 h-2.5" />
                  ) : (
                    <TrendingDown className="w-2.5 h-2.5" />
                  )}
                  {Math.abs(series.change).toFixed(0)}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
