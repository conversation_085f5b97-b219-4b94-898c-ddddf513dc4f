"use client";

import { <PERSON><PERSON><PERSON>, Crown, Lock, Sparkles, Users, Zap } from "lucide-react";
import type { ReactNode } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { trpc } from "@/utils/trpc";
import UpgradePrompt from "./upgrade-prompt";

interface FeatureGateProps {
  feature: string;
  children: ReactNode;
  fallback?: ReactNode;
  showUpgradePrompt?: boolean;
  className?: string;
}

export default function FeatureGate({
  feature,
  children,
  fallback,
  showUpgradePrompt = true,
  className = "",
}: FeatureGateProps) {
  const { data: featureCheck, isLoading } = trpc.billing.canUseFeature.useQuery(
    {
      feature: feature as any,
    }
  );
  const { data: subscription } = trpc.billing.getSubscription.useQuery();

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-32 bg-app-stroke rounded"></div>
      </div>
    );
  }

  // If user can use the feature, render children
  if (featureCheck?.allowed) {
    return <>{children}</>;
  }

  // If custom fallback provided, use it
  if (fallback) {
    return <>{fallback}</>;
  }

  // Default: show feature gate with upgrade prompt
  const currentPlan = subscription?.isClerkBilling
    ? subscription.clerkPlan.name
    : subscription?.legacyPlan.name || "free";

  const featureNames = {
    AI_CALLS: "AI Calls",
    IMAGE_GENERATIONS: "Image Generations",
    MONITORED_ACCOUNTS: "Monitored Accounts",
    MENTIONS_PER_MONTH: "Monthly Mentions",
    STORAGE_GB: "Storage",
    TEAM_MEMBERS: "Team Members",
    COOKIE_API_CALLS: "Crypto Data Calls",
  };

  const featureName =
    featureNames[feature as keyof typeof featureNames] || feature;

  const planIcons = {
    free: Zap,
    "reply-guy": Zap,
    "reply-god": Crown,
    team: Users,
  };

  const PlanIcon = planIcons[currentPlan as keyof typeof planIcons] || Lock;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Feature Locked Card */}
      <Card className="border-2 border-dashed border-app-stroke bg-app-background/50">
        <CardHeader className="text-center pb-4">
          <div className="mx-auto w-12 h-12 rounded-full bg-app-stroke/20 flex items-center justify-center mb-3">
            <Lock className="w-6 h-6 text-app-sub-headline" />
          </div>
          <CardTitle className="text-app-headline flex items-center justify-center gap-2">
            <PlanIcon className="w-5 h-5" />
            {featureName} Limit Reached
          </CardTitle>
          <CardDescription className="text-app-sub-headline">
            You've reached your {featureName.toLowerCase()} limit for the{" "}
            {currentPlan.replace("-", " ")} plan.
            {featureCheck?.limit && featureCheck.limit > 0 && (
              <span className="block mt-1 font-medium">
                {featureCheck.currentUsage} / {featureCheck.limit} used
              </span>
            )}
          </CardDescription>
        </CardHeader>

        <CardContent className="text-center space-y-4">
          <div className="p-4 rounded-lg bg-app-card border border-app-stroke">
            <div className="flex items-center justify-center gap-2 text-sm text-app-sub-headline mb-2">
              <Sparkles className="w-4 h-4" />
              Upgrade to unlock more
            </div>
            <div className="text-xs text-app-sub-headline opacity-75">
              Your usage will reset at the start of your next billing cycle
            </div>
          </div>

          {/* Quick upgrade options */}
          <div className="grid gap-2 text-sm">
            {currentPlan === "free" && (
              <div className="flex items-center justify-between p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-900 dark:text-blue-100">
                    Reply Guy
                  </span>
                </div>
                <div className="text-blue-700 dark:text-blue-300 font-semibold">
                  $9/month
                </div>
              </div>
            )}

            {(currentPlan === "free" || currentPlan === "reply-guy") && (
              <div className="flex items-center justify-between p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800">
                <div className="flex items-center gap-2">
                  <Crown className="w-4 h-4 text-purple-600" />
                  <span className="font-medium text-purple-900 dark:text-purple-100">
                    Reply God
                  </span>
                </div>
                <div className="text-purple-700 dark:text-purple-300 font-semibold">
                  $29/month
                </div>
              </div>
            )}

            {currentPlan !== "team" && (
              <div className="flex items-center justify-between p-3 rounded-lg bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-orange-600" />
                  <span className="font-medium text-orange-900 dark:text-orange-100">
                    Team
                  </span>
                </div>
                <div className="text-orange-700 dark:text-orange-300 font-semibold">
                  $99/month
                </div>
              </div>
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => (window.location.href = "/pricing")}
          >
            <ArrowRight className="w-4 h-4 mr-2" />
            View All Plans
          </Button>
        </CardContent>
      </Card>

      {/* Upgrade Prompt */}
      {showUpgradePrompt && (
        <UpgradePrompt
          currentPlan={currentPlan}
          feature={feature}
          currentUsage={featureCheck?.currentUsage}
          limit={featureCheck?.limit}
        />
      )}
    </div>
  );
}
