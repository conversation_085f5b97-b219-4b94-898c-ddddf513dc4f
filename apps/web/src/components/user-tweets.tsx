"use client";

import { formatDistanceToNow } from "date-fns";
import { Loader2, Search, Twitter } from "lucide-react";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { trpc } from "@/utils/trpc";

export function UserTweets() {
  const [username, setUsername] = useState("");
  const [includeReplies, setIncludeReplies] = useState(false);
  const [includeRetweets, setIncludeRetweets] = useState(true);
  const [cursor, setCursor] = useState<string | undefined>();

  const [data, setData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<any>(null);

  const fetchTweetsMutation = trpc.twitter.fetchUserLastTweets.useMutation({
    onMutate: () => {
      setIsLoading(true);
      setError(null);
    },
    onSuccess: (result) => {
      setData(result);
      setIsLoading(false);
    },
    onError: (err) => {
      setError(err);
      setIsLoading(false);
    },
  });

  const handleSearch = async () => {
    if (!username) return;
    setCursor(undefined);
    await fetchTweetsMutation.mutateAsync({
      username,
      limit: 20,
      includeReplies,
      includeRetweets,
    });
  };

  const loadMore = async () => {
    if (data?.nextCursor && username) {
      setCursor(data.nextCursor);
      await fetchTweetsMutation.mutateAsync({
        username,
        cursor: data.nextCursor,
        limit: 20,
        includeReplies,
        includeRetweets,
      });
    }
  };

  // Auto-fetch when parameters change
  React.useEffect(() => {
    if (username) {
      fetchTweetsMutation.mutate({
        username,
        cursor,
        limit: 20,
        includeReplies,
        includeRetweets,
      });
    }
  }, [username, cursor, includeReplies, includeRetweets]);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>User Tweet Fetcher</CardTitle>
        <CardDescription>
          Fetch and analyze recent tweets from any Twitter user
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search Controls */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="username">Twitter Username</Label>
              <Input
                id="username"
                placeholder="@username or username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={!username || isLoading}
              className="mt-6"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              Search
            </Button>
          </div>

          {/* Filter Options */}
          <div className="flex gap-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="replies"
                checked={includeReplies}
                onCheckedChange={setIncludeReplies}
              />
              <Label htmlFor="replies">Include Replies</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="retweets"
                checked={includeRetweets}
                onCheckedChange={setIncludeRetweets}
              />
              <Label htmlFor="retweets">Include Retweets</Label>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <div className="bg-red-50 text-red-800 p-4 rounded-md">
              {error.message}
            </div>
          )}

          {/* Results */}
          {data && (
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Found {data.resultCount} tweets
              </div>

              {/* Tweet List */}
              <div className="space-y-4">
                {data.data.map((tweet: any) => (
                  <Card key={tweet.id} className="p-4">
                    <div className="flex gap-3">
                      {tweet.author.profilePicture && (
                        <img
                          src={tweet.author.profilePicture}
                          alt={tweet.author.name}
                          className="w-12 h-12 rounded-full"
                        />
                      )}
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">
                            {tweet.author.name}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            @{tweet.author.userName}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            ·{" "}
                            {formatDistanceToNow(new Date(tweet.createdAt), {
                              addSuffix: true,
                            })}
                          </span>
                        </div>
                        <p className="whitespace-pre-wrap">{tweet.text}</p>
                        <div className="flex gap-4 text-sm text-muted-foreground">
                          <span>💬 {tweet.replyCount || 0}</span>
                          <span>🔁 {tweet.retweetCount || 0}</span>
                          <span>❤️ {tweet.likeCount || 0}</span>
                          {tweet.viewCount && <span>👁️ {tweet.viewCount}</span>}
                        </div>
                        <a
                          href={
                            tweet.url ||
                            `https://x.com/${tweet.author.userName}/status/${tweet.id}`
                          }
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-500 hover:underline text-sm inline-flex items-center gap-1"
                        >
                          View on Twitter
                          <Twitter className="h-3 w-3" />
                        </a>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              {/* Load More */}
              {data.hasNextPage && (
                <div className="flex justify-center">
                  <Button
                    onClick={loadMore}
                    disabled={isLoading}
                    variant="outline"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : null}
                    Load More
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
