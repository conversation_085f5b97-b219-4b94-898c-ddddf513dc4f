import React from "react";
import Logo from "@/components/logo";
import FeaturedGrid from "../featured-grid";
import MovingTextCarousel from "../moving-text-carousel";

export function FeaturesSection() {
  console.log("🔍 FeaturesSection: Rendering features section");

  return (
    <section className="min-h-[100vh] flex items-center justify-center relative py-3 sm:py-4 px-3 sm:px-4 bg-app-background">
      <div className="w-full max-w-[96vw] sm:max-w-[95vw] md:max-w-[92vw] lg:max-w-[90vw] min-h-[88vh] sm:min-h-[90vh] mx-auto bg-app-secondary flex flex-col gap-4 sm:gap-6 lg:flex-row items-stretch justify-center rounded-[12px] sm:rounded-[16px] md:rounded-[20px] p-4 sm:p-5 md:p-6 pb-[2.5rem] sm:pb-[3rem] md:pb-[6rem] relative overflow-hidden">
        {/* Text Content */}
        <div className="lg:w-[48%] w-full flex flex-col justify-between min-h-[100px] sm:min-h-[120px] lg:min-h-full">
          <h1 className="text-app-headline text-[clamp(1.5rem,6vw,4.25rem)] w-full lg:w-[90%] leading-tight mb-4 sm:mb-6 lg:mb-0 font-medium">
            Powerful AI Features for Twitter Success
          </h1>
          <Logo
            size={100}
            href="/"
            showText={false}
            className="hidden lg:block w-[80px] md:w-[100px] h-auto"
          />
        </div>

        {/* Featured Grid */}
        <div className="lg:w-[52%] w-full flex-1 flex items-center justify-center rounded-[8px] sm:rounded-[12px] min-h-[400px] sm:min-h-[450px] lg:min-h-full">
          <FeaturedGrid />
        </div>

        {/* Moving Text Carousel */}
        <div className="absolute bottom-0 left-0 w-full h-[40px] sm:h-[45px] md:h-[50px] rounded-b-[12px] sm:rounded-b-[16px] md:rounded-b-[20px] overflow-hidden">
          <MovingTextCarousel />
        </div>
      </div>
    </section>
  );
}
