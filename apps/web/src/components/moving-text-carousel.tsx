"use client";

import React from "react";

const MovingTextCarousel = () => {
  const texts = [
    "AI Mentions",
    "Multi-Model AI",
    "Real-Time Analytics",
    "Viral Prediction",
  ];

  return (
    <div className="w-full h-full bg-app-main overflow-hidden relative">
      <div className="absolute inset-0 flex items-center">
        <div className="animate-smooth-scroll flex gap-8 md:gap-12 whitespace-nowrap min-w-max">
          {/* Create enough repetitions for seamless scrolling */}
          {Array(6)
            .fill(texts)
            .flat()
            .map((text, index) => (
              <React.Fragment key={index}>
                <span className="text-app-secondary font-sans text-[20px] md:text-[28px] font-medium tracking-wide flex-shrink-0">
                  {text}
                </span>
                <span className="text-app-secondary font-sans text-[20px] md:text-[28px] font-medium flex-shrink-0">
                  —
                </span>
              </React.Fragment>
            ))}
        </div>
      </div>

      {/* Gradient masks for smooth edges */}
      <div className="absolute left-0 top-0 w-8 h-full bg-gradient-to-r from-app-main to-transparent z-10"></div>
      <div className="absolute right-0 top-0 w-8 h-full bg-gradient-to-l from-app-main to-transparent z-10"></div>
    </div>
  );
};

export default MovingTextCarousel;
