/**
 * Benji tRPC Router
 *
 * Handles AI agent operations and interactions
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { FeatureType } from "../../prisma/generated/index.js";
import { BenjiAgent, getBenjiForUser } from "../lib/benji-agent";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import mem0Service from "../lib/mem0-service";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";

export const benjiRouter = createTRPCRouter({
  /**
   * Generate AI response for a mention
   */
  generateMentionResponse: protectedProcedure
    .input(
      z.object({
        mentionId: z.string(),
        mentionContent: z.string(),
        authorInfo: z
          .object({
            name: z.string(),
            handle: z.string(),
            avatarUrl: z.string().optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check rate limits
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.AI_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log("🚀 Benji Router: Starting mention response generation");
        console.log("📝 Benji Router: Input:", {
          mentionId: input.mentionId,
          contentLength: input.mentionContent.length,
          authorHandle: input.authorInfo?.handle,
        });

        // Get user's Benji agent
        const benji = await getBenjiForUser(ctx.userId);

        console.log("🤖 Benji Router: Agent created, generating response...");

        // Generate response
        const result = await benji.generateMentionResponse(
          input.mentionContent,
          {
            mentionId: input.mentionId,
            mentionContent: input.mentionContent,
            authorInfo: input.authorInfo,
          }
        );

        console.log("✅ Benji Router: Response generation started");

        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1, {
          mentionId: input.mentionId,
          model: "benji",
        });

        // Convert streaming result to response
        console.log("📡 Benji Router: Processing text stream...");
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        console.log("✅ Benji Router: Response generated:", {
          length: responseText.length,
          preview: responseText.substring(0, 100) + "...",
        });

        // Store the response in database if we have mentionId
        if (input.mentionId && responseText) {
          try {
            await ctx.prisma.aIResponse.create({
              data: {
                mentionId: input.mentionId,
                userId: ctx.userId,
                content: responseText,
                model: "gemini-2.5-flash",
                tokensUsed: (await result.usage)?.totalTokens || 0,
                processingTime: Date.now(),
              },
            });
          } catch (dbError) {
            console.error("Failed to store AI response:", dbError);
            // Don't fail the request if storage fails
          }
        }

        return {
          response: responseText,
          model: "gemini-2.5-flash",
          usage: await result.usage,
        };
      } catch (error) {
        console.error("Benji mention response error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate AI response",
        });
      }
    }),

  /**
   * Generate quick reply for any tweet
   */
  generateQuickReply: protectedProcedure
    .input(
      z.object({
        tweetUrl: z.string().url(),
        tweetContent: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check rate limits
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.AI_CALLS,
        1
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        // Get user's Benji agent
        const benji = await getBenjiForUser(ctx.userId);

        // Generate response
        const result = await benji.generateQuickReply(input.tweetContent);

        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1, {
          tweetUrl: input.tweetUrl,
          type: "quick-reply",
        });

        // Convert streaming result to response
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        return {
          response: responseText,
          model: "gemini-2.5-flash",
          usage: await result.usage,
        };
      } catch (error) {
        console.error("Benji quick reply error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate quick reply",
        });
      }
    }),

  /**
   * Calculate bullish score for a tweet
   */
  calculateBullishScore: protectedProcedure
    .input(
      z.object({
        content: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const benji = await getBenjiForUser(ctx.userId);
        const score = await benji.calculateBullishScore(input.content);

        return { score };
      } catch (error) {
        console.error("Bullish score calculation error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to calculate bullish score",
        });
      }
    }),

  /**
   * Get user's AI usage statistics
   */
  getUsageStats: protectedProcedure.query(async ({ ctx }) => {
    const currentPeriod = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const stats = await ctx.prisma.usageLog.groupBy({
      by: ["feature"],
      where: {
        userId: ctx.userId,
        billingPeriod: currentPeriod,
      },
      _sum: {
        amount: true,
      },
    });

    const usage = stats.reduce(
      (acc, stat) => {
        acc[stat.feature] = stat._sum.amount || 0;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      currentPeriod,
      usage,
    };
  }),

  /**
   * Get available models and capabilities
   */
  getCapabilities: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.prisma.user.findUnique({
      where: { id: ctx.userId },
      include: { plan: true },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    return {
      plan: user.plan.name,
      models: [
        {
          id: "gemini-2.5-flash",
          name: "Gemini 2.5 Flash",
          available: true,
          description: "Fast and efficient for general tasks",
        },
        {
          id: "gemini-2.5-pro",
          name: "Gemini 2.5 Pro",
          available: user.plan.name !== "reply-guy",
          description: "Advanced reasoning for complex tasks",
        },
        {
          id: "openai-o3",
          name: "OpenAI o1-mini",
          available: user.plan.name === "team-plan",
          description: "Latest reasoning model",
        },
      ],
      tools: [
        {
          name: "Web Search",
          description: "Real-time web search via xAI",
          enabled: true,
        },
        {
          name: "Knowledge Search",
          description: "Semantic search via Exa",
          enabled: true,
        },
        {
          name: "Image Generation",
          description: "AI image generation via DALL-E",
          enabled: user.plan.name !== "reply-guy",
        },
      ],
    };
  }),

  /**
   * Generate enhanced mention response with crypto market intelligence
   */
  generateEnhancedMentionResponse: protectedProcedure
    .input(
      z.object({
        mentionId: z.string(),
        mentionContent: z.string(),
        authorInfo: z
          .object({
            name: z.string(),
            handle: z.string(),
            avatarUrl: z.string().optional(),
          })
          .optional(),
        monitoredAccountInfo: z
          .object({
            name: z.string(),
            handle: z.string(),
            avatarUrl: z.string().optional(),
          })
          .optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Check rate limits (enhanced responses cost more)
      const rateLimit = await checkRateLimit(
        ctx.userId,
        FeatureType.AI_CALLS,
        2
      );
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log(
          "🚀 Benji Router: Starting enhanced mention response with market intelligence"
        );
        console.log("📝 Benji Router: Input:", {
          mentionId: input.mentionId,
          contentLength: input.mentionContent.length,
          authorHandle: input.authorInfo?.handle,
          monitoredAccount: input.monitoredAccountInfo?.handle,
        });

        // Get user's Benji agent
        const benji = await getBenjiForUser(ctx.userId);

        console.log(
          "🤖 Benji Router: Agent created, generating enhanced response with market intelligence..."
        );

        // Generate enhanced response with market intelligence
        const result =
          await benji.generateEnhancedMentionResponseWithIntelligence(
            input.mentionContent,
            {
              mentionId: input.mentionId,
              mentionContent: input.mentionContent,
              authorInfo: input.authorInfo,
              monitoredAccountInfo: input.monitoredAccountInfo,
            }
          );

        console.log("✅ Benji Router: Enhanced response generation started");

        // Record usage (2 calls since this is enhanced with market data + o3 model)
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 2, {
          mentionId: input.mentionId,
          model: "benji-enhanced-o3",
          features: ["market_intelligence", "openai_o3"],
          actualModel: "openaiO3",
        });

        // Convert streaming result to response
        console.log("📡 Benji Router: Processing enhanced text stream...");
        let responseText = "";
        for await (const chunk of result.textStream) {
          responseText += chunk;
        }

        console.log("✅ Benji Router: Enhanced response generated:", {
          length: responseText.length,
          mentionId: input.mentionId,
        });

        return {
          success: true,
          response: responseText,
          mentionId: input.mentionId,
          enhanced: true,
          model: "openaiO3",
          features: ["market_intelligence", "openai_o3"],
        };
      } catch (error) {
        console.error(
          "❌ Benji Router: Enhanced response generation error:",
          error
        );

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate enhanced response. Please try again.",
        });
      }
    }),

  /**
   * Search user's conversation memories
   */
  searchMemories: protectedProcedure
    .input(
      z.object({
        query: z.string(),
        limit: z.number().min(1).max(10).default(5),
        memoryType: z.string().optional(),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log(
          "🔍 Benji Router: Searching memories for user:",
          ctx.userId
        );

        const memories = await mem0Service.searchMemories(ctx.userId, {
          query: input.query,
          limit: input.limit,
          memoryType: input.memoryType,
        });

        return {
          memories: memories.map((memory) => ({
            id: memory.id,
            content: memory.content,
            similarity: memory.similarity,
            memoryType: memory.memoryType,
            createdAt: memory.createdAt,
          })),
          count: memories.length,
        };
      } catch (error) {
        console.error("Memory search error:", error);

        // Handle rate limiting errors specifically
        if (error instanceof Error && error.message.includes("rate limit")) {
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: error.message,
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to search memories",
        });
      }
    }),

  /**
   * Get all user's memories (for dashboard/management)
   */
  getUserMemories: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(50),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log("📋 Benji Router: Getting memories for user:", ctx.userId);

        const memories = await mem0Service.getUserMemories(
          ctx.userId,
          input.limit
        );

        return {
          memories: memories.map((memory) => ({
            id: memory.id,
            content: memory.content,
            memoryType: memory.memoryType,
            createdAt: memory.createdAt,
            metadata: memory.metadata,
          })),
          count: memories.length,
        };
      } catch (error) {
        console.error("Get user memories error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to retrieve memories",
        });
      }
    }),

  /**
   * Delete specific memories (for privacy/GDPR compliance)
   */
  deleteMemories: protectedProcedure
    .input(
      z.object({
        memoryIds: z.array(z.string()).min(1).max(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        console.log(
          "🗑️ Benji Router: Deleting memories for user:",
          ctx.userId,
          input.memoryIds
        );

        await mem0Service.deleteMemories(ctx.userId, input.memoryIds);

        return {
          success: true,
          deletedCount: input.memoryIds.length,
        };
      } catch (error) {
        console.error("Delete memories error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete memories",
        });
      }
    }),

  /**
   * Get memory service health status
   */
  getMemoryHealth: protectedProcedure.query(async ({ ctx }) => {
    try {
      const health = await mem0Service.healthCheck();

      return {
        status: health.status,
        error: health.error,
        userId: ctx.userId,
      };
    } catch (error) {
      console.error("Memory health check error:", error);
      return {
        status: "unhealthy" as const,
        error: error instanceof Error ? error.message : "Unknown error",
        userId: ctx.userId,
      };
    }
  }),

  /**
   * Get memory-enhanced context for a query (for testing/debugging)
   */
  getMemoryContext: protectedProcedure
    .input(
      z.object({
        query: z.string(),
        limit: z.number().min(1).max(5).default(3),
      })
    )
    .query(async ({ ctx, input }) => {
      try {
        console.log(
          "🧠 Benji Router: Getting memory context for user:",
          ctx.userId
        );

        const memoryContext = await mem0Service.getUserMemoryContext(
          ctx.userId,
          input.query,
          input.limit
        );

        return {
          context: memoryContext,
          hasMemories: memoryContext.length > 0,
          query: input.query,
        };
      } catch (error) {
        console.error("Get memory context error:", error);

        // Handle rate limiting errors specifically
        if (error instanceof Error && error.message.includes("rate limit")) {
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: error.message,
          });
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get memory context",
        });
      }
    }),
});
