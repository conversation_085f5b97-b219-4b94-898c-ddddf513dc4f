/**
 * Notepad Router for BuddyChip tRPC API
 *
 * Handles notepad operations for enhanced mention response crafting:
 * - Creating and managing notepad sessions
 * - Source management and citation tracking
 * - Draft saving and versioning
 * - Research session management
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { FeatureType } from "../../prisma/generated/index.js";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";

export const notepadRouter = createTRPCRouter({
  /**
   * Get or create notepad for a mention
   */
  getOrCreate: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log(
          "📝 Notepad: Getting or creating notepad for mention:",
          input.mentionId
        );

        // Verify the mention belongs to the user
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        // Try to find existing notepad
        let notepad = await ctx.prisma.mentionNotepad.findUnique({
          where: {
            mentionId: input.mentionId,
          },
          include: {
            sources: {
              orderBy: {
                extractedAt: "desc",
              },
            },
            drafts: {
              where: {
                isActive: true,
              },
              orderBy: {
                version: "desc",
              },
            },
          },
        });

        // Create notepad if it doesn't exist
        if (!notepad) {
          console.log(
            "📝 Notepad: Creating new notepad for mention:",
            input.mentionId
          );
          notepad = await ctx.prisma.mentionNotepad.create({
            data: {
              mentionId: input.mentionId,
              userId: ctx.userId!,
              title: `Response for @${mention.authorHandle}`,
            },
            include: {
              sources: {
                orderBy: {
                  extractedAt: "desc",
                },
              },
              drafts: {
                where: {
                  isActive: true,
                },
                orderBy: {
                  version: "desc",
                },
              },
            },
          });
        } else {
          // Update last used timestamp
          await ctx.prisma.mentionNotepad.update({
            where: {
              id: notepad.id,
            },
            data: {
              lastUsedAt: new Date(),
            },
          });
        }

        return {
          success: true,
          notepad: {
            id: notepad.id,
            mentionId: notepad.mentionId,
            title: notepad.title,
            notes: notepad.notes,
            draftResponse: notepad.draftResponse,
            finalResponse: notepad.finalResponse,
            researchQuery: notepad.researchQuery,
            researchContext: notepad.researchContext,
            isActive: notepad.isActive,
            lastUsedAt: notepad.lastUsedAt,
            createdAt: notepad.createdAt,
            updatedAt: notepad.updatedAt,
            sources: notepad.sources.map((source) => ({
              id: source.id,
              title: source.title,
              url: source.url,
              content: source.content,
              sourceType: source.sourceType,
              searchTool: source.searchTool,
              relevanceScore: source.relevanceScore,
              credibilityScore: source.credibilityScore,
              publishedAt: source.publishedAt,
              extractedAt: source.extractedAt,
              isBookmarked: source.isBookmarked,
              userRating: source.userRating,
              userNotes: source.userNotes,
              createdAt: source.createdAt,
              updatedAt: source.updatedAt,
            })),
            drafts: notepad.drafts.map((draft) => ({
              id: draft.id,
              content: draft.content,
              version: draft.version,
              title: draft.title,
              generatedBy: draft.generatedBy,
              model: draft.model,
              prompt: draft.prompt,
              tokensUsed: draft.tokensUsed,
              isActive: draft.isActive,
              isFavorite: draft.isFavorite,
              createdAt: draft.createdAt,
              updatedAt: draft.updatedAt,
            })),
          },
        };
      } catch (error) {
        console.error("❌ Notepad: Error getting/creating notepad:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get or create notepad",
        });
      }
    }),

  /**
   * Update notepad content (notes, draft response, etc.)
   */
  update: protectedProcedure
    .input(
      z.object({
        notepadId: z.string().min(1, "Notepad ID is required"),
        title: z.string().optional(),
        notes: z.string().optional(),
        draftResponse: z.string().optional(),
        finalResponse: z.string().optional(),
        researchQuery: z.string().optional(),
        researchContext: z.any().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log("📝 Notepad: Updating notepad:", input.notepadId);

        // Verify the notepad belongs to the user
        const notepad = await ctx.prisma.mentionNotepad.findFirst({
          where: {
            id: input.notepadId,
            userId: ctx.userId!,
          },
        });

        if (!notepad) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Notepad not found",
          });
        }

        // Update notepad
        const updatedNotepad = await ctx.prisma.mentionNotepad.update({
          where: {
            id: input.notepadId,
          },
          data: {
            ...(input.title !== undefined && { title: input.title }),
            ...(input.notes !== undefined && { notes: input.notes }),
            ...(input.draftResponse !== undefined && {
              draftResponse: input.draftResponse,
            }),
            ...(input.finalResponse !== undefined && {
              finalResponse: input.finalResponse,
            }),
            ...(input.researchQuery !== undefined && {
              researchQuery: input.researchQuery,
            }),
            ...(input.researchContext !== undefined && {
              researchContext: input.researchContext,
            }),
            lastUsedAt: new Date(),
          },
        });

        return {
          success: true,
          notepad: {
            id: updatedNotepad.id,
            mentionId: updatedNotepad.mentionId,
            title: updatedNotepad.title,
            notes: updatedNotepad.notes,
            draftResponse: updatedNotepad.draftResponse,
            finalResponse: updatedNotepad.finalResponse,
            researchQuery: updatedNotepad.researchQuery,
            researchContext: updatedNotepad.researchContext,
            isActive: updatedNotepad.isActive,
            lastUsedAt: updatedNotepad.lastUsedAt,
            createdAt: updatedNotepad.createdAt,
            updatedAt: updatedNotepad.updatedAt,
          },
        };
      } catch (error) {
        console.error("❌ Notepad: Error updating notepad:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update notepad",
        });
      }
    }),

  /**
   * Add source to notepad
   */
  addSource: protectedProcedure
    .input(
      z.object({
        notepadId: z.string().min(1, "Notepad ID is required"),
        title: z.string().min(1, "Source title is required"),
        url: z.string().url("Valid URL is required"),
        content: z.string().optional(),
        sourceType: z.enum(["web", "news", "research", "social"]),
        searchTool: z.enum(["xai", "exa", "manual"]),
        relevanceScore: z.number().min(0).max(1).optional(),
        credibilityScore: z.number().min(0).max(1).optional(),
        publishedAt: z.date().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log("📝 Notepad: Adding source to notepad:", input.notepadId);

        // Verify the notepad belongs to the user
        const notepad = await ctx.prisma.mentionNotepad.findFirst({
          where: {
            id: input.notepadId,
            userId: ctx.userId!,
          },
        });

        if (!notepad) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Notepad not found",
          });
        }

        // Create source
        const source = await ctx.prisma.notepadSource.create({
          data: {
            notepadId: input.notepadId,
            title: input.title,
            url: input.url,
            content: input.content,
            sourceType: input.sourceType,
            searchTool: input.searchTool,
            relevanceScore: input.relevanceScore,
            credibilityScore: input.credibilityScore,
            publishedAt: input.publishedAt,
          },
        });

        return {
          success: true,
          source: {
            id: source.id,
            title: source.title,
            url: source.url,
            content: source.content,
            sourceType: source.sourceType,
            searchTool: source.searchTool,
            relevanceScore: source.relevanceScore,
            credibilityScore: source.credibilityScore,
            publishedAt: source.publishedAt,
            extractedAt: source.extractedAt,
            isBookmarked: source.isBookmarked,
            userRating: source.userRating,
            userNotes: source.userNotes,
            createdAt: source.createdAt,
            updatedAt: source.updatedAt,
          },
        };
      } catch (error) {
        console.error("❌ Notepad: Error adding source:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add source",
        });
      }
    }),

  /**
   * Update source (bookmark, rating, notes)
   */
  updateSource: protectedProcedure
    .input(
      z.object({
        sourceId: z.string().min(1, "Source ID is required"),
        isBookmarked: z.boolean().optional(),
        userRating: z.number().min(1).max(5).optional(),
        userNotes: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log("📝 Notepad: Updating source:", input.sourceId);

        // Verify the source belongs to the user's notepad
        const source = await ctx.prisma.notepadSource.findFirst({
          where: {
            id: input.sourceId,
            notepad: {
              userId: ctx.userId!,
            },
          },
        });

        if (!source) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Source not found",
          });
        }

        // Update source
        const updatedSource = await ctx.prisma.notepadSource.update({
          where: {
            id: input.sourceId,
          },
          data: {
            ...(input.isBookmarked !== undefined && {
              isBookmarked: input.isBookmarked,
            }),
            ...(input.userRating !== undefined && {
              userRating: input.userRating,
            }),
            ...(input.userNotes !== undefined && {
              userNotes: input.userNotes,
            }),
          },
        });

        return {
          success: true,
          source: {
            id: updatedSource.id,
            title: updatedSource.title,
            url: updatedSource.url,
            content: updatedSource.content,
            sourceType: updatedSource.sourceType,
            searchTool: updatedSource.searchTool,
            relevanceScore: updatedSource.relevanceScore,
            credibilityScore: updatedSource.credibilityScore,
            publishedAt: updatedSource.publishedAt,
            extractedAt: updatedSource.extractedAt,
            isBookmarked: updatedSource.isBookmarked,
            userRating: updatedSource.userRating,
            userNotes: updatedSource.userNotes,
            createdAt: updatedSource.createdAt,
            updatedAt: updatedSource.updatedAt,
          },
        };
      } catch (error) {
        console.error("❌ Notepad: Error updating source:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update source",
        });
      }
    }),

  /**
   * Delete source
   */
  deleteSource: protectedProcedure
    .input(
      z.object({
        sourceId: z.string().min(1, "Source ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log("📝 Notepad: Deleting source:", input.sourceId);

        // Verify the source belongs to the user's notepad
        const source = await ctx.prisma.notepadSource.findFirst({
          where: {
            id: input.sourceId,
            notepad: {
              userId: ctx.userId!,
            },
          },
        });

        if (!source) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Source not found",
          });
        }

        // Delete source
        await ctx.prisma.notepadSource.delete({
          where: {
            id: input.sourceId,
          },
        });

        return {
          success: true,
          message: "Source deleted successfully",
        };
      } catch (error) {
        console.error("❌ Notepad: Error deleting source:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete source",
        });
      }
    }),

  /**
   * Create new draft
   */
  createDraft: protectedProcedure
    .input(
      z.object({
        notepadId: z.string().min(1, "Notepad ID is required"),
        content: z.string().min(1, "Draft content is required"),
        title: z.string().optional(),
        generatedBy: z.enum(["user", "ai", "enhanced"]).optional(),
        model: z.string().optional(),
        prompt: z.string().optional(),
        tokensUsed: z.number().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log("📝 Notepad: Creating draft for notepad:", input.notepadId);

        // Verify the notepad belongs to the user
        const notepad = await ctx.prisma.mentionNotepad.findFirst({
          where: {
            id: input.notepadId,
            userId: ctx.userId!,
          },
        });

        if (!notepad) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Notepad not found",
          });
        }

        // Get next version number
        const lastDraft = await ctx.prisma.notepadDraft.findFirst({
          where: {
            notepadId: input.notepadId,
          },
          orderBy: {
            version: "desc",
          },
        });

        const nextVersion = (lastDraft?.version || 0) + 1;

        // Create draft
        const draft = await ctx.prisma.notepadDraft.create({
          data: {
            notepadId: input.notepadId,
            content: input.content,
            version: nextVersion,
            title: input.title || `Draft v${nextVersion}`,
            generatedBy: input.generatedBy || "user",
            model: input.model,
            prompt: input.prompt,
            tokensUsed: input.tokensUsed,
          },
        });

        return {
          success: true,
          draft: {
            id: draft.id,
            content: draft.content,
            version: draft.version,
            title: draft.title,
            generatedBy: draft.generatedBy,
            model: draft.model,
            prompt: draft.prompt,
            tokensUsed: draft.tokensUsed,
            isActive: draft.isActive,
            isFavorite: draft.isFavorite,
            createdAt: draft.createdAt,
            updatedAt: draft.updatedAt,
          },
        };
      } catch (error) {
        console.error("❌ Notepad: Error creating draft:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create draft",
        });
      }
    }),
});
