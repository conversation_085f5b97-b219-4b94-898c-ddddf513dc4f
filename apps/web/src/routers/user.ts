/**
 * User Router for BuddyChip tRPC API
 *
 * Handles user profile, subscription, and usage operations
 */

import { clerkClient } from "@clerk/nextjs/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";
import type { PlanFeature } from "../../prisma/generated";
import {
  logSecurityEvent,
  sanitizeMetadata,
  sanitizeSystemPrompt,
  sanitizeUserName,
} from "../lib/security-utils";
import {
  createTRPCRouter,
  protectedProcedure,
  publicProcedure,
} from "../lib/trpc";
import {
  canUserUseFeature,
  getOrCreateUser,
  getUserStats,
  logUsage,
} from "../lib/user-service";

export const userRouter = createTRPCRouter({
  /**
   * Get current user profile with plan and usage data
   */
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "You must be logged in to access this resource",
      });
    }

    try {
      // Get Clerk user data for fallback
      const clerk = await clerkClient();
      const clerkUser = await clerk.users.getUser(ctx.userId);

      // Get or create user in our database
      const user = await getOrCreateUser(ctx.userId, {
        email: clerkUser.emailAddresses[0]?.emailAddress,
        name:
          clerkUser.firstName && clerkUser.lastName
            ? `${clerkUser.firstName} ${clerkUser.lastName}`
            : clerkUser.firstName || undefined,
        avatar: clerkUser.imageUrl,
      });

      // Get user statistics
      const stats = await getUserStats(ctx.userId);

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          lastActiveAt: user.lastActiveAt,
        },
        plan: {
          id: user.plan.id,
          name: user.plan.name,
          displayName: user.plan.displayName,
          description: user.plan.description,
          price: user.plan.price,
          features: user.plan.features.map((f: PlanFeature) => ({
            feature: f.feature,
            limit: f.limit,
          })),
        },
        stats,
      };
    } catch (error) {
      console.error("Error getting user profile:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get user profile",
      });
    }
  }),

  /**
   * Get user's current usage for all features
   */
  getUsage: protectedProcedure.query(async ({ ctx }) => {
    console.log("🔍 UserRouter: Getting usage for user:", ctx.userId);

    try {
      const features = [
        "AI_CALLS",
        "IMAGE_GENERATIONS",
        "MONITORED_ACCOUNTS",
        "MENTIONS_PER_MONTH",
      ];

      const usage = await Promise.all(
        features.map(async (feature) => {
          const result = await canUserUseFeature(ctx.userId!, feature);
          return {
            feature,
            ...result,
          };
        })
      );

      return usage;
    } catch (error) {
      console.error("Error getting user usage:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get user usage",
      });
    }
  }),

  /**
   * Check if user can perform a specific action
   */
  canUseFeature: protectedProcedure
    .input(
      z.object({
        feature: z.enum([
          "AI_CALLS",
          "IMAGE_GENERATIONS",
          "MONITORED_ACCOUNTS",
          "MENTIONS_PER_MONTH",
          "STORAGE_GB",
          "TEAM_MEMBERS",
        ]),
      })
    )
    .query(async ({ ctx, input }) => {
      console.log(
        "🔍 UserRouter: Checking feature usage for user:",
        ctx.userId,
        "feature:",
        input.feature
      );

      try {
        return await canUserUseFeature(ctx.userId, input.feature);
      } catch (error) {
        console.error("Error checking feature usage:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check feature availability",
        });
      }
    }),

  /**
   * Update user profile information
   */
  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1).max(100).optional(), // Add length validation
        // Note: email and avatar are managed by Clerk
      })
    )
    .mutation(async ({ ctx, input }) => {
      console.log("🔍 UserRouter: Updating profile for user:", ctx.userId);

      try {
        // Sanitize input
        const sanitizedName = input.name
          ? sanitizeUserName(input.name)
          : undefined;

        // Update in Clerk if name is provided
        if (sanitizedName) {
          const [firstName, ...lastNameParts] = sanitizedName.split(" ");
          const clerk = await clerkClient();
          await clerk.users.updateUser(ctx.userId, {
            firstName,
            lastName: lastNameParts.join(" ") || undefined,
          });
        }

        // Update in our database
        const user = await ctx.prisma.user.update({
          where: { id: ctx.userId },
          data: {
            name: sanitizedName,
            lastActiveAt: new Date(),
          },
        });

        return {
          success: true,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
          },
        };
      } catch (error) {
        console.error("Error updating user profile:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update profile",
        });
      }
    }),

  /**
   * Log feature usage (for tracking rate limits)
   */
  logUsage: protectedProcedure
    .input(
      z.object({
        feature: z.enum([
          "AI_CALLS",
          "IMAGE_GENERATIONS",
          "MONITORED_ACCOUNTS",
          "MENTIONS_PER_MONTH",
          "STORAGE_GB",
          "TEAM_MEMBERS",
        ]),
        amount: z.number().min(1).max(1000).default(1), // Add reasonable limits
        metadata: z.record(z.any()).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      console.log(
        "🔍 UserRouter: Logging usage for user:",
        ctx.userId,
        "feature:",
        input.feature,
        "amount:",
        input.amount
      );

      try {
        // Check if user can use this feature
        const canUse = await canUserUseFeature(ctx.userId, input.feature);
        if (!canUse.allowed) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: `Usage limit exceeded for ${input.feature}. Current: ${canUse.currentUsage}, Limit: ${canUse.limit}`,
          });
        }

        // Sanitize metadata and log the usage
        const sanitizedMetadata = input.metadata
          ? sanitizeMetadata(input.metadata)
          : undefined;
        await logUsage(
          ctx.userId,
          input.feature,
          input.amount,
          sanitizedMetadata
        );

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error("Error logging usage:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to log usage",
        });
      }
    }),

  /**
   * Get current user's personality settings
   */
  getPersonality: protectedProcedure.query(async ({ ctx }) => {
    try {
      const user = await ctx.prisma.user.findUnique({
        where: { id: ctx.userId! },
        select: {
          personalityId: true,
          customSystemPrompt: true,
          useFirstPerson: true,
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return {
        personalityId: user.personalityId,
        customSystemPrompt: user.customSystemPrompt,
        useFirstPerson: user.useFirstPerson,
      };
    } catch (error) {
      console.error("Error getting user personality:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get personality settings",
      });
    }
  }),

  /**
   * Get all available personality profiles
   */
  getPersonalities: publicProcedure.query(async ({ ctx }) => {
    try {
      const personalities = await ctx.prisma.personalityProfile.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          description: true,
        },
        orderBy: { name: "asc" },
      });

      return personalities;
    } catch (error) {
      console.error("Error getting personalities:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get personality profiles",
      });
    }
  }),

  /**
   * Update user's personality settings
   */
  updatePersonality: protectedProcedure
    .input(
      z.object({
        personalityId: z.string().nullable(),
        customSystemPrompt: z.string().max(2000).optional(), // Add length limit
        useFirstPerson: z.boolean().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // Sanitize system prompt
        const sanitizedPrompt = input.customSystemPrompt
          ? sanitizeSystemPrompt(input.customSystemPrompt)
          : null;

        console.log(
          "🔍 UserRouter: Updating personality for user:",
          ctx.userId
        );

        const updateData: any = {
          personalityId: input.personalityId,
          customSystemPrompt: sanitizedPrompt,
          lastActiveAt: new Date(),
        };

        // Only update useFirstPerson if provided
        if (input.useFirstPerson !== undefined) {
          updateData.useFirstPerson = input.useFirstPerson;
        }

        await ctx.prisma.user.update({
          where: { id: ctx.userId! },
          data: updateData,
        });

        console.log("✅ UserRouter: Personality updated successfully");
        return { success: true };
      } catch (error) {
        console.error("❌ UserRouter: Error updating personality:", error);
        logSecurityEvent({
          type: "INVALID_INPUT",
          userId: ctx.userId!,
          details: `Personality update failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update personality settings",
        });
      }
    }),

  /**
   * Get available AI models
   */
  getAIModels: publicProcedure.query(async ({ ctx }) => {
    try {
      console.log("🤖 Fetching AI models from database...");

      const models = await ctx.prisma.aIModel.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          displayName: true,
          description: true,
          provider: true,
          modelId: true,
          costTier: true,
          speed: true,
        },
        orderBy: { name: "asc" },
      });

      console.log(
        `✅ Found ${models.length} AI models:`,
        models.map((m) => m.name)
      );
      return models;
    } catch (error) {
      console.error("❌ Error getting AI models:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get AI models",
      });
    }
  }),

  /**
   * Get user's selected AI model
   */
  getSelectedModel: protectedProcedure.query(async ({ ctx }) => {
    try {
      console.log(`🔍 Getting selected model for user: ${ctx.userId}`);

      const user = await ctx.prisma.user.findUnique({
        where: { id: ctx.userId! },
        select: {
          modelId: true,
          selectedModel: {
            select: {
              id: true,
              name: true,
              displayName: true,
              description: true,
              provider: true,
              modelId: true,
              costTier: true,
              speed: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      console.log(
        `✅ User's selected model:`,
        user.selectedModel?.name || "None"
      );
      return user.selectedModel || null;
    } catch (error) {
      console.error("❌ Error getting selected model:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get selected model",
      });
    }
  }),

  /**
   * Update user's selected AI model
   */
  updateSelectedModel: protectedProcedure
    .input(
      z.object({
        modelId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      try {
        console.log(
          `🔄 Updating model for user ${ctx.userId} to: ${input.modelId || "None"}`
        );

        // Validate model exists if provided
        if (input.modelId) {
          const model = await ctx.prisma.aIModel.findUnique({
            where: { id: input.modelId },
          });

          if (!model) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "AI model not found",
            });
          }

          if (!model.isActive) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Selected AI model is not active",
            });
          }
        }

        // Update user's selected model
        await ctx.prisma.user.update({
          where: { id: ctx.userId! },
          data: {
            modelId: input.modelId || null,
            lastActiveAt: new Date(),
          },
        });

        console.log(`✅ Successfully updated user's model selection`);
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error("❌ Error updating selected model:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update selected model",
        });
      }
    }),
});
