/**
 * Mentions Router for BuddyChip tRPC API - Fixed version matching schema
 *
 * Handles mention operations including:
 * - Fetching latest mentions for dashboard
 * - Paginated mentions for reply-guy page
 * - Creating mentions from URLs (Quick Reply)
 * - Updating bullish scores and metadata
 * - Mention management (delete, enhance, mark as replied)
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { FeatureType } from "../../prisma/generated/index.js";
import { checkRateLimit, recordUsage } from "../lib/db-utils";
import { MentionSyncService } from "../lib/mention-sync-service";
import { performanceMonitor } from "../lib/performance-monitor";
import { createTRPCRouter, protectedProcedure } from "../lib/trpc";
import { twitterClient } from "../lib/twitter-client";

export const mentionsRouter = createTRPCRouter({
  /**
   * Get latest mentions for dashboard display
   */
  getLatest: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(10),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mentions = await performanceMonitor.trackQuery(
          "mentions.getLatest",
          () =>
            ctx.prisma.mention.findMany({
              where: {
                userId: ctx.userId!,
                archived: false, // Only show non-archived mentions
              },
              orderBy: {
                createdAt: "desc",
              },
              take: input.limit,
              include: {
                account: {
                  select: {
                    id: true,
                    twitterHandle: true,
                    displayName: true,
                    avatarUrl: true,
                    isActive: true,
                  },
                },
              },
            }),
          { limit: input.limit },
          ctx.userId
        );

        return {
          success: true,
          mentions: mentions.map((mention) => ({
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatarUrl,
            tweetUrl: mention.link,
            platform: "twitter",
            createdAt: mention.createdAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.processed, // Using processed field as proxy
            account: mention.account
              ? {
                  id: mention.account.id,
                  handle: mention.account.twitterHandle,
                  displayName: mention.account.displayName,
                  avatar: mention.account.avatarUrl,
                  isActive: mention.account.isActive,
                }
              : null,
          })),
        };
      } catch (error: any) {
        console.error("Get latest mentions error:", error);

        // Handle specific database connection errors
        if (
          error.message?.includes("Can't reach database server") ||
          error.message?.includes("Database is temporarily unavailable") ||
          error.name === "PrismaClientInitializationError"
        ) {
          throw new TRPCError({
            code: "SERVICE_UNAVAILABLE",
            message:
              "Database is temporarily unavailable. Please try again in a few moments.",
          });
        }

        // Handle other known Prisma errors
        if (error.name === "PrismaClientKnownRequestError") {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Database query failed. Please check your request.",
          });
        }

        // Generic fallback
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch latest mentions",
        });
      }
    }),

  /**
   * Get all mentions with pagination (for reply-guy page)
   */
  getAll: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        accountId: z.string().optional(),
        hasAIResponse: z.boolean().optional(),
        includeResponses: z.boolean().default(false), // Only load responses when explicitly requested
        includeAccount: z.boolean().default(true), // Allow disabling account data if not needed
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        console.log("🔍 Mentions.getAll: Starting query with input:", input);
        console.log("🔍 Mentions.getAll: User ID:", ctx.userId);
        console.log("🔍 Mentions.getAll: Prisma client exists:", !!ctx.prisma);

        const where: any = {
          userId: ctx.userId!,
          archived: false, // Only show non-archived mentions by default
        };

        // Add filters
        if (input.accountId) {
          where.accountId = input.accountId;
        }
        if (input.hasAIResponse !== undefined) {
          where.processed = input.hasAIResponse; // Using processed field as proxy
        }

        // Optimized cursor-based pagination using timestamp + id
        if (input.cursor) {
          try {
            const [timestamp, id] = input.cursor.split("_");
            if (timestamp && id) {
              // Use compound cursor for better performance with existing indexes
              where.OR = [
                {
                  createdAt: { lt: new Date(timestamp) },
                },
                {
                  createdAt: new Date(timestamp),
                  id: { lt: id },
                },
              ];
            } else {
              // Fallback to simple ID cursor for backwards compatibility
              where.id = { lt: input.cursor };
            }
          } catch (cursorError) {
            console.warn(
              "Invalid cursor format, falling back to ID-based pagination:",
              cursorError
            );
            where.id = { lt: input.cursor };
          }
        }

        console.log(
          "🔍 Mentions.getAll: Where clause:",
          JSON.stringify(where, null, 2)
        );
        console.log("🔍 Mentions.getAll: About to execute findMany query...");

        const mentions = await performanceMonitor.trackQuery(
          "mentions.getAll",
          () =>
            ctx.prisma.mention.findMany({
              where,
              orderBy: {
                createdAt: "desc",
              },
              take: input.limit + 1, // Take one extra to determine if there are more results
              include: {
                // Conditionally include account data
                ...(input.includeAccount && {
                  account: {
                    select: {
                      id: true,
                      twitterHandle: true,
                      displayName: true,
                      avatarUrl: true,
                      isActive: true,
                    },
                  },
                }),
                // Only load responses when explicitly requested (eliminates N+1)
                ...(input.includeResponses && {
                  responses: {
                    orderBy: {
                      createdAt: "desc",
                    },
                    take: 3, // Get latest 3 AI responses
                    select: {
                      id: true,
                      content: true,
                      model: true,
                      confidence: true,
                      tokensUsed: true,
                      createdAt: true,
                    },
                  },
                }),
              },
            }),
          {
            limit: input.limit,
            includeResponses: input.includeResponses,
            includeAccount: input.includeAccount,
            accountId: input.accountId,
            hasAIResponse: input.hasAIResponse,
          },
          ctx.userId
        );

        console.log(
          "✅ Mentions.getAll: Query completed successfully, found",
          mentions.length,
          "mentions"
        );

        // Check if there are more results
        const hasNextPage = mentions.length > input.limit;
        const items = hasNextPage ? mentions.slice(0, -1) : mentions;
        // Generate compound cursor for better pagination performance
        const nextCursor = hasNextPage
          ? `${items[items.length - 1].createdAt.toISOString()}_${items[items.length - 1].id}`
          : null;

        return {
          success: true,
          mentions: items.map((mention) => ({
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatarUrl,
            tweetUrl: mention.link,
            platform: "twitter",
            createdAt: mention.createdAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.processed,
            account:
              input.includeAccount && mention.account
                ? {
                    id: mention.account.id,
                    handle: mention.account.twitterHandle,
                    displayName: mention.account.displayName,
                    avatar: mention.account.avatarUrl,
                    isActive: mention.account.isActive,
                  }
                : null,
            responses: input.includeResponses
              ? mention.responses?.map((response) => ({
                  id: response.id,
                  content: response.content,
                  model: response.model,
                  confidence: response.confidence,
                  tokensUsed: response.tokensUsed,
                  createdAt: response.createdAt,
                })) || []
              : [], // Return empty array when responses not loaded
          })),
          nextCursor,
          hasNextPage,
          // Add metadata about what was included for debugging
          meta: {
            includeResponses: input.includeResponses,
            includeAccount: input.includeAccount,
            totalItems: items.length,
          },
        };
      } catch (error) {
        console.error("❌ Mentions.getAll: Error occurred:", error);
        console.error("❌ Mentions.getAll: Error details:", {
          message: error instanceof Error ? error.message : String(error),
          code: (error as any)?.code,
          meta: (error as any)?.meta,
          stack: error instanceof Error ? error.stack : undefined,
        });
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch mentions",
        });
      }
    }),

  /**
   * Get tweets for a monitored account with advanced filtering
   * Can fetch mentions, user tweets, replies, or any combination
   */
  getAccountTweets: protectedProcedure
    .input(
      z.object({
        accountId: z.string(),
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        // Tweet type filters
        tweetTypes: z
          .object({
            mentions: z.boolean().default(true), // Tweets mentioning the account
            userTweets: z.boolean().default(true), // Tweets by the account
            replies: z.boolean().default(true), // Include replies (for userTweets)
            retweets: z.boolean().default(true), // Include retweets (for userTweets)
          })
          .optional(),
        // Additional filters
        hasAIResponse: z.boolean().optional(),
        archived: z.boolean().default(false),
        // Date range filters
        startDate: z.date().optional(),
        endDate: z.date().optional(),
        // Sorting
        sortBy: z
          .enum(["createdAt", "bullishScore", "importanceScore"])
          .default("createdAt"),
        sortOrder: z.enum(["asc", "desc"]).default("desc"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        // Verify account ownership
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found or access denied",
          });
        }

        // Build where clause based on filters
        const where: any = {
          archived: input.archived,
        };

        // Tweet type filtering
        const typeConditions = [];
        const types = input.tweetTypes || {
          mentions: true,
          userTweets: true,
          replies: true,
          retweets: true,
        };

        if (types.mentions) {
          // Mentions: tweets that mention this account but aren't by this account
          typeConditions.push({
            accountId: input.accountId,
            isUserTweet: false,
          });
        }

        if (types.userTweets) {
          // User tweets: tweets by this account
          const userTweetCondition: any = {
            authorHandle: account.twitterHandle,
            isUserTweet: true,
          };

          // Apply reply/retweet filters only to user tweets
          if (!types.replies) {
            userTweetCondition.isReply = false;
          }
          // Note: We'd need to add isRetweet field to fully support this filter

          typeConditions.push(userTweetCondition);
        }

        // Combine type conditions with OR
        if (typeConditions.length > 0) {
          where.OR = typeConditions;
        }

        // Additional filters
        if (input.hasAIResponse !== undefined) {
          where.processed = input.hasAIResponse;
        }

        if (input.startDate || input.endDate) {
          where.mentionedAt = {};
          if (input.startDate) where.mentionedAt.gte = input.startDate;
          if (input.endDate) where.mentionedAt.lte = input.endDate;
        }

        // Pagination
        if (input.cursor) {
          where.id = { lt: input.cursor };
        }

        // Build orderBy
        const orderBy: any = {};
        orderBy[input.sortBy] = input.sortOrder;

        const tweets = await ctx.prisma.mention.findMany({
          where,
          orderBy,
          take: input.limit + 1,
          include: {
            account: true,
            responses: {
              orderBy: { createdAt: "desc" },
              take: 3,
            },
          },
        });

        const hasNextPage = tweets.length > input.limit;
        const items = hasNextPage ? tweets.slice(0, -1) : tweets;
        const nextCursor = hasNextPage ? items[items.length - 1].id : null;

        return {
          success: true,
          tweets: items.map((tweet) => ({
            id: tweet.id,
            content: tweet.content,
            authorHandle: tweet.authorHandle,
            authorName: tweet.authorName,
            authorAvatar: tweet.authorAvatarUrl,
            tweetUrl: tweet.link,
            createdAt: tweet.createdAt,
            bullishScore: tweet.bullishScore,
            importanceScore: tweet.importanceScore,
            keywords: tweet.keywords,
            hasAIResponse: tweet.processed,
            isUserTweet: tweet.isUserTweet,
            isReply: tweet.isReply,
            tweetType: tweet.isUserTweet ? "userTweet" : "mention",
            account: tweet.account,
            responses: tweet.responses.map((r) => ({
              id: r.id,
              content: r.content,
              model: r.model,
              confidence: r.confidence,
              createdAt: r.createdAt,
            })),
          })),
          nextCursor,
          hasNextPage,
          filters: {
            applied: input.tweetTypes || {
              mentions: true,
              userTweets: true,
              replies: true,
              retweets: true,
            },
            account: {
              id: account.id,
              handle: account.twitterHandle,
              displayName: account.displayName,
            },
          },
        };
      } catch (error) {
        console.error("Get account tweets error:", error);
        throw error instanceof TRPCError
          ? error
          : new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to fetch account tweets",
            });
      }
    }),

  /**
   * Create mention from Twitter/X URL (for Quick Reply feature)
   */
  createFromUrl: protectedProcedure
    .input(
      z.object({
        url: z.string().url("Please provide a valid URL"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Validate it's a Twitter URL
        if (!twitterClient.validateTwitterUrl(input.url)) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Please provide a valid Twitter or X.com URL",
          });
        }

        // Extract tweet ID
        const tweetId = twitterClient.extractTweetId(input.url);
        if (!tweetId) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Could not extract tweet ID from URL",
          });
        }

        // Check if mention already exists
        const existingMention = await ctx.prisma.mention.findFirst({
          where: {
            id: tweetId,
            userId: ctx.userId!,
          },
        });

        if (existingMention) {
          return {
            success: true,
            mention: {
              id: existingMention.id,
              content: existingMention.content,
              authorHandle: existingMention.authorHandle,
              authorName: existingMention.authorName,
              authorAvatar: existingMention.authorAvatarUrl,
              tweetUrl: existingMention.link,
              platform: "twitter",
              createdAt: existingMention.createdAt,
              bullishScore: existingMention.bullishScore,
              keywords: existingMention.keywords,
              hasAIResponse: existingMention.processed,
            },
            isNew: false,
            message: "This tweet is already in your mentions",
          };
        }

        // Fetch tweet content from Twitter
        const tweet = await twitterClient.getTweetFromUrl(input.url);
        if (!tweet) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message:
              "Could not fetch tweet content. The tweet may be private, deleted, or the URL is invalid.",
          });
        }

        // Create the mention
        const mention = await ctx.prisma.mention.create({
          data: {
            id: tweet.id, // Use tweet ID as primary key
            userId: ctx.userId!,
            link: twitterClient.normalizeTwitterUrl(input.url),
            content: tweet.text,
            authorHandle: tweet.author.userName,
            authorName: tweet.author.name,
            authorAvatarUrl: tweet.author.profilePicture,
            mentionedAt: new Date(tweet.createdAt),
            processed: false,
          },
        });

        return {
          success: true,
          mention: {
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatarUrl,
            tweetUrl: mention.link,
            platform: "twitter",
            createdAt: mention.createdAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.processed,
          },
          isNew: true,
          message: "Tweet added to your mentions",
        };
      } catch (error) {
        console.error("Create mention from URL error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create mention from URL",
        });
      }
    }),

  /**
   * Delete a mention
   */
  delete: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().cuid("Invalid mention ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the mention belongs to the user
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        // Delete the mention and related AI responses
        await ctx.prisma.mention.delete({
          where: {
            id: input.mentionId,
          },
        });

        return {
          success: true,
          message: "Mention deleted successfully",
        };
      } catch (error) {
        console.error("Delete mention error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete mention",
        });
      }
    }),

  /**
   * Enhance mention with AI-powered reply generation using tools
   */
  enhance: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          "🔧 Enhance: Starting enhanced reply generation for mention:",
          input.mentionId
        );

        // Verify the mention belongs to the user and get the monitored account info
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          include: {
            account: true, // Include the monitored account info
          },
        });

        if (!mention) {
          console.error("❌ Enhance: Mention not found:", input.mentionId);
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        console.log(
          "✅ Enhance: Mention found, content length:",
          mention.content.length
        );

        // Check rate limits for AI calls
        const rateLimit = await checkRateLimit(
          ctx.userId!,
          FeatureType.AI_CALLS,
          1
        );
        if (!rateLimit.allowed) {
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: `AI calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
          });
        }

        // Use AI for enhanced reply generation with tools
        console.log("🤖 Enhance: Loading Benji AI agent with tools...");
        const { getBenjiForUser } = await import("../lib/benji-agent");
        const benji = await getBenjiForUser(ctx.userId!);

        console.log(
          "🚀 Enhance: Generating enhanced AI response with XAI/Exa search tools..."
        );
        // Generate enhanced AI response using tools for improved context and intelligence
        const result = await benji.generateEnhancedMentionResponse(
          mention.content,
          {
            mentionId: input.mentionId,
            mentionContent: mention.content,
            // Pass the MONITORED ACCOUNT info so AI knows whose identity to assume
            monitoredAccountInfo: mention.account
              ? {
                  name:
                    mention.account.displayName ||
                    mention.account.twitterHandle,
                  handle: mention.account.twitterHandle,
                  avatarUrl: mention.account.avatarUrl || undefined,
                }
              : undefined,
            // Also pass mention author info for context
            mentionAuthorInfo: {
              name: mention.authorName,
              handle: mention.authorHandle,
              avatarUrl: mention.authorAvatarUrl || undefined,
            },
          }
        );

        console.log(
          "✅ Enhance: AI response generation started, processing stream..."
        );

        // Process the streaming result
        let responseText = "";
        try {
          console.log("🔄 Enhance: Starting to process text stream...");
          let chunkCount = 0;
          for await (const chunk of result.textStream) {
            chunkCount++;
            console.log(
              `📝 Enhance: Chunk ${chunkCount}:`,
              chunk.length > 50 ? chunk.substring(0, 50) + "..." : chunk
            );
            responseText += chunk;
          }
          console.log(
            `✅ Enhance: Stream processing complete. Total chunks: ${chunkCount}, Total length: ${responseText.length}`
          );
        } catch (streamError) {
          console.error("❌ Enhance: Streaming error:", streamError);
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to process AI response stream",
          });
        }

        if (!responseText.trim()) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "AI generated empty response. Please try again.",
          });
        }

        console.log("✅ Enhance: Response generated:", {
          length: responseText.length,
          preview: responseText.substring(0, 100) + "...",
        });

        // Store the enhanced AI response in database
        let aiResponse;
        try {
          aiResponse = await ctx.prisma.aIResponse.create({
            data: {
              mentionId: input.mentionId,
              userId: ctx.userId!,
              content: responseText,
              model: "enhanced-benji-o3",
              tokensUsed: (await result.usage)?.totalTokens || 0,
              processingTime: Date.now(),
            },
          });
        } catch (dbError) {
          console.error(
            "❌ Enhance: Database error creating AI response:",
            dbError
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to save enhanced response",
          });
        }

        // Record usage for rate limiting and update mention
        try {
          await Promise.all([
            recordUsage(ctx.userId!, FeatureType.AI_CALLS, 1, {
              mentionId: input.mentionId,
              model: "enhanced-benji-o3",
              enhanced: true,
              actualModel: "openaiO3",
            }),
            ctx.prisma.mention.update({
              where: { id: input.mentionId },
              data: { processed: true },
            }),
          ]);
        } catch (updateError) {
          console.error(
            "❌ Enhance: Error updating usage/mention:",
            updateError
          );
          // Don't fail the request if usage recording fails, response was created successfully
        }

        return {
          success: true,
          response: {
            id: aiResponse.id,
            content: responseText,
            model: "enhanced-benji-o3",
            actualModel: "openaiO3",
            tokensUsed: aiResponse.tokensUsed,
          },
          message: "Enhanced AI response generated successfully",
        };
      } catch (error) {
        console.error("Enhance mention error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to enhance mention",
        });
      }
    }),

  /**
   * Mark mention as having AI response
   */
  markAsReplied: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Update the mention
        const updatedMention = await ctx.prisma.mention.update({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          data: {
            processed: true,
          },
        });

        return {
          success: true,
          mention: updatedMention,
          message: "Mention marked as replied",
        };
      } catch (error) {
        console.error("Mark as replied error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to mark mention as replied",
        });
      }
    }),

  /**
   * Get mention by ID with all details
   */
  getById: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          include: {
            account: {
              select: {
                id: true,
                twitterHandle: true,
                displayName: true,
                avatarUrl: true,
                isActive: true,
              },
            },
            responses: {
              orderBy: {
                createdAt: "desc",
              },
            },
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found",
          });
        }

        return {
          success: true,
          mention: {
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatarUrl,
            tweetUrl: mention.link,
            platform: "twitter",
            createdAt: mention.createdAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.processed,
            account: mention.account
              ? {
                  id: mention.account.id,
                  handle: mention.account.twitterHandle,
                  displayName: mention.account.displayName,
                  avatar: mention.account.avatarUrl,
                  isActive: mention.account.isActive,
                }
              : null,
            responses: mention.responses.map((response) => ({
              id: response.id,
              content: response.content,
              model: response.model,
              confidence: response.confidence,
              tokensUsed: response.tokensUsed,
              createdAt: response.createdAt,
            })),
          },
        };
      } catch (error) {
        console.error("Get mention by ID error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch mention details",
        });
      }
    }),

  /**
   * Update bullish score manually
   */
  updateBullishScore: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
        bullishScore: z.number().min(0).max(100),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const updatedMention = await ctx.prisma.mention.update({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          data: {
            bullishScore: input.bullishScore,
          },
        });

        return {
          success: true,
          mention: updatedMention,
          message: "Bullish score updated successfully",
        };
      } catch (error) {
        console.error("Update bullish score error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update bullish score",
        });
      }
    }),

  /**
   * Update importance score manually
   */
  updateImportanceScore: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
        importanceScore: z.number().min(0).max(100),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const updatedMention = await ctx.prisma.mention.update({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
          data: {
            importanceScore: input.importanceScore,
          },
        });

        return {
          success: true,
          mention: updatedMention,
          message: "Importance score updated successfully",
        };
      } catch (error) {
        console.error("Update importance score error:", error);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update importance score",
        });
      }
    }),

  /**
   * Archive a mention
   */
  archive: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the mention belongs to the user
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found or access denied",
          });
        }

        // Archive the mention
        const archivedMention = await ctx.prisma.mention.update({
          where: { id: input.mentionId },
          data: {
            archived: true,
            archivedAt: new Date(),
          },
        });

        return {
          success: true,
          message: "Mention archived successfully",
          mention: {
            id: archivedMention.id,
            archived: archivedMention.archived,
            archivedAt: archivedMention.archivedAt,
          },
        };
      } catch (error) {
        console.error("Archive mention error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to archive mention",
        });
      }
    }),

  /**
   * Unarchive a mention
   */
  unarchive: protectedProcedure
    .input(
      z.object({
        mentionId: z.string().min(1, "Mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the mention belongs to the user
        const mention = await ctx.prisma.mention.findFirst({
          where: {
            id: input.mentionId,
            userId: ctx.userId!,
          },
        });

        if (!mention) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Mention not found or access denied",
          });
        }

        // Unarchive the mention
        const unarchivedMention = await ctx.prisma.mention.update({
          where: { id: input.mentionId },
          data: {
            archived: false,
            archivedAt: null,
          },
        });

        return {
          success: true,
          message: "Mention unarchived successfully",
          mention: {
            id: unarchivedMention.id,
            archived: unarchivedMention.archived,
            archivedAt: unarchivedMention.archivedAt,
          },
        };
      } catch (error) {
        console.error("Unarchive mention error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to unarchive mention",
        });
      }
    }),

  /**
   * Bulk archive multiple mentions
   */
  bulkArchive: protectedProcedure
    .input(
      z.object({
        mentionIds: z
          .array(z.string())
          .min(1, "At least one mention ID is required"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify all mentions belong to the user
        const mentions = await ctx.prisma.mention.findMany({
          where: {
            id: { in: input.mentionIds },
            userId: ctx.userId!,
          },
          select: { id: true },
        });

        if (mentions.length !== input.mentionIds.length) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Some mentions not found or access denied",
          });
        }

        // Archive all mentions
        const result = await ctx.prisma.mention.updateMany({
          where: {
            id: { in: input.mentionIds },
            userId: ctx.userId!,
          },
          data: {
            archived: true,
            archivedAt: new Date(),
          },
        });

        return {
          success: true,
          message: `${result.count} mentions archived successfully`,
          archivedCount: result.count,
        };
      } catch (error) {
        console.error("Bulk archive mentions error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to archive mentions",
        });
      }
    }),

  /**
   * Get archived mentions
   */
  getArchived: protectedProcedure
    .input(
      z.object({
        cursor: z.string().optional(),
        limit: z.number().min(1).max(100).default(20),
        accountId: z.string().optional(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const where: any = {
          userId: ctx.userId!,
          archived: true,
        };

        if (input.accountId) {
          where.accountId = input.accountId;
        }

        const mentions = await ctx.prisma.mention.findMany({
          where,
          orderBy: {
            archivedAt: "desc",
          },
          take: input.limit + 1,
          cursor: input.cursor ? { id: input.cursor } : undefined,
          include: {
            account: {
              select: {
                id: true,
                twitterHandle: true,
                displayName: true,
                avatarUrl: true,
                isActive: true,
              },
            },
            responses: {
              orderBy: {
                createdAt: "desc",
              },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    avatar: true,
                  },
                },
              },
            },
          },
        });

        let nextCursor: string | undefined;
        if (mentions.length > input.limit) {
          const nextItem = mentions.pop();
          nextCursor = nextItem!.id;
        }

        return {
          success: true,
          mentions: mentions.map((mention) => ({
            id: mention.id,
            content: mention.content,
            authorHandle: mention.authorHandle,
            authorName: mention.authorName,
            authorAvatar: mention.authorAvatarUrl,
            tweetUrl: mention.link,
            platform: "twitter",
            createdAt: mention.createdAt,
            archivedAt: mention.archivedAt,
            bullishScore: mention.bullishScore,
            importanceScore: mention.importanceScore,
            keywords: mention.keywords,
            hasAIResponse: mention.responses.length > 0,
            account: mention.account
              ? {
                  id: mention.account.id,
                  handle: mention.account.twitterHandle,
                  displayName: mention.account.displayName,
                  avatar: mention.account.avatarUrl,
                  isActive: mention.account.isActive,
                }
              : null,
            responses: mention.responses.map((response) => ({
              id: response.id,
              content: response.content,
              model: response.model,
              confidence: response.confidence,
              tokensUsed: response.tokensUsed,
              used: response.used,
              createdAt: response.createdAt,
            })),
          })),
          nextCursor,
        };
      } catch (error) {
        console.error("Get archived mentions error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch archived mentions",
        });
      }
    }),

  /**
   * Sync mentions for a specific monitored account
   */
  syncAccount: protectedProcedure
    .input(
      z.object({
        accountId: z.string().cuid("Invalid account ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        console.log(
          `🔄 Starting mention sync for account ${input.accountId} by user ${ctx.userId}`
        );

        // Create sync service instance with Prisma client
        const syncService = new MentionSyncService(ctx.prisma);

        // Sync mentions for the specific account
        const result = await syncService.syncAccountMentions(
          input.accountId,
          ctx.userId!
        );

        if (!result.success) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: result.error || "Failed to sync mentions",
          });
        }

        return {
          success: true,
          result: result,
          message: `Synced ${result.newMentions} new mentions for @${result.accountHandle}`,
        };
      } catch (error) {
        console.error("Sync account mentions error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to sync account mentions",
        });
      }
    }),

  /**
   * Sync mentions for all monitored accounts of the current user
   */
  syncAllAccounts: protectedProcedure.mutation(async ({ ctx }) => {
    try {
      console.log(`🚀 Starting bulk mention sync for user ${ctx.userId}`);

      // Create sync service instance with Prisma client
      const syncService = new MentionSyncService(ctx.prisma);

      // Sync mentions for all user's accounts
      const result = await syncService.syncAllUserAccounts(ctx.userId!);

      return {
        success: result.success,
        totalNewMentions: result.totalNewMentions,
        accountResults: result.results,
        errors: result.errors,
        message: result.success
          ? `Successfully synced ${result.totalNewMentions} new mentions across ${result.results.length} accounts`
          : `Sync completed with ${result.errors.length} errors`,
      };
    } catch (error) {
      console.error("Bulk sync mentions error:", error);

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to sync mentions for all accounts",
      });
    }
  }),

  /**
   * Get sync statistics for the current user
   */
  getSyncStats: protectedProcedure.query(async ({ ctx }) => {
    try {
      // Create sync service instance with Prisma client
      const syncService = new MentionSyncService(ctx.prisma);

      // Get sync statistics
      const stats = await syncService.getSyncStats(ctx.userId!);

      return {
        success: true,
        stats: stats,
      };
    } catch (error) {
      console.error("Get sync stats error:", error);

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get sync statistics",
      });
    }
  }),

  /**
   * Delete an AI response
   */
  deleteResponse: protectedProcedure
    .input(
      z.object({
        responseId: z.string().cuid("Invalid response ID"),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // First, verify the response exists and belongs to the user
        const response = await ctx.prisma.aIResponse.findFirst({
          where: {
            id: input.responseId,
            userId: ctx.userId!,
          },
          include: {
            mention: {
              select: {
                id: true,
                userId: true,
              },
            },
          },
        });

        if (!response) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "AI response not found or access denied",
          });
        }

        // Verify the mention also belongs to the user (double security check)
        if (response.mention.userId !== ctx.userId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "Access denied",
          });
        }

        // Delete the AI response
        await ctx.prisma.aIResponse.delete({
          where: {
            id: input.responseId,
          },
        });

        return {
          success: true,
          message: "AI response deleted successfully",
        };
      } catch (error) {
        console.error("Delete AI response error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete AI response",
        });
      }
    }),

  /**
   * Get performance statistics for debugging and monitoring
   */
  getPerformanceStats: protectedProcedure
    .input(
      z.object({
        minutes: z.number().min(1).max(1440).default(60), // Default to last hour, max 24 hours
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const stats = performanceMonitor.getStats(input.minutes);

        // Log summary to console for debugging
        performanceMonitor.logSummary(input.minutes);

        return {
          success: true,
          stats: {
            timeRange: `${input.minutes} minutes`,
            queryCount: stats.queryCount,
            averageDuration: Math.round(stats.averageDuration * 100) / 100, // Round to 2 decimals
            slowQueriesCount: stats.slowQueries.length,
            topSlowQueries: stats.topSlowQueries.map((q) => ({
              ...q,
              avgDuration: Math.round(q.avgDuration * 100) / 100,
            })),
            recommendations: generatePerformanceRecommendations(stats),
          },
        };
      } catch (error) {
        console.error("Get performance stats error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get performance statistics",
        });
      }
    }),
});

/**
 * Generate performance recommendations based on query statistics
 */
function generatePerformanceRecommendations(stats: any): string[] {
  const recommendations: string[] = [];

  if (stats.averageDuration > 1000) {
    recommendations.push(
      "Average query time is over 1s - consider optimizing database indexes or query structure"
    );
  }

  if (stats.slowQueries.length > stats.queryCount * 0.1) {
    recommendations.push(
      "High percentage of slow queries detected - review database configuration and query optimization"
    );
  }

  const mentionsGetAllQueries = stats.topSlowQueries.find(
    (q: any) => q.query === "mentions.getAll"
  );
  if (mentionsGetAllQueries && mentionsGetAllQueries.avgDuration > 500) {
    recommendations.push(
      "mentions.getAll queries are slow - consider using includeResponses: false when responses are not needed"
    );
  }

  if (stats.queryCount === 0) {
    recommendations.push("No queries recorded in the specified time range");
  } else if (stats.queryCount > 100) {
    recommendations.push(
      "High query volume detected - consider implementing caching strategies"
    );
  }

  if (recommendations.length === 0) {
    recommendations.push(
      "Performance looks good! All queries are running efficiently."
    );
  }

  return recommendations;
}

/**
 * Extract keywords from mention content
 * This is a simple implementation - could be enhanced with AI
 */
function extractKeywords(content: string): string[] {
  // Remove URLs, mentions, hashtags for cleaner analysis
  const cleanContent = content
    .replace(/https?:\/\/[^\s]+/g, "") // Remove URLs
    .replace(/@\w+/g, "") // Remove mentions
    .replace(/#\w+/g, "") // Remove hashtags
    .toLowerCase();

  // Simple keyword extraction based on common patterns
  const words = cleanContent
    .split(/\s+/)
    .filter(
      (word) =>
        word.length > 3 &&
        ![
          "this",
          "that",
          "with",
          "from",
          "they",
          "were",
          "been",
          "have",
          "will",
          "would",
          "could",
          "should",
        ].includes(word)
    );

  // Get most frequent words (max 5)
  const wordCounts = words.reduce(
    (acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return Object.entries(wordCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([word]) => word);
}

/**
 * Calculate bullish score based on content sentiment
 * This is a simple implementation - could be enhanced with AI
 */
function calculateBullishScore(content: string): number {
  const lowerContent = content.toLowerCase();

  // Positive indicators
  const positiveWords = [
    "great",
    "awesome",
    "amazing",
    "excellent",
    "fantastic",
    "love",
    "best",
    "good",
    "nice",
    "wonderful",
    "perfect",
    "brilliant",
    "outstanding",
  ];
  const negativeWords = [
    "bad",
    "terrible",
    "awful",
    "hate",
    "worst",
    "horrible",
    "disappointing",
    "useless",
    "broken",
    "poor",
    "failed",
  ];

  let score = 50; // Neutral starting point

  // Check for positive words
  positiveWords.forEach((word) => {
    if (lowerContent.includes(word)) {
      score += 10;
    }
  });

  // Check for negative words
  negativeWords.forEach((word) => {
    if (lowerContent.includes(word)) {
      score -= 15;
    }
  });

  // Check for exclamation marks (usually positive energy)
  const exclamationCount = (content.match(/!/g) || []).length;
  score += Math.min(exclamationCount * 5, 20);

  // Check for question marks (usually neutral to slightly negative)
  const questionCount = (content.match(/\?/g) || []).length;
  score -= Math.min(questionCount * 2, 10);

  // Ensure score is within bounds
  return Math.max(0, Math.min(100, Math.round(score)));
}
