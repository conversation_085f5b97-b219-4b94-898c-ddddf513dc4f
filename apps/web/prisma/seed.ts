/**
 * Seed script for BuddyChip database
 *
 * This script populates the database with:
 * - Default subscription plans (<PERSON><PERSON>, <PERSON>ly <PERSON>, Team Plan)
 * - Plan features and limits
 */

import { PrismaClient, FeatureType } from "../prisma/generated/index.js";
import { seedAIModels } from "../src/scripts/seed-ai-models.js";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Seeding database...");

  // Create subscription plans
  const freePlan = await prisma.subscriptionPlan.upsert({
    where: { name: "free" },
    update: {},
    create: {
      name: "free",
      displayName: "Free",
      description: "Get started with basic AI features",
      price: 0.0,
      baseUsers: 1,
      additionalUserPrice: null,
      isActive: true,
    },
  });

  const replyGuyPlan = await prisma.subscriptionPlan.upsert({
    where: { name: "reply-guy" },
    update: {},
    create: {
      name: "reply-guy",
      displayName: "<PERSON>ly Guy",
      description:
        "Perfect for individuals starting their social media engagement journey",
      price: 9.0,
      baseUsers: 1,
      additionalUserPrice: null,
      isActive: true,
    },
  });

  const replyGodPlan = await prisma.subscriptionPlan.upsert({
    where: { name: "reply-god" },
    update: {},
    create: {
      name: "reply-god",
      displayName: "Reply God",
      description: "Advanced features for power users and content creators",
      price: 29.0,
      baseUsers: 1,
      additionalUserPrice: null,
      isActive: true,
    },
  });

  const teamPlan = await prisma.subscriptionPlan.upsert({
    where: { name: "team" },
    update: {},
    create: {
      name: "team",
      displayName: "Team",
      description: "Enterprise solution for teams and organizations",
      price: 99.0,
      baseUsers: 10,
      additionalUserPrice: 20.0,
      isActive: true,
    },
  });

  console.log("✅ Created subscription plans");

  // Define feature limits for each plan
  const planFeatures = [
    // Free Plan Features
    {
      planId: freePlan.id,
      feature: FeatureType.AI_CALLS,
      limit: 50, // 50 AI calls per month
    },
    {
      planId: freePlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: 5, // 5 image generations per month
    },
    {
      planId: freePlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 1, // 1 monitored account
    },
    {
      planId: freePlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: 100, // 100 mentions per month
    },
    {
      planId: freePlan.id,
      feature: FeatureType.MENTIONS_PER_SYNC,
      limit: 25, // 25 mentions per sync
    },
    {
      planId: freePlan.id,
      feature: FeatureType.MAX_TOTAL_MENTIONS,
      limit: 50, // 50 max total mentions
    },
    {
      planId: freePlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 1, // 0.5GB storage (using 1 as minimum)
    },
    {
      planId: freePlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 1, // 1 team member
    },
    {
      planId: freePlan.id,
      feature: FeatureType.COOKIE_API_CALLS,
      limit: 10, // 10 Cookie.fun API calls per month
    },

    // Reply Guy Plan Features
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.AI_CALLS,
      limit: 1000, // 1000 AI calls per month
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: 20, // 20 image generations per month
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 3, // 3 monitored accounts
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: 1000, // 1000 mentions per month
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.MENTIONS_PER_SYNC,
      limit: 25, // 25 mentions per sync
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.MAX_TOTAL_MENTIONS,
      limit: 100, // 100 max total mentions
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 1, // 1GB storage
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 1, // 1 team member
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.COOKIE_API_CALLS,
      limit: 50, // 50 Cookie.fun API calls per month
    },

    // Reply God Plan Features
    {
      planId: replyGodPlan.id,
      feature: FeatureType.AI_CALLS,
      limit: 5000, // 5000 AI calls per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: 100, // 100 image generations per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 10, // 10 monitored accounts
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: 5000, // 5000 mentions per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.MENTIONS_PER_SYNC,
      limit: 100, // 100 mentions per sync
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.MAX_TOTAL_MENTIONS,
      limit: 500, // 500 max total mentions
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 5, // 5GB storage
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 3, // 3 team members
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.COOKIE_API_CALLS,
      limit: 200, // 200 Cookie.fun API calls per month
    },

    // Team Plan Features
    {
      planId: teamPlan.id,
      feature: FeatureType.AI_CALLS,
      limit: -1, // Unlimited AI calls
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.IMAGE_GENERATIONS,
      limit: -1, // Unlimited image generations
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.MONITORED_ACCOUNTS,
      limit: 50, // 50 monitored accounts
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.MENTIONS_PER_MONTH,
      limit: -1, // Unlimited mentions per month
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.MENTIONS_PER_SYNC,
      limit: 200, // 200 mentions per sync
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.MAX_TOTAL_MENTIONS,
      limit: 2000, // 2000 max total mentions
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.STORAGE_GB,
      limit: 20, // 20GB storage
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.TEAM_MEMBERS,
      limit: 10, // 10 team members
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.COOKIE_API_CALLS,
      limit: -1, // Unlimited Cookie.fun API calls
    },

    // Persona Generation Features
    {
      planId: freePlan.id,
      feature: FeatureType.PERSONA_GENERATIONS,
      limit: 0, // No persona generations for free plan
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.PERSONA_GENERATIONS,
      limit: 1, // 1 persona generation per month
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.PERSONA_GENERATIONS,
      limit: 3, // 3 persona generations per month
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.PERSONA_GENERATIONS,
      limit: -1, // Unlimited persona generations
    },

    // Persona Memory Operations Features (for storing persona tweets/analysis as memories)
    {
      planId: freePlan.id,
      feature: FeatureType.PERSONA_MEMORY_OPS,
      limit: 0, // No persona memory operations for free plan
    },
    {
      planId: replyGuyPlan.id,
      feature: FeatureType.PERSONA_MEMORY_OPS,
      limit: 2000, // 2000 memory operations per month (enough for 2-4 personas)
    },
    {
      planId: replyGodPlan.id,
      feature: FeatureType.PERSONA_MEMORY_OPS,
      limit: 10000, // 10,000 memory operations per month (enough for many personas)
    },
    {
      planId: teamPlan.id,
      feature: FeatureType.PERSONA_MEMORY_OPS,
      limit: -1, // Unlimited persona memory operations
    },
  ];

  // Create plan features
  for (const feature of planFeatures) {
    await prisma.planFeature.upsert({
      where: {
        planId_feature: {
          planId: feature.planId,
          feature: feature.feature,
        },
      },
      update: {
        limit: feature.limit,
      },
      create: feature,
    });
  }

  console.log("✅ Created plan features");

  // Seed AI models
  await seedAIModels(prisma);

  // Log the created plans
  const plans = await prisma.subscriptionPlan.findMany({
    include: {
      features: true,
    },
  });

  console.log("\n📋 Created Plans:");
  plans.forEach((plan) => {
    console.log(`\n${plan.displayName} ($${plan.price}/month):`);
    plan.features.forEach((feature) => {
      const limitText =
        feature.limit === -1 ? "Unlimited" : feature.limit.toString();
      console.log(`  - ${feature.feature}: ${limitText}`);
    });
  });

  console.log("\n🎉 Database seeded successfully!");
}

main()
  .catch((e) => {
    console.error("❌ Error seeding database:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
