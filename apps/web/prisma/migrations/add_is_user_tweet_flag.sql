-- Add isUserTweet flag to mentions table
-- This flag indicates whether a mention is actually a user's own tweet (not a mention)
ALTER TABLE "mentions" 
ADD COLUMN IF NOT EXISTS "isUserTweet" BOOLEAN NOT NULL DEFAULT FALSE;

-- Add index for querying user tweets
CREATE INDEX IF NOT EXISTS "mentions_isUserTweet_idx" ON "mentions"("isUserTweet");

-- Add compound index for user's own tweets
CREATE INDEX IF NOT EXISTS "mentions_userId_isUserTweet_idx" ON "mentions"("userId", "isUserTweet");