-- Mem0 Memory Storage Migration
-- This migration creates the memories table and functions for mem0 AI memory management
-- with user-specific memory isolation for the BuddyChip application

-- Create the memories table for mem0 vector storage
CREATE TABLE IF NOT EXISTS memories (
  id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
  
  -- Vector embedding storage (1536 dimensions for OpenAI text-embedding-ada-002)
  embedding VECTOR(1536) NOT NULL,
  
  -- Memory content and metadata
  content TEXT NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
  
  -- User isolation - ensure each user has their own memory space
  user_id TEXT NOT NULL,
  
  -- Memory categorization
  memory_type TEXT DEFAULT 'conversation', -- 'conversation', 'preference', 'fact', etc.
  
  -- Relevance and decay management
  relevance_score FLOAT DEFAULT 1.0,
  access_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Standard timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for optimal performance
CREATE INDEX IF NOT EXISTS idx_memories_user_id ON memories(user_id);
CREATE INDEX IF NOT EXISTS idx_memories_memory_type ON memories(memory_type);
CREATE INDEX IF NOT EXISTS idx_memories_relevance ON memories(relevance_score DESC);
CREATE INDEX IF NOT EXISTS idx_memories_created_at ON memories(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_memories_last_accessed ON memories(last_accessed_at DESC);

-- Compound indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_memories_user_type ON memories(user_id, memory_type);
CREATE INDEX IF NOT EXISTS idx_memories_user_relevance ON memories(user_id, relevance_score DESC);

-- GIN index for metadata JSON queries
CREATE INDEX IF NOT EXISTS idx_memories_metadata ON memories USING GIN(metadata);

-- Vector similarity index using HNSW for fast similarity search
CREATE INDEX IF NOT EXISTS idx_memories_embedding_hnsw 
ON memories USING hnsw (embedding vector_cosine_ops);

-- Create function for vector similarity search with user isolation
CREATE OR REPLACE FUNCTION match_user_memories(
  query_embedding VECTOR(1536),
  match_user_id TEXT,
  match_count INT DEFAULT 5,
  similarity_threshold FLOAT DEFAULT 0.5,
  filter_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS TABLE (
  id TEXT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT,
  memory_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql AS $$
BEGIN
  RETURN QUERY
  SELECT
    m.id,
    m.content,
    m.metadata,
    1 - (m.embedding <=> query_embedding) AS similarity,
    m.memory_type,
    m.created_at
  FROM memories m
  WHERE 
    m.user_id = match_user_id
    AND (1 - (m.embedding <=> query_embedding)) >= similarity_threshold
    AND (
      CASE 
        WHEN filter_metadata::text = '{}'::text THEN true
        ELSE m.metadata @> filter_metadata
      END
    )
  ORDER BY m.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Function to update access tracking when memories are retrieved
CREATE OR REPLACE FUNCTION update_memory_access(memory_id TEXT)
RETURNS VOID
LANGUAGE plpgsql AS $$
BEGIN
  UPDATE memories 
  SET 
    access_count = access_count + 1,
    last_accessed_at = NOW(),
    relevance_score = LEAST(relevance_score + 0.1, 2.0) -- Boost relevance but cap at 2.0
  WHERE id = memory_id;
END;
$$;

-- Function to decay memory relevance over time (can be called periodically)
CREATE OR REPLACE FUNCTION decay_memory_relevance(days_threshold INT DEFAULT 30)
RETURNS VOID
LANGUAGE plpgsql AS $$
BEGIN
  UPDATE memories 
  SET relevance_score = GREATEST(relevance_score * 0.95, 0.1) -- Decay but keep minimum of 0.1
  WHERE last_accessed_at < NOW() - INTERVAL '1 day' * days_threshold;
END;
$$;

-- Function to clean up old, low-relevance memories
CREATE OR REPLACE FUNCTION cleanup_old_memories(
  user_id_param TEXT,
  max_memories_per_user INT DEFAULT 1000,
  min_relevance_threshold FLOAT DEFAULT 0.2
)
RETURNS INT
LANGUAGE plpgsql AS $$
DECLARE
  deleted_count INT;
BEGIN
  -- Delete memories that are old and have low relevance
  DELETE FROM memories 
  WHERE user_id = user_id_param
    AND relevance_score < min_relevance_threshold
    AND created_at < NOW() - INTERVAL '90 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  -- If still over limit, delete oldest memories
  DELETE FROM memories 
  WHERE id IN (
    SELECT id FROM memories 
    WHERE user_id = user_id_param
    ORDER BY created_at DESC
    OFFSET max_memories_per_user
  );
  
  GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
  
  RETURN deleted_count;
END;
$$;

-- Trigger to update the updated_at timestamp
CREATE TRIGGER update_memories_updated_at 
  BEFORE UPDATE ON memories 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Add foreign key constraint to link memories to users table if it exists
-- This will be automatically handled by Prisma relationships, but documented here
-- ALTER TABLE memories ADD CONSTRAINT fk_memories_user_id 
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE memories IS 'Mem0 AI memory storage with user-specific isolation for BuddyChip Benji agent';
COMMENT ON COLUMN memories.embedding IS 'Vector embedding (1536d) for semantic similarity search';
COMMENT ON COLUMN memories.user_id IS 'User ID for memory isolation - each user has their own memory space';
COMMENT ON COLUMN memories.memory_type IS 'Type of memory: conversation, preference, fact, etc.';
COMMENT ON COLUMN memories.relevance_score IS 'Dynamic relevance score (0.1-2.0) that increases with access and decays over time';
COMMENT ON FUNCTION match_user_memories IS 'Fast vector similarity search with user isolation and metadata filtering';
COMMENT ON FUNCTION update_memory_access IS 'Updates access tracking and boosts relevance when memories are retrieved';
COMMENT ON FUNCTION decay_memory_relevance IS 'Periodic function to decay relevance of unused memories';
COMMENT ON FUNCTION cleanup_old_memories IS 'Manages memory storage limits per user by removing old, low-relevance memories';