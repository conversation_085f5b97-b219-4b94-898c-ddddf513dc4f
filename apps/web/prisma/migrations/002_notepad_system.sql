-- Migration: Add Notepad System Tables
-- Description: Creates tables for the enhanced notepad system that allows users to craft responses with research tools, sources, and drafts
-- Date: 2025-01-28

-- Create mention_notepads table
CREATE TABLE IF NOT EXISTS "mention_notepads" (
    "id" TEXT NOT NULL,
    "mentionId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "title" TEXT,
    "notes" TEXT,
    "draftResponse" TEXT,
    "finalResponse" TEXT,
    "researchQuery" TEXT,
    "researchContext" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastUsedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "mention_notepads_pkey" PRIMARY KEY ("id")
);

-- Create notepad_sources table
CREATE TABLE IF NOT EXISTS "notepad_sources" (
    "id" TEXT NOT NULL,
    "notepadId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "content" TEXT,
    "sourceType" TEXT NOT NULL,
    "searchTool" TEXT NOT NULL,
    "relevanceScore" DOUBLE PRECISION,
    "credibilityScore" DOUBLE PRECISION,
    "publishedAt" TIMESTAMP(3),
    "extractedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "isBookmarked" BOOLEAN NOT NULL DEFAULT false,
    "userRating" INTEGER,
    "userNotes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notepad_sources_pkey" PRIMARY KEY ("id")
);

-- Create notepad_drafts table
CREATE TABLE IF NOT EXISTS "notepad_drafts" (
    "id" TEXT NOT NULL,
    "notepadId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "version" INTEGER NOT NULL,
    "title" TEXT,
    "generatedBy" TEXT,
    "model" TEXT,
    "prompt" TEXT,
    "tokensUsed" INTEGER,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isFavorite" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notepad_drafts_pkey" PRIMARY KEY ("id")
);

-- Create unique constraint for one notepad per mention
CREATE UNIQUE INDEX IF NOT EXISTS "mention_notepads_mentionId_key" ON "mention_notepads"("mentionId");

-- Create indexes for mention_notepads
CREATE INDEX IF NOT EXISTS "mention_notepads_mentionId_idx" ON "mention_notepads"("mentionId");
CREATE INDEX IF NOT EXISTS "mention_notepads_userId_idx" ON "mention_notepads"("userId");
CREATE INDEX IF NOT EXISTS "mention_notepads_lastUsedAt_idx" ON "mention_notepads"("lastUsedAt" DESC);
CREATE INDEX IF NOT EXISTS "mention_notepads_isActive_idx" ON "mention_notepads"("isActive");

-- Create indexes for notepad_sources
CREATE INDEX IF NOT EXISTS "notepad_sources_notepadId_idx" ON "notepad_sources"("notepadId");
CREATE INDEX IF NOT EXISTS "notepad_sources_sourceType_idx" ON "notepad_sources"("sourceType");
CREATE INDEX IF NOT EXISTS "notepad_sources_searchTool_idx" ON "notepad_sources"("searchTool");
CREATE INDEX IF NOT EXISTS "notepad_sources_isBookmarked_idx" ON "notepad_sources"("isBookmarked");
CREATE INDEX IF NOT EXISTS "notepad_sources_relevanceScore_idx" ON "notepad_sources"("relevanceScore" DESC);
CREATE INDEX IF NOT EXISTS "notepad_sources_extractedAt_idx" ON "notepad_sources"("extractedAt" DESC);

-- Create indexes for notepad_drafts
CREATE INDEX IF NOT EXISTS "notepad_drafts_notepadId_idx" ON "notepad_drafts"("notepadId");
CREATE INDEX IF NOT EXISTS "notepad_drafts_version_idx" ON "notepad_drafts"("version" DESC);
CREATE INDEX IF NOT EXISTS "notepad_drafts_isActive_idx" ON "notepad_drafts"("isActive");
CREATE INDEX IF NOT EXISTS "notepad_drafts_isFavorite_idx" ON "notepad_drafts"("isFavorite");
CREATE INDEX IF NOT EXISTS "notepad_drafts_createdAt_idx" ON "notepad_drafts"("createdAt" DESC);

-- Add foreign key constraints
ALTER TABLE "mention_notepads" ADD CONSTRAINT "mention_notepads_mentionId_fkey" FOREIGN KEY ("mentionId") REFERENCES "mentions"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "mention_notepads" ADD CONSTRAINT "mention_notepads_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "notepad_sources" ADD CONSTRAINT "notepad_sources_notepadId_fkey" FOREIGN KEY ("notepadId") REFERENCES "mention_notepads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "notepad_drafts" ADD CONSTRAINT "notepad_drafts_notepadId_fkey" FOREIGN KEY ("notepadId") REFERENCES "mention_notepads"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add check constraints for data validation
ALTER TABLE "notepad_sources" ADD CONSTRAINT "notepad_sources_relevanceScore_check" CHECK ("relevanceScore" >= 0 AND "relevanceScore" <= 1);
ALTER TABLE "notepad_sources" ADD CONSTRAINT "notepad_sources_credibilityScore_check" CHECK ("credibilityScore" >= 0 AND "credibilityScore" <= 1);
ALTER TABLE "notepad_sources" ADD CONSTRAINT "notepad_sources_userRating_check" CHECK ("userRating" >= 1 AND "userRating" <= 5);
ALTER TABLE "notepad_sources" ADD CONSTRAINT "notepad_sources_sourceType_check" CHECK ("sourceType" IN ('web', 'news', 'research', 'social'));
ALTER TABLE "notepad_sources" ADD CONSTRAINT "notepad_sources_searchTool_check" CHECK ("searchTool" IN ('xai', 'exa', 'manual'));

ALTER TABLE "notepad_drafts" ADD CONSTRAINT "notepad_drafts_version_check" CHECK ("version" > 0);
ALTER TABLE "notepad_drafts" ADD CONSTRAINT "notepad_drafts_generatedBy_check" CHECK ("generatedBy" IN ('user', 'ai', 'enhanced'));

-- Create trigger to automatically update updatedAt timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_mention_notepads_updated_at BEFORE UPDATE ON "mention_notepads" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notepad_sources_updated_at BEFORE UPDATE ON "notepad_sources" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_notepad_drafts_updated_at BEFORE UPDATE ON "notepad_drafts" FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE "mention_notepads" IS 'Enhanced notepad sessions for crafting responses to mentions with research tools and draft management';
COMMENT ON TABLE "notepad_sources" IS 'Sources and citations collected during research sessions for notepad responses';
COMMENT ON TABLE "notepad_drafts" IS 'Draft versions of responses created during the notepad crafting process';

COMMENT ON COLUMN "mention_notepads"."researchContext" IS 'JSONB field storing research session metadata and context';
COMMENT ON COLUMN "notepad_sources"."relevanceScore" IS 'AI-assessed relevance score from 0.0 to 1.0';
COMMENT ON COLUMN "notepad_sources"."credibilityScore" IS 'AI-assessed credibility score from 0.0 to 1.0';
COMMENT ON COLUMN "notepad_sources"."sourceType" IS 'Type of source: web, news, research, or social';
COMMENT ON COLUMN "notepad_sources"."searchTool" IS 'Tool used to find this source: xai, exa, or manual';
COMMENT ON COLUMN "notepad_drafts"."version" IS 'Version number of the draft, incremented for each new version';
COMMENT ON COLUMN "notepad_drafts"."generatedBy" IS 'How the draft was created: user, ai, or enhanced';
