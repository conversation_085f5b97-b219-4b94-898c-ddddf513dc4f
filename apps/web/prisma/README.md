# BuddyChip Database Setup

This directory contains the Prisma schema and related database files for the BuddyChip application.

## Quick Start

1. **Set up environment variables** in `apps/server/.env`:
   ```bash
   DATABASE_URL="postgresql://user:password@localhost:5432/buddychip?schema=public"
   DIRECT_URL="postgresql://user:password@localhost:5432/buddychip"
   ```

2. **Generate Prisma client**:
   ```bash
   pnpm db:generate
   ```

3. **Push schema to database**:
   ```bash
   pnpm db:push
   ```

4. **Seed initial data**:
   ```bash
   pnpm db:seed
   ```

## File Structure

```
prisma/
├── README.md                    # This file
├── SCHEMA_DOCS.md              # Comprehensive schema documentation
├── schema/
│   └── schema.prisma           # Main Prisma schema file
├── generated/                  # Generated Prisma client (auto-generated)
├── migrations/                 # Database migrations
│   └── 001_init_extensions.sql # Initial PostgreSQL extensions
└── seed.ts                     # Database seeding script
```

## Commands

| Command | Description |
|---------|-------------|
| `pnpm db:generate` | Generate Prisma client |
| `pnpm db:push` | Push schema changes to database |
| `pnpm db:migrate` | Create and run migrations |
| `pnpm db:studio` | Open Prisma Studio |
| `pnpm db:seed` | Seed database with initial data |
| `pnpm db:reset` | Reset database (development only) |

## Schema Features

### 🚀 Modern Prisma 6.9.0 Features
- **PostgreSQL Extensions**: pgvector, pgcrypto, pg_trgm
- **Full-Text Search**: Advanced search with `fullTextSearchPostgres`
- **Relation Joins**: Optimized queries with `relationJoins`
- **Advanced Indexing**: GIN, compound, and specialized indexes

### 🔍 Core Models
- **User**: Clerk authentication integration
- **SubscriptionPlan**: Flexible pricing tiers
- **PlanFeature**: Configurable feature limits
- **MonitoredAccount**: Twitter account monitoring
- **Mention**: Tweet data with AI analysis
- **AIResponse**: AI-generated responses
- **Image**: File storage with metadata
- **UsageLog**: Rate limiting and analytics

### 📊 Subscription Plans
| Plan | Price | AI Calls | Images | Accounts |
|------|-------|----------|---------|----------|
| Reply Guy | $20/mo | 100 | 20 | 3 |
| Reply God | $50/mo | 500 | 50 | 10 |
| Team | $79+$50/user | 1000 | 100 | 25 |

## PostgreSQL Extensions

### pgvector
- **Purpose**: Vector similarity search for Mem0 AI memory
- **Usage**: Automatic via Mem0 library configuration
- **Tables**: Managed by Mem0, not in Prisma schema

### pgcrypto
- **Purpose**: Cryptographic functions and secure ID generation
- **Usage**: Enhanced security for sensitive operations
- **Benefits**: Better random number generation

### pg_trgm
- **Purpose**: Trigram similarity and fuzzy text search
- **Usage**: Twitter handle fuzzy matching
- **Benefits**: Better search experience

## Rate Limiting

The schema includes built-in rate limiting via the `UsageLog` model:

```typescript
// Check if user can perform action
const rateLimit = await checkRateLimit(userId, FeatureType.AI_CALLS);

if (!rateLimit.allowed) {
  throw new Error(`Limit exceeded. Used: ${rateLimit.currentUsage}/${rateLimit.limit}`);
}

// Record usage after successful operation
await recordUsage(userId, FeatureType.AI_CALLS, 1, { 
  model: 'gpt-4', 
  tokensUsed: 150 
});
```

## Performance Optimizations

### Indexing Strategy
- **Single-column**: Foreign keys, timestamps, status fields
- **Compound**: Common query patterns (userId + feature + billing period)
- **Specialized**: GIN indexes for full-text and array search
- **Unique constraints**: Prevent data duplication

### Query Optimization
- Use `relationLoadStrategy: 'join'` for efficient data fetching
- Leverage compound indexes for complex WHERE clauses
- Implement proper pagination with cursor-based navigation

### Data Types
- `Decimal` for precise financial calculations
- `Array` for keywords and tags
- `JSON` for flexible metadata
- `DateTime` with proper timezone handling

## Development Workflow

1. **Make schema changes** in `schema/schema.prisma`
2. **Generate client**: `pnpm db:generate`
3. **Push changes**: `pnpm db:push` (development) or `pnpm db:migrate` (production)
4. **Test changes**: Use Prisma Studio to verify data
5. **Update seed data** if needed in `seed.ts`

## Production Deployment

1. **Environment setup**: Configure `DATABASE_URL` and `DIRECT_URL`
2. **Run migrations**: `pnpm prisma migrate deploy`
3. **Generate client**: `pnpm db:generate`
4. **Seed data**: `pnpm db:seed` (initial deployment only)
5. **Verify setup**: Check database and extensions

## Troubleshooting

### Common Issues

**Extension not found**:
```sql
-- Manually create extensions if needed
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pgcrypto;
CREATE EXTENSION IF NOT EXISTS pg_trgm;
```

**Permission denied**:
- Ensure database user has `CREATE EXTENSION` privileges
- Use superuser for initial setup if needed

**Schema location**:
- All commands use `--schema ./prisma/schema/schema.prisma`
- Generated client outputs to `./prisma/generated/`

**Migration issues**:
- Use `pnpm db:reset` in development to start fresh
- Check Supabase dashboard for connection issues
- Verify environment variables are correctly set

For detailed schema documentation, see [SCHEMA_DOCS.md](./SCHEMA_DOCS.md).