{"buildCommand": "cd ../.. && pnpm install && pnpm turbo build --filter=web", "installCommand": "cd ../.. && pnpm install", "framework": "nextjs", "outputDirectory": ".next", "functions": {"app/api/telegram/**": {"maxDuration": 60}, "app/api/trpc/**": {"maxDuration": 45}, "app/**": {"maxDuration": 30}}, "headers": [{"source": "/api/telegram/webhook", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}], "crons": [{"path": "/api/sync/mentions", "schedule": "*/5 * * * *"}]}