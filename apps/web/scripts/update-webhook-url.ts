#!/usr/bin/env tsx

/**
 * Update Telegram Webhook URL
 * 
 * This script updates the Telegram webhook to use the new endpoint
 */

async function updateTelegramWebhook() {
  console.log('🔧 Updating Telegram Webhook URL\n');

  // Use the actual bot token
  const botToken = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
  const webhookSecret = 'fedb2719f4a14793c848016d95e7378dcd7fab158f6ac7cd46ea266d645989d9';
  const newWebhookUrl = 'https://www.buddychip.app/api/telegram/webhook-new';

  try {
    // Step 1: Get current webhook info
    console.log('1️⃣ Getting current webhook info...');
    const webhookInfoResponse = await fetch(`https://api.telegram.org/bot${botToken}/getWebhookInfo`);
    const webhookInfo = await webhookInfoResponse.json();
    
    if (webhookInfo.ok) {
      console.log('Current webhook status:');
      console.log(`- URL: ${webhookInfo.result?.url || 'Not set'}`);
      console.log(`- Pending updates: ${webhookInfo.result?.pending_update_count || 0}`);
      console.log(`- Max connections: ${webhookInfo.result?.max_connections || 'Not set'}`);
      console.log(`- Allowed updates: ${webhookInfo.result?.allowed_updates?.join(', ') || 'All'}`);
      
      if (webhookInfo.result?.last_error_message) {
        console.log(`- Last error: ${webhookInfo.result.last_error_message}`);
        const errorDate = new Date(webhookInfo.result.last_error_date * 1000);
        console.log(`- Last error date: ${errorDate.toISOString()}`);
      }
    } else {
      console.log('❌ Failed to get webhook info:', webhookInfo.description);
      return;
    }

    // Step 2: Set new webhook
    console.log('\n2️⃣ Setting new webhook...');
    console.log(`New webhook URL: ${newWebhookUrl}`);
    
    const setWebhookData = {
      url: newWebhookUrl,
      secret_token: webhookSecret,
      allowed_updates: ['message', 'callback_query'],
      max_connections: 40,
      drop_pending_updates: true // Clear any pending updates from old webhook
    };

    const setWebhookResponse = await fetch(`https://api.telegram.org/bot${botToken}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(setWebhookData)
    });

    const setWebhookResult = await setWebhookResponse.json();
    
    if (setWebhookResult.ok) {
      console.log('✅ Webhook updated successfully!');
      console.log(`- New URL: ${newWebhookUrl}`);
      console.log(`- Secret token: Configured`);
      console.log(`- Allowed updates: message, callback_query`);
      console.log(`- Pending updates: Cleared`);
    } else {
      console.log('❌ Failed to set webhook:', setWebhookResult.description);
      return;
    }

    // Step 3: Verify new webhook
    console.log('\n3️⃣ Verifying new webhook...');
    
    // Wait a moment for the webhook to be set
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const verifyResponse = await fetch(`https://api.telegram.org/bot${botToken}/getWebhookInfo`);
    const verifyInfo = await verifyResponse.json();
    
    if (verifyInfo.ok) {
      console.log('Verification results:');
      console.log(`- URL: ${verifyInfo.result?.url}`);
      console.log(`- Pending updates: ${verifyInfo.result?.pending_update_count || 0}`);
      console.log(`- Last error: ${verifyInfo.result?.last_error_message || 'None'}`);
      
      if (verifyInfo.result?.url === newWebhookUrl) {
        console.log('✅ Webhook verification successful!');
      } else {
        console.log('⚠️ Webhook URL mismatch');
      }
    }

    // Step 4: Test new webhook endpoint
    console.log('\n4️⃣ Testing new webhook endpoint...');
    
    try {
      const healthResponse = await fetch(`${newWebhookUrl}?action=health`);
      console.log(`Health check status: ${healthResponse.status}`);
      
      if (healthResponse.status === 200) {
        const healthData = await healthResponse.json();
        console.log(`Service status: ${healthData.status}`);
        console.log(`Environment: ${healthData.environment}`);
        console.log(`Configuration valid: ${healthData.configuration?.valid ? '✅' : '❌'}`);
      } else {
        console.log('⚠️ Health check returned non-200 status');
      }
    } catch (error) {
      console.log('⚠️ Health check failed (this is expected if the new endpoint is not deployed yet)');
    }

    // Step 5: Send test message
    console.log('\n5️⃣ Sending test message to verify webhook...');
    
    // Send a message to yourself to test the webhook
    const testChatId = '904041730'; // Your Telegram user ID
    const testMessage = '🔧 Webhook updated! Testing new security system...';
    
    const sendMessageResponse = await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: testChatId,
        text: testMessage
      })
    });

    const sendMessageResult = await sendMessageResponse.json();
    
    if (sendMessageResult.ok) {
      console.log('✅ Test message sent successfully!');
      console.log('Check your Telegram for the test message.');
      console.log('If you receive it, the webhook is working correctly.');
    } else {
      console.log('❌ Failed to send test message:', sendMessageResult.description);
    }

    console.log('\n🎉 Webhook update completed!');
    console.log('\n📋 Summary:');
    console.log(`- Old webhook: Replaced`);
    console.log(`- New webhook: ${newWebhookUrl}`);
    console.log(`- Security: Enhanced with new system`);
    console.log(`- Pending updates: Cleared`);
    console.log(`- Test message: ${sendMessageResult.ok ? 'Sent' : 'Failed'}`);
    
    console.log('\n🧪 Next Steps:');
    console.log('1. Try sending /start to @Benji_BuddyChip_Bot');
    console.log('2. Check if you receive a response');
    console.log('3. Monitor the health check endpoint for any issues');
    console.log('4. Check Vercel deployment logs if needed');

  } catch (error) {
    console.error('❌ Webhook update failed:', error);
  }
}

// Run the update
updateTelegramWebhook().catch(console.error);
