#!/usr/bin/env node

/**
 * Test Webhook Script
 * 
 * This script tests the webhook endpoint to see what's causing the 403 error
 */

const https = require('https');

const WEBHOOK_URL = 'https://www.buddychip.app/api/telegram/webhook';
const BOT_TOKEN = '**********************************************';

function makeRequest(url, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TelegramBot (like TwitterBot)',
        ...headers
      }
    };

    if (data) {
      options.headers['Content-Length'] = Buffer.byteLength(data);
    }

    const req = https.request(url, options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          body: body
        });
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(data);
    }
    
    req.end();
  });
}

async function main() {
  console.log('🧪 Testing Telegram Webhook\n');

  try {
    // Test 1: Health check (GET)
    console.log('1️⃣ Testing health check...');
    const healthResult = await makeRequest(`${WEBHOOK_URL}?action=health`);
    console.log(`Status: ${healthResult.status}`);
    console.log(`Body: ${healthResult.body}\n`);

    // Test 2: Simulate Telegram webhook (POST)
    console.log('2️⃣ Testing webhook POST (simulating Telegram)...');
    const testUpdate = JSON.stringify({
      update_id: 123456789,
      message: {
        message_id: 1,
        from: {
          id: 123456789,
          is_bot: false,
          first_name: "Test",
          username: "testuser"
        },
        chat: {
          id: 123456789,
          first_name: "Test",
          username: "testuser",
          type: "private"
        },
        date: Math.floor(Date.now() / 1000),
        text: "/start"
      }
    });

    const webhookResult = await makeRequest(
      WEBHOOK_URL, 
      'POST', 
      testUpdate,
      {
        'X-Telegram-Bot-Api-Secret-Token': 'fedb2719f4a14793c848016d95e7378dcd7fab158f6ac7cd46ea266d645989d9'
      }
    );
    console.log(`Status: ${webhookResult.status}`);
    console.log(`Body: ${webhookResult.body}\n`);

    // Test 3: Check if it's a CORS issue
    console.log('3️⃣ Testing OPTIONS request...');
    const optionsResult = await makeRequest(WEBHOOK_URL, 'OPTIONS');
    console.log(`Status: ${optionsResult.status}`);
    console.log(`Headers: ${JSON.stringify(optionsResult.headers, null, 2)}\n`);

    // Test 4: Test without secret token
    console.log('4️⃣ Testing POST without secret token...');
    const noTokenResult = await makeRequest(WEBHOOK_URL, 'POST', testUpdate);
    console.log(`Status: ${noTokenResult.status}`);
    console.log(`Body: ${noTokenResult.body}\n`);

    console.log('✅ Webhook tests complete!');

  } catch (error) {
    console.error('❌ Error testing webhook:', error);
  }
}

if (require.main === module) {
  main();
}
