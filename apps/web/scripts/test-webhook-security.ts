#!/usr/bin/env tsx

/**
 * Test script for webhook security issues
 * 
 * This script tests the security middleware to see why it's returning 403 Forbidden
 */

import { securityMiddleware } from '../src/lib/telegram-security';

async function testWebhookSecurity() {
  console.log('🔍 Testing Webhook Security Issues\n');

  // Test the security middleware with a simulated Telegram request
  console.log('1️⃣ Testing security middleware...');
  
  // Simulate a typical Telegram webhook request
  const mockRequest = {
    ip: '***************', // Telegram IP range
    headers: {
      'content-type': 'application/json',
      'user-agent': 'TelegramBot (like TwitterBot)',
      'x-telegram-bot-api-secret-token': process.env.TELEGRAM_WEBHOOK_SECRET || '',
      'content-length': '200'
    },
    body: JSON.stringify({
      update_id: 123456,
      message: {
        message_id: 1,
        from: {
          id: 904041730,
          is_bot: false,
          first_name: "<PERSON>",
          username: "france<PERSON><PERSON><PERSON>",
          language_code: "en"
        },
        chat: {
          id: 904041730,
          first_name: "<PERSON>",
          username: "france<PERSON><PERSON><PERSON>",
          type: "private"
        },
        date: Math.floor(Date.now() / 1000),
        text: "/start"
      }
    })
  };

  const botToken = process.env.TELEGRAM_BOT_TOKEN || '';

  console.log('Environment variables:');
  console.log(`- TELEGRAM_BYPASS_SECURITY: ${process.env.TELEGRAM_BYPASS_SECURITY}`);
  console.log(`- TELEGRAM_BYPASS_IP_CHECK: ${process.env.TELEGRAM_BYPASS_IP_CHECK}`);
  console.log(`- NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`- TELEGRAM_WEBHOOK_SECRET: ${process.env.TELEGRAM_WEBHOOK_SECRET ? 'Set' : 'Missing'}`);
  console.log(`- Bot token: ${botToken ? 'Set' : 'Missing'}`);

  console.log('\nTesting with mock Telegram request...');
  const result = securityMiddleware(mockRequest, botToken);

  console.log('Security check result:', {
    allowed: result.allowed,
    response: result.response,
    securityInfo: result.securityInfo
  });

  if (!result.allowed) {
    console.log('\n❌ Security check FAILED - this explains the 403 Forbidden!');
    console.log('Failure details:', result.securityInfo);
    
    if (result.securityInfo.issues) {
      console.log('\nSecurity issues found:');
      result.securityInfo.issues.forEach((issue: string, index: number) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    if (result.securityInfo.warnings) {
      console.log('\nSecurity warnings:');
      result.securityInfo.warnings.forEach((warning: string, index: number) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
    }
  } else {
    console.log('\n✅ Security check PASSED');
  }

  // Test with different scenarios
  console.log('\n2️⃣ Testing different scenarios...');

  // Test with missing secret token
  console.log('\nTesting without secret token...');
  const requestWithoutSecret = {
    ...mockRequest,
    headers: {
      ...mockRequest.headers,
      'x-telegram-bot-api-secret-token': undefined
    }
  };
  const resultWithoutSecret = securityMiddleware(requestWithoutSecret, botToken);
  console.log('Without secret token:', {
    allowed: resultWithoutSecret.allowed,
    reason: resultWithoutSecret.securityInfo.reason || 'No specific reason'
  });

  // Test with wrong IP
  console.log('\nTesting with non-Telegram IP...');
  const requestWithWrongIP = {
    ...mockRequest,
    ip: '***********' // Local IP, not Telegram
  };
  const resultWithWrongIP = securityMiddleware(requestWithWrongIP, botToken);
  console.log('With wrong IP:', {
    allowed: resultWithWrongIP.allowed,
    reason: resultWithWrongIP.securityInfo.reason || 'No specific reason'
  });

  // Test with malformed body
  console.log('\nTesting with malformed JSON...');
  const requestWithBadJSON = {
    ...mockRequest,
    body: '{"invalid": json}'
  };
  const resultWithBadJSON = securityMiddleware(requestWithBadJSON, botToken);
  console.log('With malformed JSON:', {
    allowed: resultWithBadJSON.allowed,
    reason: resultWithBadJSON.securityInfo.reason || 'No specific reason'
  });

  console.log('\n3️⃣ Testing bypass mechanisms...');

  // Check if bypass is actually working
  if (process.env.TELEGRAM_BYPASS_SECURITY === "true") {
    console.log('✅ TELEGRAM_BYPASS_SECURITY is enabled');
    console.log('This should bypass most security checks');
  } else {
    console.log('❌ TELEGRAM_BYPASS_SECURITY is not enabled');
    console.log('Security checks will be enforced');
  }

  // Test the actual webhook endpoint
  console.log('\n4️⃣ Testing actual webhook endpoint...');
  try {
    const webhookResponse = await fetch('https://www.buddychip.app/api/telegram/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Telegram-Bot-Api-Secret-Token': process.env.TELEGRAM_WEBHOOK_SECRET || '',
      },
      body: mockRequest.body
    });

    console.log('Webhook response status:', webhookResponse.status);
    console.log('Webhook response headers:', Object.fromEntries(webhookResponse.headers.entries()));
    
    const responseText = await webhookResponse.text();
    console.log('Webhook response body:', responseText);

    if (webhookResponse.status === 403) {
      console.log('\n🚨 CONFIRMED: Webhook is returning 403 Forbidden');
      console.log('This matches the error we saw in the webhook info');
    }

  } catch (error) {
    console.log('❌ Failed to test webhook endpoint:', error);
  }

  console.log('\n💡 Recommendations:');
  if (!result.allowed) {
    console.log('1. Check security bypass settings');
    console.log('2. Verify webhook secret token');
    console.log('3. Check IP allowlist/blocklist');
    console.log('4. Review security middleware logic');
    console.log('5. Consider temporarily disabling strict security for debugging');
  } else {
    console.log('1. Security middleware seems to be working');
    console.log('2. The issue might be elsewhere in the webhook handler');
    console.log('3. Check for other middleware or rate limiting');
  }
}

// Run the test
testWebhookSecurity().catch(console.error);
