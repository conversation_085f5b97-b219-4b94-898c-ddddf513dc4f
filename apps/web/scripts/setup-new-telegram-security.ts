#!/usr/bin/env tsx

/**
 * Setup and Test New Telegram Security System
 * 
 * This script sets up and validates the new Telegram security system
 */

import { telegramSecurity } from '../src/lib/telegram-security-config';
import { telegramSecurityValidator } from '../src/lib/telegram-security-new';
import { telegramLogger } from '../src/lib/telegram-logger';

async function setupNewTelegramSecurity() {
  console.log('🔧 Setting up New Telegram Security System\n');

  try {
    // Step 1: Validate configuration
    console.log('1️⃣ Validating security configuration...');
    
    const config = telegramSecurity.getConfig();
    const isValid = telegramSecurity.isValid();
    const errors = telegramSecurity.getErrors();

    console.log('Configuration Status:');
    console.log(`- Environment: ${config.environment}`);
    console.log(`- Valid: ${isValid ? '✅' : '❌'}`);
    console.log(`- Bot Token: ${config.botToken ? '✅ Configured' : '❌ Missing'}`);
    console.log(`- Webhook Secret: ${config.webhookSecret ? '✅ Configured' : '❌ Missing'}`);
    
    console.log('\nSecurity Settings:');
    console.log(`- Webhook Secret Validation: ${config.enableWebhookSecretValidation ? '✅' : '❌'}`);
    console.log(`- IP Validation: ${config.enableIPValidation ? '✅' : '❌'}`);
    console.log(`- Rate Limiting: ${config.enableRateLimit ? '✅' : '❌'}`);
    console.log(`- Content Validation: ${config.enableContentValidation ? '✅' : '❌'}`);
    console.log(`- Development Bypass: ${config.allowBypassInDevelopment ? '✅' : '❌'}`);

    if (!isValid) {
      console.log('\n❌ Configuration Errors:');
      errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }

    // Step 2: Test security validation
    console.log('\n2️⃣ Testing security validation...');

    // Test valid Telegram request
    const validRequest = {
      ip: '***************', // Telegram IP
      headers: {
        'content-type': 'application/json',
        'user-agent': 'TelegramBot (like TwitterBot)',
        'x-telegram-bot-api-secret-token': config.webhookSecret || 'test-secret',
      },
      body: JSON.stringify({
        update_id: 123456,
        message: {
          message_id: 1,
          from: {
            id: 904041730,
            is_bot: false,
            first_name: "Francesco",
            username: "francescooddo",
            language_code: "en"
          },
          chat: {
            id: 904041730,
            first_name: "Francesco",
            username: "francescooddo",
            type: "private"
          },
          date: Math.floor(Date.now() / 1000),
          text: "/start"
        }
      }),
      method: 'POST',
      url: 'https://www.buddychip.app/api/telegram/webhook-new'
    };

    console.log('Testing valid Telegram request...');
    const validResult = telegramSecurityValidator.validateRequest(validRequest);
    console.log(`Result: ${validResult.allowed ? '✅ ALLOWED' : '❌ BLOCKED'}`);
    console.log(`Status: ${validResult.status}`);
    console.log(`Message: ${validResult.message}`);
    
    if (validResult.details) {
      console.log('Checks:');
      Object.entries(validResult.details.checks).forEach(([check, passed]) => {
        console.log(`  - ${check}: ${passed ? '✅' : '❌'}`);
      });
      
      if (validResult.details.warnings.length > 0) {
        console.log('Warnings:');
        validResult.details.warnings.forEach((warning, index) => {
          console.log(`  ${index + 1}. ${warning}`);
        });
      }
    }

    // Test invalid request
    console.log('\nTesting invalid request (wrong IP)...');
    const invalidRequest = {
      ...validRequest,
      ip: '***********' // Non-Telegram IP
    };
    
    const invalidResult = telegramSecurityValidator.validateRequest(invalidRequest);
    console.log(`Result: ${invalidResult.allowed ? '✅ ALLOWED' : '❌ BLOCKED'}`);
    console.log(`Status: ${invalidResult.status}`);
    console.log(`Message: ${invalidResult.message}`);

    // Step 3: Test webhook endpoints
    console.log('\n3️⃣ Testing webhook endpoints...');

    // Test health check
    console.log('Testing health check endpoint...');
    try {
      const healthResponse = await fetch('https://www.buddychip.app/api/telegram/webhook-new?action=health');
      const healthData = await healthResponse.json();
      
      console.log(`Health Check Status: ${healthResponse.status}`);
      console.log(`Service Status: ${healthData.status}`);
      console.log(`Environment: ${healthData.environment}`);
      console.log(`Configuration Valid: ${healthData.configuration?.valid ? '✅' : '❌'}`);
      
      if (healthData.configuration?.errors?.length > 0) {
        console.log('Configuration Errors:');
        healthData.configuration.errors.forEach((error: string, index: number) => {
          console.log(`  ${index + 1}. ${error}`);
        });
      }
    } catch (error) {
      console.log('❌ Health check failed:', error);
    }

    // Step 4: Setup webhook
    console.log('\n4️⃣ Setting up webhook...');
    
    if (config.botToken) {
      try {
        // Get current webhook info
        const webhookInfoResponse = await fetch(`https://api.telegram.org/bot${config.botToken}/getWebhookInfo`);
        const webhookInfo = await webhookInfoResponse.json();
        
        console.log('Current webhook info:');
        console.log(`- URL: ${webhookInfo.result?.url || 'Not set'}`);
        console.log(`- Pending updates: ${webhookInfo.result?.pending_update_count || 0}`);
        console.log(`- Last error: ${webhookInfo.result?.last_error_message || 'None'}`);
        
        if (webhookInfo.result?.last_error_date) {
          const errorDate = new Date(webhookInfo.result.last_error_date * 1000);
          console.log(`- Last error date: ${errorDate.toISOString()}`);
        }

        // Set new webhook
        console.log('\nSetting new webhook...');
        const newWebhookUrl = 'https://www.buddychip.app/api/telegram/webhook-new';
        
        const setWebhookData: any = {
          url: newWebhookUrl,
          allowed_updates: ['message', 'callback_query']
        };

        if (config.webhookSecret) {
          setWebhookData.secret_token = config.webhookSecret;
        }

        const setWebhookResponse = await fetch(`https://api.telegram.org/bot${config.botToken}/setWebhook`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(setWebhookData)
        });

        const setWebhookResult = await setWebhookResponse.json();
        
        if (setWebhookResult.ok) {
          console.log('✅ Webhook set successfully!');
          console.log(`New webhook URL: ${newWebhookUrl}`);
        } else {
          console.log('❌ Failed to set webhook:', setWebhookResult.description);
        }

      } catch (error) {
        console.log('❌ Webhook setup failed:', error);
      }
    } else {
      console.log('❌ Cannot set webhook: Bot token not configured');
    }

    // Step 5: Environment setup instructions
    console.log('\n5️⃣ Environment Setup Instructions...');
    
    console.log('\nFor Production (Vercel):');
    console.log('1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables');
    console.log('2. Add the following variables:');
    console.log(`   - TELEGRAM_BOT_TOKEN: ${config.botToken || '[YOUR_BOT_TOKEN]'}`);
    console.log(`   - TELEGRAM_WEBHOOK_SECRET: ${config.webhookSecret || '[YOUR_WEBHOOK_SECRET]'}`);
    console.log('   - NODE_ENV: production');
    console.log('3. Redeploy your application');
    
    console.log('\nFor Development:');
    console.log('1. Ensure .env file contains:');
    console.log(`   TELEGRAM_BOT_TOKEN=${config.botToken || '[YOUR_BOT_TOKEN]'}`);
    console.log(`   TELEGRAM_WEBHOOK_SECRET=${config.webhookSecret || '[YOUR_WEBHOOK_SECRET]'}`);
    console.log('   NODE_ENV=development');
    console.log('2. Use ngrok or similar for local webhook testing');

    console.log('\n✅ New Telegram Security System Setup Complete!');
    
    // Summary
    console.log('\n📋 Summary:');
    console.log(`- Configuration: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
    console.log(`- Environment: ${config.environment}`);
    console.log(`- Security Level: ${config.environment === 'DEVELOPMENT' ? 'Relaxed' : 'Strict'}`);
    console.log(`- New Webhook: https://www.buddychip.app/api/telegram/webhook-new`);
    
    if (config.environment === 'DEVELOPMENT' && !isValid) {
      console.log('\n💡 Development Mode: The system will work with warnings even if configuration is incomplete');
    }

  } catch (error) {
    console.error('❌ Setup failed:', error);
    telegramLogger.error('New security system setup failed', {
      error: error instanceof Error ? error : new Error(String(error)),
      action: 'security_setup'
    });
  }
}

// Run the setup
setupNewTelegramSecurity().catch(console.error);
