#!/usr/bin/env tsx

/**
 * Test script for Telegram database integration
 * 
 * This script tests the database connection and Telegram-related operations
 */

import { prisma } from '../src/lib/db-utils';
import { telegramLogger } from '../src/lib/telegram-logger';

async function testTelegramDatabase() {
  console.log('🧪 Testing Telegram Database Integration\n');

  try {
    // Test 1: Basic database connection
    console.log('1️⃣ Testing database connection...');
    const result = await prisma.$queryRaw`SELECT NOW() as current_time`;
    console.log('✅ Database connected successfully:', result);

    // Test 2: Check Telegram tables exist
    console.log('\n2️⃣ Checking Telegram tables...');
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%telegram%'
      ORDER BY table_name
    `;
    console.log('✅ Telegram tables found:', tables);

    // Test 3: Count existing Telegram users
    console.log('\n3️⃣ Checking existing Telegram users...');
    const userCount = await prisma.telegramUser.count();
    console.log(`✅ Found ${userCount} Telegram users in database`);

    // Test 4: Get recent Telegram users
    if (userCount > 0) {
      console.log('\n4️⃣ Fetching recent Telegram users...');
      const recentUsers = await prisma.telegramUser.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          telegramId: true,
          username: true,
          firstName: true,
          userId: true,
          isActive: true,
          createdAt: true,
          lastActiveAt: true
        }
      });
      console.log('✅ Recent Telegram users:', recentUsers);

      // Check for unlinked users
      const unlinkedUsers = recentUsers.filter(user => !user.userId);
      if (unlinkedUsers.length > 0) {
        console.log(`⚠️ Found ${unlinkedUsers.length} unlinked Telegram users (no BuddyChip account connection)`);
      }
    }

    // Test 5: Check Telegram sessions
    console.log('\n5️⃣ Checking Telegram sessions...');
    const sessionCount = await prisma.telegramSession.count();
    console.log(`✅ Found ${sessionCount} Telegram sessions in database`);

    if (sessionCount > 0) {
      const activeSessions = await prisma.telegramSession.count({
        where: { isActive: true }
      });
      console.log(`✅ Active sessions: ${activeSessions}`);
    }

    // Test 6: Test creating a test Telegram user (and clean up)
    console.log('\n6️⃣ Testing Telegram user creation...');
    const testUser = await prisma.telegramUser.create({
      data: {
        telegramId: 'test_' + Date.now(),
        username: 'test_user',
        firstName: 'Test',
        lastName: 'User',
        languageCode: 'en'
      }
    });
    console.log('✅ Test user created:', testUser.id);

    // Clean up test user
    await prisma.telegramUser.delete({
      where: { id: testUser.id }
    });
    console.log('✅ Test user cleaned up');

    // Test 7: Test database logging
    console.log('\n7️⃣ Testing database operation logging...');
    telegramLogger.logDatabaseOperation('SELECT', 'telegram_users', true, 25);
    telegramLogger.logDatabaseOperation('INSERT', 'telegram_users', true, 45);
    console.log('✅ Database logging test completed');

    // Test 8: Check for any database constraints or issues
    console.log('\n8️⃣ Checking database constraints...');
    const constraints = await prisma.$queryRaw`
      SELECT 
        tc.constraint_name, 
        tc.table_name, 
        tc.constraint_type,
        kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu 
        ON tc.constraint_name = kcu.constraint_name
      WHERE tc.table_name IN ('telegram_users', 'telegram_sessions')
      ORDER BY tc.table_name, tc.constraint_type
    `;
    console.log('✅ Database constraints:', constraints);

    console.log('\n✅ All database tests completed successfully!');

  } catch (error) {
    console.error('❌ Database test failed:', error);
    telegramLogger.error('Database test failed', {
      error: error instanceof Error ? error : new Error(String(error)),
      action: 'database_test'
    });
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the test
testTelegramDatabase().catch(console.error);
