#!/usr/bin/env tsx

/**
 * Test script for Telegram logging system
 * 
 * This script tests the new structured logging system for the Telegram agent
 */

import { telegramLogger } from '../src/lib/telegram-logger';

async function testTelegramLogging() {
  console.log('🧪 Testing Telegram Logging System\n');

  // Test basic logging levels
  console.log('1️⃣ Testing basic logging levels:');
  telegramLogger.debug('This is a debug message');
  telegramLogger.info('This is an info message');
  telegramLogger.warn('This is a warning message');
  telegramLogger.error('This is an error message');

  console.log('\n2️⃣ Testing logging with context:');
  
  // Test webhook logging
  telegramLogger.logWebhookReceived(12345, 'message', 67890);
  
  // Test command logging
  telegramLogger.logCommandReceived('/start', 'user123', 67890, 'testuser');
  
  // Test message processing
  telegramLogger.logMessageProcessed(67890, 'user123', 150, true);
  telegramLogger.logMessageProcessed(67890, 'user123', 250, false);
  
  // Test AI response logging
  telegramLogger.logAIResponse('user123', 67890, 'gpt-4', 150);
  
  // Test security events
  telegramLogger.logSecurityEvent('SUSPICIOUS_ACTIVITY', '192.168.1.1', 67890, {
    reason: 'Too many requests',
    attempts: 5
  });
  
  // Test rate limiting
  telegramLogger.logRateLimitHit('user123', 67890, 'AI_CALLS', 100);
  
  // Test database operations
  telegramLogger.logDatabaseOperation('INSERT', 'telegram_users', true, 25);
  telegramLogger.logDatabaseOperation('UPDATE', 'telegram_users', false, 45);
  
  // Test Twitter integration
  telegramLogger.logTwitterIntegration(
    'user123', 
    67890, 
    'https://twitter.com/user/status/*********', 
    true
  );
  
  // Test account linking
  telegramLogger.logAccountLinking('telegram123', 'buddychip456', true);
  
  console.log('\n3️⃣ Testing performance timing:');
  const timer = telegramLogger.startTimer();
  
  // Simulate some work
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const elapsed = timer();
  telegramLogger.info('Operation completed', {
    processingTime: elapsed,
    action: 'test_operation'
  });
  
  console.log('\n4️⃣ Testing health check logging:');
  telegramLogger.logHealthCheck('healthy', {
    uptime: '5 minutes',
    memoryUsage: '45MB',
    activeConnections: 3
  });
  
  telegramLogger.logHealthCheck('unhealthy', {
    error: 'Database connection failed',
    lastSuccessfulCheck: '2 minutes ago'
  });
  
  console.log('\n5️⃣ Testing error logging with stack traces:');
  try {
    throw new Error('Test error for logging');
  } catch (error) {
    telegramLogger.error('Caught test error', {
      chatId: 67890,
      userId: 'user123',
      error: error as Error,
      action: 'test_error_handling'
    });
  }
  
  console.log('\n✅ Telegram logging test completed!');
  console.log('\n📋 Log levels available:');
  console.log('   - DEBUG: Detailed debugging information');
  console.log('   - INFO: General information messages');
  console.log('   - WARN: Warning messages');
  console.log('   - ERROR: Error messages with stack traces');
  console.log('\n🔧 Set TELEGRAM_LOG_LEVEL environment variable to control verbosity');
  console.log('   Example: TELEGRAM_LOG_LEVEL=INFO (default), DEBUG, WARN, ERROR');
}

// Run the test
testTelegramLogging().catch(console.error);
