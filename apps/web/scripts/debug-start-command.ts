#!/usr/bin/env tsx

/**
 * Debug script for /start command issue
 * 
 * This script simulates and debugs the /start command for user 904041730
 */

import { prisma } from '../src/lib/db-utils';
import { telegramLogger } from '../src/lib/telegram-logger';

async function debugStartCommand() {
  console.log('🔍 Debugging /start command for user 904041730 (francescooddo)\n');

  try {
    // Check user record
    console.log('1️⃣ Checking user record...');
    const user = await prisma.telegramUser.findUnique({
      where: { telegramId: '904041730' },
      include: {
        sessions: {
          orderBy: { createdAt: 'desc' },
          take: 3
        }
      }
    });

    if (!user) {
      console.log('❌ User not found in database');
      return;
    }

    console.log('✅ User found:', {
      id: user.id,
      telegramId: user.telegramId,
      username: user.username,
      firstName: user.firstName,
      isActive: user.isActive,
      isBlocked: user.isBlocked,
      lastActiveAt: user.lastActiveAt,
      sessionsCount: user.sessions.length
    });

    // Check if user is blocked
    if (user.isBlocked) {
      console.log('⚠️ User is marked as blocked');
    }

    if (!user.isActive) {
      console.log('⚠️ User is marked as inactive');
    }

    // Check recent sessions
    console.log('\n2️⃣ Checking recent sessions...');
    if (user.sessions.length > 0) {
      console.log('Recent sessions:');
      user.sessions.forEach((session, index) => {
        console.log(`  ${index + 1}. Session ${session.id}:`);
        console.log(`     - Active: ${session.isActive}`);
        console.log(`     - Created: ${session.createdAt}`);
        console.log(`     - Updated: ${session.updatedAt}`);
        console.log(`     - Expires: ${session.expiresAt}`);
        if (session.context) {
          const context = session.context as any;
          console.log(`     - Messages: ${context.messageHistory?.length || 0}`);
          console.log(`     - Last interaction: ${context.lastInteraction || 'None'}`);
        }
      });
    } else {
      console.log('No sessions found');
    }

    // Test webhook endpoint health
    console.log('\n3️⃣ Testing webhook endpoint...');
    try {
      const response = await fetch('https://www.buddychip.app/api/telegram/webhook?action=health');
      const health = await response.json();
      console.log('✅ Webhook health:', health);
    } catch (error) {
      console.log('❌ Webhook health check failed:', error);
    }

    // Check bot token validity
    console.log('\n4️⃣ Testing bot token...');
    const botToken = process.env.TELEGRAM_BOT_TOKEN;
    if (!botToken) {
      console.log('❌ TELEGRAM_BOT_TOKEN not found');
      return;
    }

    try {
      const botResponse = await fetch(`https://api.telegram.org/bot${botToken}/getMe`);
      const botInfo = await botResponse.json();
      console.log('✅ Bot info:', botInfo);
    } catch (error) {
      console.log('❌ Bot token test failed:', error);
    }

    // Check webhook status
    console.log('\n5️⃣ Checking webhook status...');
    try {
      const webhookResponse = await fetch(`https://api.telegram.org/bot${botToken}/getWebhookInfo`);
      const webhookInfo = await webhookResponse.json();
      console.log('✅ Webhook info:', webhookInfo);
      
      if (webhookInfo.result?.last_error_message) {
        console.log('⚠️ Last webhook error:', webhookInfo.result.last_error_message);
        console.log('⚠️ Last error date:', new Date(webhookInfo.result.last_error_date * 1000));
      }
    } catch (error) {
      console.log('❌ Webhook status check failed:', error);
    }

    // Simulate /start command processing
    console.log('\n6️⃣ Simulating /start command processing...');
    
    // This is what should happen when /start is received:
    console.log('Expected flow:');
    console.log('1. Webhook receives message with /start command');
    console.log('2. Bot processes update and routes to handleStartCommand');
    console.log('3. getOrCreateTelegramUser finds existing user');
    console.log('4. Bot sends welcome message with inline keyboard');
    console.log('5. User receives message in Telegram');

    // Check if there are any recent updates we can see
    console.log('\n7️⃣ Checking for recent activity...');
    const recentActivity = await prisma.telegramUser.findMany({
      where: {
        lastActiveAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      orderBy: { lastActiveAt: 'desc' },
      take: 5
    });

    console.log('Recent activity (last 24h):');
    recentActivity.forEach(u => {
      console.log(`- ${u.username || u.firstName} (${u.telegramId}): ${u.lastActiveAt}`);
    });

    // Test sending a message to the user (if we can)
    console.log('\n8️⃣ Testing message sending...');
    console.log('⚠️ Skipping actual message send to avoid spam');
    console.log('To test manually:');
    console.log('1. Send /start to @Benji_BuddyChip_Bot');
    console.log('2. Check webhook logs for incoming update');
    console.log('3. Check if bot responds with welcome message');

    // Check environment variables
    console.log('\n9️⃣ Checking environment configuration...');
    console.log('Environment check:');
    console.log(`- TELEGRAM_BOT_TOKEN: ${botToken ? 'Set' : 'Missing'}`);
    console.log(`- NEXT_PUBLIC_APP_URL: ${process.env.NEXT_PUBLIC_APP_URL || 'Missing'}`);
    console.log(`- TELEGRAM_WEBHOOK_SECRET: ${process.env.TELEGRAM_WEBHOOK_SECRET ? 'Set' : 'Missing'}`);
    console.log(`- TELEGRAM_BYPASS_SECURITY: ${process.env.TELEGRAM_BYPASS_SECURITY || 'Not set'}`);
    console.log(`- NODE_ENV: ${process.env.NODE_ENV || 'Not set'}`);

    console.log('\n✅ Debug analysis completed!');
    
    // Recommendations
    console.log('\n💡 Troubleshooting steps:');
    console.log('1. Try sending /start again to @Benji_BuddyChip_Bot');
    console.log('2. Check if you receive any response (even error message)');
    console.log('3. Try other commands like /help');
    console.log('4. Check if bot is online and responding to other users');
    console.log('5. Verify webhook is receiving updates');

  } catch (error) {
    console.error('❌ Debug failed:', error);
    telegramLogger.error('Start command debug failed', {
      error: error instanceof Error ? error : new Error(String(error)),
      action: 'debug_start_command'
    });
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the debug
debugStartCommand().catch(console.error);
