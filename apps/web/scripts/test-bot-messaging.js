#!/usr/bin/env node

/**
 * Direct Telegram Bot Messaging Test
 * 
 * Tests direct message sending capabilities without requiring user interaction
 */

const https = require('https');

const BOT_TOKEN = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
const TELEGRAM_API_BASE = `https://api.telegram.org/bot${BOT_TOKEN}`;

// Test chat ID - we'll use the bot's own ID for testing (should fail gracefully)
const TEST_CHAT_ID = '7652990262'; // <PERSON><PERSON>'s own ID

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(color, message, prefix = '') {
  console.log(`${colors[color]}${prefix}${message}${colors.reset}`);
}

function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const url = `${TELEGRAM_API_BASE}/${endpoint}`;
    const postData = options.data ? JSON.stringify(options.data) : null;
    
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BuddyChip-Bot-Test/1.0',
      }
    };

    if (postData) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: parsed,
            headers: res.headers
          });
        } catch (e) {
          reject(new Error(`Failed to parse JSON: ${data}`));
        }
      });
    });

    req.on('error', reject);
    
    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

async function testBotMessaging() {
  log('cyan', '\n🤖 TELEGRAM BOT MESSAGING TEST\n');
  
  // Test 1: Check recent updates to find a valid chat ID
  log('blue', 'ℹ️  Checking for recent updates...');
  
  try {
    const updatesResponse = await makeRequest('getUpdates', {
      data: { limit: 10 }
    });
    
    if (updatesResponse.data.ok && updatesResponse.data.result.length > 0) {
      log('green', '✅ Found recent updates');
      
      const validChatIds = new Set();
      updatesResponse.data.result.forEach(update => {
        if (update.message && update.message.chat) {
          validChatIds.add(update.message.chat.id);
        }
      });
      
      if (validChatIds.size > 0) {
        log('green', `✅ Found ${validChatIds.size} valid chat ID(s)`);
        
        // Test sending to the first valid chat
        const testChatId = Array.from(validChatIds)[0];
        await testSendMessage(testChatId);
        
      } else {
        log('yellow', '⚠️  No valid chat IDs found in recent updates');
      }
      
    } else {
      log('yellow', '⚠️  No recent updates found');
    }
  } catch (err) {
    log('red', `❌ Error checking updates: ${err.message}`);
  }
  
  // Test 2: Validate message sending API endpoint
  log('blue', '\nℹ️  Testing sendMessage API endpoint validation...');
  
  try {
    const testResponse = await makeRequest('sendMessage', {
      data: {
        chat_id: 'invalid_chat_id',
        text: 'Test message'
      }
    });
    
    if (!testResponse.data.ok) {
      log('green', '✅ sendMessage API correctly validates chat IDs');
      log('blue', `   Expected error: ${testResponse.data.description}`);
    } else {
      log('yellow', '⚠️  Unexpected success with invalid chat ID');
    }
    
  } catch (err) {
    log('red', `❌ Network error testing sendMessage: ${err.message}`);
  }
  
  // Test 3: Test other messaging methods
  await testOtherMethods();
}

async function testSendMessage(chatId) {
  log('blue', `\nℹ️  Testing message sending to chat ID: ${chatId}`);
  
  const testMessage = `🤖 *BuddyChip Bot Test Message*

✅ This is an automated test of the Telegram bot functionality
🕒 Timestamp: ${new Date().toISOString()}
🔧 All systems operational

If you received this message, the bot is working correctly!`;

  try {
    const response = await makeRequest('sendMessage', {
      data: {
        chat_id: chatId,
        text: testMessage,
        parse_mode: 'Markdown',
        disable_notification: true // Reduce notification spam
      }
    });
    
    if (response.data.ok) {
      log('green', '✅ Test message sent successfully!');
      log('green', `   Message ID: ${response.data.result.message_id}`);
      log('green', `   Chat ID: ${response.data.result.chat.id}`);
      log('green', `   Date: ${new Date(response.data.result.date * 1000).toISOString()}`);
      return true;
    } else {
      log('red', `❌ Failed to send message: ${response.data.description}`);
      return false;
    }
    
  } catch (err) {
    log('red', `❌ Error sending message: ${err.message}`);
    return false;
  }
}

async function testOtherMethods() {
  log('blue', '\nℹ️  Testing other bot methods...');
  
  // Test sendChatAction
  try {
    const actionResponse = await makeRequest('sendChatAction', {
      data: {
        chat_id: 'invalid_chat',
        action: 'typing'
      }
    });
    
    if (!actionResponse.data.ok) {
      log('green', '✅ sendChatAction correctly validates inputs');
    }
  } catch (err) {
    log('yellow', '⚠️  Error testing sendChatAction (expected)');
  }
  
  // Test getChat with bot's own ID
  try {
    const chatResponse = await makeRequest('getChat', {
      data: {
        chat_id: BOT_TOKEN.split(':')[0] // Bot's ID
      }
    });
    
    if (chatResponse.data.ok) {
      log('green', '✅ getChat works with bot ID');
      log('blue', `   Bot chat type: ${chatResponse.data.result.type}`);
    } else {
      log('yellow', `⚠️  getChat with bot ID failed: ${chatResponse.data.description}`);
    }
  } catch (err) {
    log('yellow', `⚠️  Error testing getChat: ${err.message}`);
  }
  
  // Test file upload capability
  log('blue', '\nℹ️  Testing file upload capability...');
  
  try {
    const photoResponse = await makeRequest('sendPhoto', {
      data: {
        chat_id: 'invalid_chat',
        photo: 'invalid_url'
      }
    });
    
    if (!photoResponse.data.ok) {
      log('green', '✅ sendPhoto correctly validates inputs');
    }
  } catch (err) {
    log('yellow', '⚠️  Error testing sendPhoto (expected)');
  }
}

async function testWebhookCompatibility() {
  log('blue', '\nℹ️  Testing webhook compatibility...');
  
  try {
    const webhookInfo = await makeRequest('getWebhookInfo');
    
    if (webhookInfo.data.ok) {
      const info = webhookInfo.data.result;
      
      if (info.url) {
        log('green', '✅ Webhook is configured');
        log('blue', `   Webhook URL: ${info.url}`);
        log('blue', `   Pending updates: ${info.pending_update_count}`);
        
        if (info.last_error_date) {
          log('yellow', '⚠️  Recent webhook errors detected');
          log('yellow', `   Last error: ${info.last_error_message}`);
        } else {
          log('green', '✅ No webhook errors');
        }
      } else {
        log('yellow', '⚠️  No webhook configured (using polling)');
      }
    }
  } catch (err) {
    log('red', `❌ Error checking webhook: ${err.message}`);
  }
}

async function generateTestReport() {
  log('cyan', '\n📋 BOT MESSAGING TEST SUMMARY');
  log('cyan', '================================\n');
  
  // Get bot info again for final report
  try {
    const botInfo = await makeRequest('getMe');
    
    if (botInfo.data.ok) {
      const bot = botInfo.data.result;
      
      log('green', '✅ Bot is fully operational');
      log('blue', `   Username: @${bot.username}`);
      log('blue', `   Name: ${bot.first_name}`);
      log('blue', `   ID: ${bot.id}`);
      log('blue', `   Can join groups: ${bot.can_join_groups ? 'Yes' : 'No'}`);
      log('blue', `   Profile link: https://t.me/${bot.username}`);
      
      console.log('\n🔧 Next Steps:');
      console.log('   1. Start a conversation with the bot to test messaging');
      console.log('   2. Send /start command to test command handling');
      console.log('   3. Send a Twitter URL to test AI functionality');
      console.log('   4. Test webhook endpoint integration');
      
    } else {
      log('red', '❌ Bot is not responding properly');
    }
    
  } catch (err) {
    log('red', `❌ Error generating report: ${err.message}`);
  }
}

async function main() {
  console.log(`${colors.cyan}╔════════════════════════════════════════╗`);
  console.log(`║       TELEGRAM BOT MESSAGING TEST     ║`);
  console.log(`╚════════════════════════════════════════╝${colors.reset}\n`);
  
  await testBotMessaging();
  await testWebhookCompatibility();
  await generateTestReport();
  
  log('green', '\n✅ Messaging test completed!\n');
}

if (require.main === module) {
  main().catch(err => {
    log('red', `❌ Fatal error: ${err.message}`);
    process.exit(1);
  });
}