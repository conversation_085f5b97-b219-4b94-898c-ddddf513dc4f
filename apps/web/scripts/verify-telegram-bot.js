#!/usr/bin/env node

/**
 * Comprehensive Telegram Bot Verification Script
 * 
 * Tests the bot token and verifies all basic functionality including:
 * - Bot token validation
 * - getMe endpoint
 * - Bot permissions and settings
 * - Webhook capabilities
 * - Message sending
 * - Command responses
 */

const https = require('https');
const readline = require('readline');

const BOT_TOKEN = '7652990262:AAEgH3GfhmPatsnxEPzwyUdaDvm_25ZfvTM';
const TELEGRAM_API_BASE = `https://api.telegram.org/bot${BOT_TOKEN}`;

// Color codes for better output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(color, message, prefix = '') {
  console.log(`${colors[color]}${prefix}${message}${colors.reset}`);
}

function success(message) {
  log('green', message, '✅ ');
}

function error(message) {
  log('red', message, '❌ ');
}

function warning(message) {
  log('yellow', message, '⚠️  ');
}

function info(message) {
  log('blue', message, 'ℹ️  ');
}

function section(title) {
  log('cyan', `\n${'='.repeat(50)}`, '');
  log('cyan', title.toUpperCase(), '📋 ');
  log('cyan', `${'='.repeat(50)}`, '');
}

/**
 * Make HTTP request to Telegram API
 */
function makeRequest(endpoint, options = {}) {
  return new Promise((resolve, reject) => {
    const url = `${TELEGRAM_API_BASE}/${endpoint}`;
    const postData = options.data ? JSON.stringify(options.data) : null;
    
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'BuddyChip-Bot-Verification/1.0',
        ...(options.headers || {})
      }
    };

    if (postData) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(postData);
    }

    const req = https.request(url, requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: parsed,
            headers: res.headers
          });
        } catch (e) {
          reject(new Error(`Failed to parse JSON: ${data}`));
        }
      });
    });

    req.on('error', reject);
    
    if (postData) {
      req.write(postData);
    }
    
    req.end();
  });
}

/**
 * Test 1: Validate Bot Token Format
 */
function validateTokenFormat() {
  section('Bot Token Format Validation');
  
  const tokenPattern = /^\d+:[A-Za-z0-9_-]{35}$/;
  
  if (!BOT_TOKEN) {
    error('Bot token is not set');
    return false;
  }
  
  if (!tokenPattern.test(BOT_TOKEN)) {
    error('Bot token format is invalid');
    info('Expected format: XXXXXXXXX:XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX');
    return false;
  }
  
  success('Bot token format is valid');
  
  const [botId] = BOT_TOKEN.split(':');
  info(`Bot ID: ${botId}`);
  
  return true;
}

/**
 * Test 2: Test getMe Endpoint
 */
async function testGetMe() {
  section('Bot Information (getMe)');
  
  try {
    const response = await makeRequest('getMe');
    
    if (response.statusCode !== 200) {
      error(`HTTP ${response.statusCode}: Failed to get bot info`);
      return null;
    }
    
    if (!response.data.ok) {
      error(`API Error: ${response.data.description || 'Unknown error'}`);
      return null;
    }
    
    const bot = response.data.result;
    
    success('Bot information retrieved successfully');
    
    console.log('\n📊 Bot Details:');
    console.log(`   ID: ${bot.id}`);
    console.log(`   Name: ${bot.first_name} ${bot.last_name || ''}`);
    console.log(`   Username: @${bot.username}`);
    console.log(`   Is Bot: ${bot.is_bot ? 'Yes' : 'No'}`);
    console.log(`   Can Join Groups: ${bot.can_join_groups ? 'Yes' : 'No'}`);
    console.log(`   Can Read Group Messages: ${bot.can_read_all_group_messages ? 'Yes' : 'No'}`);
    console.log(`   Supports Inline Queries: ${bot.supports_inline_queries ? 'Yes' : 'No'}`);
    console.log(`   Has Main Web App: ${bot.has_main_web_app ? 'Yes' : 'No'}`);
    
    if (bot.description) {
      console.log(`   Description: ${bot.description}`);
    }
    
    if (bot.short_description) {
      console.log(`   Short Description: ${bot.short_description}`);
    }
    
    return bot;
  } catch (err) {
    error(`Network error: ${err.message}`);
    return null;
  }
}

/**
 * Test 3: Check Bot Commands
 */
async function checkBotCommands() {
  section('Bot Commands Configuration');
  
  try {
    const response = await makeRequest('getMyCommands');
    
    if (response.statusCode !== 200) {
      error(`HTTP ${response.statusCode}: Failed to get commands`);
      return;
    }
    
    if (!response.data.ok) {
      error(`API Error: ${response.data.description || 'Unknown error'}`);
      return;
    }
    
    const commands = response.data.result;
    
    if (commands.length === 0) {
      warning('No commands are configured for this bot');
      info('Consider setting up commands with /setcommands in @BotFather');
    } else {
      success(`Found ${commands.length} configured commands:`);
      commands.forEach(cmd => {
        console.log(`   /${cmd.command} - ${cmd.description}`);
      });
    }
    
  } catch (err) {
    error(`Error checking commands: ${err.message}`);
  }
}

/**
 * Test 4: Check Webhook Status
 */
async function checkWebhookStatus() {
  section('Webhook Configuration');
  
  try {
    const response = await makeRequest('getWebhookInfo');
    
    if (response.statusCode !== 200) {
      error(`HTTP ${response.statusCode}: Failed to get webhook info`);
      return;
    }
    
    if (!response.data.ok) {
      error(`API Error: ${response.data.description || 'Unknown error'}`);
      return;
    }
    
    const webhook = response.data.result;
    
    console.log('\n🔗 Webhook Status:');
    console.log(`   URL: ${webhook.url || 'Not set (using long polling)'}`);
    console.log(`   Has Custom Certificate: ${webhook.has_custom_certificate ? 'Yes' : 'No'}`);
    console.log(`   Pending Updates: ${webhook.pending_update_count}`);
    console.log(`   Max Connections: ${webhook.max_connections || 'Default (40)'}`);
    
    if (webhook.allowed_updates && webhook.allowed_updates.length > 0) {
      console.log(`   Allowed Updates: ${webhook.allowed_updates.join(', ')}`);
    }
    
    if (webhook.last_error_date) {
      const errorDate = new Date(webhook.last_error_date * 1000);
      warning(`Last Error: ${webhook.last_error_message} (${errorDate.toISOString()})`);
    } else {
      success('No webhook errors reported');
    }
    
    if (webhook.url) {
      success('Webhook is configured');
    } else {
      info('Bot is using long polling (no webhook set)');
    }
    
  } catch (err) {
    error(`Error checking webhook: ${err.message}`);
  }
}

/**
 * Test 5: Test Bot Permissions
 */
async function testBotPermissions() {
  section('Bot Permissions Test');
  
  info('Testing basic API permissions...');
  
  // Test getting updates (this should work for any bot)
  try {
    const response = await makeRequest('getUpdates', {
      data: { limit: 1 }
    });
    
    if (response.statusCode === 200 && response.data.ok) {
      success('Bot can access getUpdates endpoint');
    } else {
      warning('Bot may have limited access to getUpdates');
    }
  } catch (err) {
    error(`Error testing permissions: ${err.message}`);
  }
  
  // Test if bot can get its own chat member status (requires additional permissions)
  try {
    const meResponse = await makeRequest('getMe');
    if (meResponse.data.ok) {
      const botId = meResponse.data.result.id;
      
      // This will fail gracefully if bot doesn't have permission
      const memberResponse = await makeRequest('getChatMember', {
        data: {
          chat_id: botId,
          user_id: botId
        }
      });
      
      if (memberResponse.data.ok) {
        success('Bot has full API access');
      }
    }
  } catch (err) {
    // This is expected to fail for most bots, so we don't report it as an error
  }
}

/**
 * Test 6: Interactive Message Test
 */
async function interactiveMessageTest() {
  section('Interactive Message Test');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    info('To test message sending, we need a chat ID.');
    console.log('\nOptions:');
    console.log('1. Send a message to the bot from Telegram and check recent updates');
    console.log('2. Skip interactive test');
    
    rl.question('\nChoose option (1 or 2): ', async (choice) => {
      if (choice === '1') {
        await testMessageSending(rl);
      } else {
        info('Skipping interactive message test');
      }
      rl.close();
      resolve();
    });
  });
}

/**
 * Test message sending functionality
 */
async function testMessageSending(rl) {
  try {
    info('Checking recent updates for chat IDs...');
    
    const updatesResponse = await makeRequest('getUpdates', {
      data: { limit: 5 }
    });
    
    if (!updatesResponse.data.ok) {
      error('Could not get recent updates');
      return;
    }
    
    const updates = updatesResponse.data.result;
    
    if (updates.length === 0) {
      warning('No recent messages found. Send a message to the bot first.');
      return;
    }
    
    console.log('\nRecent chat IDs:');
    const chatIds = new Set();
    updates.forEach((update, index) => {
      if (update.message && update.message.chat) {
        const chatId = update.message.chat.id;
        chatIds.add(chatId);
        const chatType = update.message.chat.type;
        const chatTitle = update.message.chat.title || 
                         update.message.chat.first_name || 
                         'Unknown';
        console.log(`   ${index + 1}. Chat ID: ${chatId} (${chatType}: ${chatTitle})`);
      }
    });
    
    if (chatIds.size === 0) {
      warning('No valid chat IDs found in recent updates');
      return;
    }
    
    return new Promise((resolve) => {
      rl.question('\nEnter a chat ID to test message sending (or "skip"): ', async (input) => {
        if (input.toLowerCase() === 'skip') {
          info('Skipping message test');
          resolve();
          return;
        }
        
        const chatId = parseInt(input);
        if (isNaN(chatId)) {
          error('Invalid chat ID');
          resolve();
          return;
        }
        
        info(`Sending test message to chat ${chatId}...`);
        
        try {
          const testMessage = `🤖 *BuddyChip Bot Verification Test*

✅ Bot token is working correctly
🕒 Test time: ${new Date().toISOString()}
🔧 Verification script completed successfully

This is an automated test message. Your bot is functioning properly!`;

          const sendResponse = await makeRequest('sendMessage', {
            data: {
              chat_id: chatId,
              text: testMessage,
              parse_mode: 'Markdown'
            }
          });
          
          if (sendResponse.data.ok) {
            success('Test message sent successfully!');
            console.log(`   Message ID: ${sendResponse.data.result.message_id}`);
          } else {
            error(`Failed to send message: ${sendResponse.data.description}`);
          }
          
        } catch (err) {
          error(`Error sending message: ${err.message}`);
        }
        
        resolve();
      });
    });
    
  } catch (err) {
    error(`Error in message test: ${err.message}`);
  }
}

/**
 * Test 7: Rate Limiting Test
 */
async function testRateLimiting() {
  section('Rate Limiting Test');
  
  info('Testing API rate limits with multiple rapid requests...');
  
  const promises = [];
  for (let i = 0; i < 5; i++) {
    promises.push(makeRequest('getMe'));
  }
  
  try {
    const results = await Promise.all(promises);
    const successCount = results.filter(r => r.statusCode === 200).length;
    
    if (successCount === 5) {
      success('All rapid requests succeeded - no rate limiting detected');
    } else {
      warning(`${successCount}/5 requests succeeded - possible rate limiting`);
    }
    
  } catch (err) {
    error(`Rate limiting test failed: ${err.message}`);
  }
}

/**
 * Generate Bot Health Report
 */
function generateHealthReport(botInfo) {
  section('Bot Health Summary');
  
  console.log('\n📋 Overall Status:');
  
  if (botInfo) {
    success('Bot is operational and responding');
    success(`Bot username: @${botInfo.username}`);
    success(`Bot ID: ${botInfo.id}`);
    
    if (botInfo.can_join_groups) {
      success('Bot can join groups');
    } else {
      warning('Bot cannot join groups');
    }
    
    if (botInfo.can_read_all_group_messages) {
      info('Bot can read all group messages');
    } else {
      info('Bot reads only messages addressed to it');
    }
    
  } else {
    error('Bot is not responding to API calls');
    error('Check token validity and network connectivity');
  }
  
  console.log('\n🔧 Recommendations:');
  console.log('   1. Set up bot commands via @BotFather for better UX');
  console.log('   2. Configure webhook for production deployment');
  console.log('   3. Test message sending with actual users');
  console.log('   4. Monitor rate limits in production');
  console.log('   5. Set up error handling and logging');
  
  console.log('\n🌐 Useful Links:');
  console.log(`   Bot Profile: https://t.me/${botInfo?.username || 'your_bot'}`);
  console.log('   Telegram Bot API: https://core.telegram.org/bots/api');
  console.log('   BotFather: https://t.me/BotFather');
}

/**
 * Main execution function
 */
async function main() {
  console.log(`${colors.cyan}${colors.bright}`);
  console.log('╔══════════════════════════════════════════════════════════╗');
  console.log('║                 TELEGRAM BOT VERIFICATION                ║');
  console.log('║                     BuddyChip Project                    ║');
  console.log('╚══════════════════════════════════════════════════════════╝');
  console.log(colors.reset);
  
  info(`Testing bot token: ${BOT_TOKEN.substring(0, 10)}...`);
  info(`Test started at: ${new Date().toISOString()}`);
  
  // Run all tests
  const isValidFormat = validateTokenFormat();
  if (!isValidFormat) {
    process.exit(1);
  }
  
  const botInfo = await testGetMe();
  await checkBotCommands();
  await checkWebhookStatus();
  await testBotPermissions();
  await testRateLimiting();
  await interactiveMessageTest();
  
  generateHealthReport(botInfo);
  
  console.log(`\n${colors.green}${colors.bright}Verification completed!${colors.reset}`);
  
  if (botInfo) {
    process.exit(0);
  } else {
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('unhandledRejection', (reason, promise) => {
  error(`Unhandled Rejection at: ${promise}, reason: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  error(`Uncaught Exception: ${err.message}`);
  process.exit(1);
});

// Run the verification
if (require.main === module) {
  main().catch(err => {
    error(`Fatal error: ${err.message}`);
    process.exit(1);
  });
}

module.exports = {
  makeRequest,
  validateTokenFormat,
  testGetMe,
  BOT_TOKEN,
  TELEGRAM_API_BASE
};