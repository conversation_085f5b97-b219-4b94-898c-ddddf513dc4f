#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to generate favicon files from Logo.svg
 * Creates various sizes needed for different devices and browsers
 */

const fs = require('fs')
const path = require('path')

const sourceFile = path.join(__dirname, '../apps/web/public/Logo.svg')
const targetDir = path.join(__dirname, '../apps/web/public')

// Check if Logo.svg exists
if (!fs.existsSync(sourceFile)) {
  console.error('❌ Logo.svg not found at:', sourceFile)
  process.exit(1)
}

console.log('🎨 Generating favicon files from Logo.svg...')

// For now, we'll copy the SVG as favicon.svg (modern browsers support SVG favicons)
const svgContent = fs.readFileSync(sourceFile, 'utf8')

// Create favicon.svg (modern browsers)
fs.writeFileSync(path.join(targetDir, 'favicon.svg'), svgContent)
console.log('✅ Created favicon.svg')

// Create a simplified ICO version notice
console.log(`
📝 Favicon files generated:
   ✅ favicon.svg (modern browsers)

🚀 For production, consider generating additional formats:
   - favicon.ico (legacy browsers)
   - apple-touch-icon.png (iOS)
   - favicon-16x16.png, favicon-32x32.png (various sizes)

You can use online tools like:
- https://realfavicongenerator.net/
- https://favicon.io/

Simply upload your Logo.svg and download the generated files.
`)

console.log('🎉 Favicon generation complete!')